#!/usr/bin/env python3
"""
Fix the root cause: Session ID mismatch - point to session with actual data
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_session_id_mismatch_root_cause():
    """Fix the root cause: Session ID mismatch"""
    print("🔧 FIXING ROOT CAUSE: SESSION ID MISMATCH")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Find the session with the most extracted data
        print("\n1. 🔍 FINDING SESSION WITH ACTUAL DATA:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as count
            FROM extracted_data 
            GROUP BY session_id 
            ORDER BY count DESC
        """)
        
        sessions_with_data = cursor.fetchall()
        
        if sessions_with_data:
            print("   Sessions with extracted data:")
            for session_id, count in sessions_with_data:
                print(f"     {session_id}: {count} records")
            
            # Get the session with the most data
            best_session = sessions_with_data[0][0]
            best_count = sessions_with_data[0][1]
            
            print(f"\n   ✅ Best session: {best_session} ({best_count} records)")
        else:
            print("   ❌ No sessions with extracted data found")
            return
        
        # 2. Check what data exists for this session
        print("\n2. 📊 CHECKING DATA FOR BEST SESSION:")
        
        data_tables = [
            ('extracted_data', 'session_id'),
            ('comparison_results', 'session_id'),
            ('pre_reporting_results', 'session_id')
        ]
        
        session_data = {}
        for table_name, session_column in data_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {session_column} = ?", (best_session,))
            count = cursor.fetchone()[0]
            session_data[table_name] = count
            print(f"   {table_name}: {count} records")
        
        # 3. Update current session to point to the session with data
        print("\n3. 🔄 UPDATING CURRENT SESSION POINTER:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_session_manager
            
            session_manager = get_session_manager()
            
            # Update current session
            session_manager.set_current_session(best_session, f"Data_Session_{best_session[-8:]}")
            
            print(f"   ✅ Updated current session to: {best_session}")
            
            # Verify the update
            from core.session_manager import get_current_session_id
            current_session = get_current_session_id()
            print(f"   ✅ Verified current session: {current_session}")
            
            if current_session == best_session:
                print("   ✅ Session update successful!")
            else:
                print(f"   ❌ Session update failed: {current_session} != {best_session}")
                return
        
        except Exception as e:
            print(f"   ❌ Session update failed: {e}")
            return
        
        # 4. Update session phases based on available data
        print("\n4. 📊 UPDATING SESSION PHASES:")
        
        phases_to_update = [
            ('EXTRACTION', session_data['extracted_data']),
            ('COMPARISON', session_data['comparison_results']),
            ('PRE_REPORTING', session_data['pre_reporting_results'])
        ]
        
        for phase_name, data_count in phases_to_update:
            if data_count > 0:
                session_manager.update_phase_status(phase_name, 'COMPLETED', data_count)
                print(f"   ✅ {phase_name}: COMPLETED ({data_count} records)")
            else:
                session_manager.update_phase_status(phase_name, 'NOT_STARTED', 0)
                print(f"   ⏳ {phase_name}: NOT_STARTED (0 records)")
        
        # 5. Test data availability with corrected session
        print("\n5. 🧪 TESTING DATA AVAILABILITY WITH CORRECTED SESSION:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            
            # Test extracted data loading
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                print(f"   ✅ Extracted data loading: {len(current_data)} current, {len(previous_data)} previous")
                
                # If we have extracted data but no comparison results, generate them
                if session_data['comparison_results'] == 0:
                    print("   🔄 Generating comparison results...")
                    
                    comparison_results = manager._compare_payroll_data(current_data, previous_data)
                    
                    if comparison_results:
                        manager._store_comparison_results(comparison_results)
                        
                        # Verify storage
                        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (best_session,))
                        new_comparison_count = cursor.fetchone()[0]
                        
                        print(f"   ✅ Generated {new_comparison_count} comparison results")
                        
                        # Update phase status
                        session_manager.update_phase_status('COMPARISON', 'COMPLETED', new_comparison_count)
                        
                        session_data['comparison_results'] = new_comparison_count
                    else:
                        print("   ❌ Failed to generate comparison results")
                else:
                    print("   ✅ Comparison results already exist")
            else:
                print("   ❌ Could not load extracted data")
                return
        
        except Exception as e:
            print(f"   ❌ Data availability test failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 6. Generate pre-reporting results if needed
        print("\n6. 🔄 ENSURING PRE-REPORTING DATA:")
        
        if session_data['pre_reporting_results'] == 0 and session_data['comparison_results'] > 0:
            try:
                print("   Generating pre-reporting results...")
                
                # Load comparison results
                cursor.execute("""
                    SELECT id, employee_id, employee_name, section_name, item_label,
                           previous_value, current_value, change_type, priority,
                           numeric_difference, percentage_change
                    FROM comparison_results 
                    WHERE session_id = ?
                    ORDER BY priority DESC, section_name, employee_id
                """, (best_session,))
                
                comparison_rows = cursor.fetchall()
                
                if comparison_rows:
                    # Convert to proper format
                    all_changes = []
                    for row in comparison_rows:
                        change = {
                            'id': row[0],
                            'employee_id': row[1],
                            'employee_name': row[2],
                            'section_name': row[3],
                            'item_label': row[4],
                            'previous_value': row[5],
                            'current_value': row[6],
                            'change_type': row[7],
                            'priority': row[8],
                            'numeric_difference': row[9],
                            'percentage_change': row[10]
                        }
                        all_changes.append(change)
                    
                    # Categorize and store
                    categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                    auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                    manager._store_pre_reporting_results(categorized_changes, auto_selected)
                    
                    # Verify storage
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (best_session,))
                    new_pre_reporting_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Generated {new_pre_reporting_count} pre-reporting results")
                    
                    # Update phase status to WAITING_FOR_USER
                    session_manager.update_phase_status('PRE_REPORTING', 'WAITING_FOR_USER', new_pre_reporting_count)
                    
                    session_data['pre_reporting_results'] = new_pre_reporting_count
            
            except Exception as e:
                print(f"   ❌ Pre-reporting generation failed: {e}")
        
        # 7. Test UI data methods
        print("\n7. 🧪 TESTING UI DATA METHODS:")
        
        try:
            # Test get_pre_reporting_data
            result = manager.get_pre_reporting_data()
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                session_id = result.get('session_id', 'unknown')
                
                print(f"   ✅ get_pre_reporting_data():")
                print(f"     Session: {session_id}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                # Check auto-selection
                auto_selected = sum(1 for item in result['data'] if item.get('selected_for_report'))
                print(f"     Auto-selected: {auto_selected}")
                print(f"     Pending review: {data_count - auto_selected}")
                
                print("   ✅ UI data is now available!")
            else:
                print(f"   ❌ UI data still not available: {result}")
        
        except Exception as e:
            print(f"   ❌ UI data test failed: {e}")
        
        # 8. Final status
        print("\n8. ✅ FINAL STATUS:")
        
        final_status = session_manager.get_session_status()
        
        print(f"   Current session: {best_session}")
        print(f"   Phase statuses:")
        for phase in final_status['phases']:
            print(f"     {phase['name']}: {phase['status']} ({phase['data_count']} records)")
        
        print(f"\n🎉 ROOT CAUSE FIXED!")
        print(f"✅ Session ID mismatch resolved")
        print(f"✅ Current session points to data: {best_session}")
        print(f"✅ Extracted data: {session_data['extracted_data']} records")
        print(f"✅ Comparison results: {session_data['comparison_results']} records")
        print(f"✅ Pre-reporting results: {session_data['pre_reporting_results']} records")
        print(f"✅ UI data methods working")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. UI should now detect PRE_REPORTING phase in WAITING_FOR_USER status")
        print(f"2. Interactive pre-reporting interface should load")
        print(f"3. User can review and select changes")
        print(f"4. User can generate final reports")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_session_id_mismatch_root_cause()
