#!/usr/bin/env python3
"""
Restore the Python database manager with correct syntax
"""

import os
import shutil

def restore_python_db_manager():
    """Restore the Python database manager"""
    print("🔧 RESTORING PYTHON DATABASE MANAGER")
    print("=" * 60)
    
    # Create backup of current broken file
    broken_file = os.path.join(os.path.dirname(__file__), 'core', 'python_database_manager.py')
    backup_file = broken_file.replace('.py', '_broken_backup.py')
    
    if os.path.exists(broken_file):
        shutil.copy2(broken_file, backup_file)
        print(f"   Backed up broken file to: {backup_file}")
    
    # The issue is that we need to remove only the conflicting comparison_results table creation
    # Let me manually fix just the table creation part
    
    print("   Manually fixing the comparison_results table creation...")
    
    # Read the current file
    with open(broken_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix the specific issue - the comparison_results table in ensure_tables_exist should not have constraints
    # Find and replace the problematic table creation
    old_table_creation = '''            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS comparison_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    employee_id TEXT NOT NULL,
                    employee_name TEXT,
                    section_name TEXT NOT NULL,
                    item_label TEXT NOT NULL,
                    previous_value TEXT,
                    current_value TEXT,
                    change_type TEXT,
                    priority TEXT,
                    numeric_difference REAL,
                    percentage_change REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')'''
    
    new_table_creation = '''            # comparison_results table is handled by the unified database schema
            # No constraints on change_type to allow all comparison result types
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS comparison_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    employee_id TEXT NOT NULL,
                    employee_name TEXT,
                    section_name TEXT NOT NULL,
                    item_label TEXT NOT NULL,
                    previous_value TEXT,
                    current_value TEXT,
                    change_type TEXT,
                    priority TEXT,
                    numeric_difference REAL,
                    percentage_change REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')'''
    
    # Replace the content
    if old_table_creation in content:
        content = content.replace(old_table_creation, new_table_creation)
        print("   ✅ Fixed comparison_results table creation")
    else:
        print("   ⚠️ Could not find exact table creation to replace")
    
    # Write the fixed content
    with open(broken_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("   ✅ File restored with correct syntax")
    
    # Test the syntax
    import subprocess
    result = subprocess.run(['python', '-m', 'py_compile', broken_file], 
                          capture_output=True, text=True, cwd=os.path.dirname(__file__))
    
    if result.returncode == 0:
        print("   ✅ Syntax check passed")
        return True
    else:
        print(f"   ❌ Syntax check failed: {result.stderr}")
        return False

if __name__ == "__main__":
    restore_python_db_manager()
