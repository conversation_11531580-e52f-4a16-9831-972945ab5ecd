#!/usr/bin/env python3
"""
Diagnose extraction freeze issue
"""

import sys
import os
import sqlite3
import time
sys.path.append('.')

def diagnose_extraction_freeze():
    """Diagnose what's causing the extraction freeze"""
    print("🔍 DIAGNOSING EXTRACTION FREEZE")
    print("=" * 50)
    
    try:
        # Step 1: Check current session and PDF files
        print("1. 📋 CHECKING SESSION AND PDF FILES:")
        
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"   Current session: {current_session}")
        
        # Get PDF paths from session
        cursor.execute('SELECT current_pdf_path, previous_pdf_path FROM audit_sessions WHERE session_id = ?', (current_session,))
        session_data = cursor.fetchone()
        
        if session_data:
            current_pdf, previous_pdf = session_data
            print(f"   Current PDF: {current_pdf}")
            print(f"   Previous PDF: {previous_pdf}")
            
            # Check if files exist
            current_exists = os.path.exists(current_pdf) if current_pdf else False
            previous_exists = os.path.exists(previous_pdf) if previous_pdf else False
            
            print(f"   Current PDF exists: {current_exists}")
            print(f"   Previous PDF exists: {previous_exists}")
            
            if current_exists:
                current_size = os.path.getsize(current_pdf) / (1024 * 1024)
                print(f"   Current PDF size: {current_size:.2f} MB")
            
            if previous_exists:
                previous_size = os.path.getsize(previous_pdf) / (1024 * 1024)
                print(f"   Previous PDF size: {previous_size:.2f} MB")
            
            # Step 2: Test PDF reading capability
            print("\n2. 🧪 TESTING PDF READING CAPABILITY:")
            
            if current_exists:
                print(f"   Testing current PDF: {current_pdf}")
                try:
                    import PyPDF2
                    with open(current_pdf, 'rb') as f:
                        pdf = PyPDF2.PdfReader(f)
                        total_pages = len(pdf.pages)
                        print(f"   ✅ Current PDF readable: {total_pages} pages")
                        
                        # Test reading first page
                        if total_pages > 0:
                            first_page = pdf.pages[0]
                            text_sample = first_page.extract_text()[:100]
                            print(f"   ✅ First page text sample: {text_sample[:50]}...")
                        
                except Exception as e:
                    print(f"   ❌ Current PDF reading failed: {e}")
                    return False
            
            if previous_exists:
                print(f"   Testing previous PDF: {previous_pdf}")
                try:
                    import PyPDF2
                    with open(previous_pdf, 'rb') as f:
                        pdf = PyPDF2.PdfReader(f)
                        total_pages = len(pdf.pages)
                        print(f"   ✅ Previous PDF readable: {total_pages} pages")
                        
                except Exception as e:
                    print(f"   ❌ Previous PDF reading failed: {e}")
                    return False
            
            # Step 3: Test extraction with timeout
            print("\n3. ⏱️ TESTING EXTRACTION WITH TIMEOUT:")
            
            if current_exists:
                print("   Testing single page extraction...")
                try:
                    from core.perfect_extraction_integration import PerfectExtractionIntegrator
                    
                    integrator = PerfectExtractionIntegrator(debug=True)
                    
                    # Test with timeout
                    import signal
                    
                    def timeout_handler(signum, frame):
                        raise TimeoutError("Extraction timeout")
                    
                    # Set 30 second timeout
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(30)
                    
                    try:
                        # Test single page extraction
                        result = integrator._extract_single_page(current_pdf, 1)
                        signal.alarm(0)  # Cancel timeout
                        
                        print(f"   ✅ Single page extraction successful: {result.get('success')}")
                        if result.get('success'):
                            employee_data = result.get('employee_data', {})
                            print(f"   Employee ID: {employee_data.get('employee_id', 'N/A')}")
                            print(f"   Employee Name: {employee_data.get('employee_name', 'N/A')}")
                        else:
                            print(f"   Error: {result.get('error', 'Unknown')}")
                        
                    except TimeoutError:
                        signal.alarm(0)
                        print("   ❌ EXTRACTION TIMEOUT - This is likely causing the UI freeze!")
                        print("   The extraction process is hanging on PDF processing")
                        return False
                    except Exception as e:
                        signal.alarm(0)
                        print(f"   ❌ Extraction error: {e}")
                        return False
                
                except Exception as e:
                    print(f"   ❌ Failed to test extraction: {e}")
                    return False
            
            # Step 4: Check system resources
            print("\n4. 💻 CHECKING SYSTEM RESOURCES:")
            
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            print(f"   CPU usage: {cpu_percent}%")
            
            # Memory usage
            memory = psutil.virtual_memory()
            print(f"   Memory usage: {memory.percent}% ({memory.used / (1024**3):.1f}GB / {memory.total / (1024**3):.1f}GB)")
            
            # Available CPU cores
            cpu_count = os.cpu_count()
            print(f"   CPU cores: {cpu_count}")
            
            if cpu_percent > 90:
                print("   ⚠️ High CPU usage detected - may cause performance issues")
            
            if memory.percent > 90:
                print("   ⚠️ High memory usage detected - may cause performance issues")
            
            # Step 5: Recommendations
            print("\n5. 💡 RECOMMENDATIONS:")
            
            if current_size > 100 or previous_size > 100:
                print("   • Large PDF files detected - consider reducing batch size")
                print("   • Use smaller test files for initial testing")
            
            print("   • Check if antivirus is scanning PDF files during processing")
            print("   • Ensure sufficient free disk space for temporary files")
            print("   • Close other applications to free up system resources")
            print("   • Try processing one PDF at a time instead of both simultaneously")
            
            return True
        else:
            print("   ❌ No session data found")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_extraction_fix():
    """Create a test extraction with reduced complexity"""
    print("\n🔧 CREATING TEST EXTRACTION FIX")
    print("=" * 40)
    
    try:
        # Create a simplified extraction test
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        
        # Clear any existing extracted data
        cursor.execute('DELETE FROM extracted_data WHERE session_id = ?', (current_session,))
        
        # Create minimal test extracted data to bypass extraction
        test_employees = [
            {
                'employee_id': 'TEST001',
                'employee_name': 'Test Employee 1',
                'period_type': 'current',
                'section_data': '{"EARNINGS": {"BASIC SALARY": "5000.00"}, "DEDUCTIONS": {"INCOME TAX": "800.00"}}'
            },
            {
                'employee_id': 'TEST001',
                'employee_name': 'Test Employee 1',
                'period_type': 'previous',
                'section_data': '{"EARNINGS": {"BASIC SALARY": "4500.00"}, "DEDUCTIONS": {"INCOME TAX": "750.00"}}'
            },
            {
                'employee_id': 'TEST002',
                'employee_name': 'Test Employee 2',
                'period_type': 'current',
                'section_data': '{"EARNINGS": {"BASIC SALARY": "6000.00"}, "DEDUCTIONS": {"INCOME TAX": "900.00"}}'
            },
            {
                'employee_id': 'TEST002',
                'employee_name': 'Test Employee 2',
                'period_type': 'previous',
                'section_data': '{"EARNINGS": {"BASIC SALARY": "6000.00"}, "DEDUCTIONS": {"INCOME TAX": "900.00"}}'
            }
        ]
        
        for employee in test_employees:
            cursor.execute('''
                INSERT INTO extracted_data 
                (session_id, employee_id, employee_name, period_type, section_data)
                VALUES (?, ?, ?, ?, ?)
            ''', (current_session, employee['employee_id'], employee['employee_name'], 
                  employee['period_type'], employee['section_data']))
        
        conn.commit()
        
        # Update session status
        cursor.execute('UPDATE audit_sessions SET status = ? WHERE session_id = ?', ('extraction_complete', current_session))
        
        # Update phase status
        cursor.execute('''
            INSERT OR REPLACE INTO session_phases 
            (session_id, phase_name, status, data_count)
            VALUES (?, ?, ?, ?)
        ''', (current_session, 'EXTRACTION', 'COMPLETED', len(test_employees)))
        
        conn.commit()
        conn.close()
        
        print(f"   ✅ Created {len(test_employees)} test extraction records")
        print(f"   ✅ Session {current_session} marked as extraction complete")
        print("   ✅ System can now proceed to comparison phase")
        print("\n   🚀 NEXT STEPS:")
        print("   1. Try running the audit process again")
        print("   2. It should skip extraction and go directly to comparison")
        print("   3. This will help isolate if the issue is in extraction or elsewhere")
        
        return True
        
    except Exception as e:
        print(f"❌ Test fix failed: {e}")
        return False

if __name__ == "__main__":
    success = diagnose_extraction_freeze()
    
    if success:
        print("\n✅ DIAGNOSIS COMPLETE")
        
        # Offer to create test fix
        create_fix = input("\nCreate test extraction data to bypass freeze? (y/n): ").lower().strip()
        if create_fix == 'y':
            fix_success = create_test_extraction_fix()
            if fix_success:
                print("\n🎉 TEST EXTRACTION FIX CREATED!")
                print("   Try running the audit process again.")
            else:
                print("\n⚠️ TEST FIX FAILED")
    else:
        print("\n⚠️ DIAGNOSIS INCOMPLETE")
        print("   The extraction freeze issue needs further investigation.")
