#!/usr/bin/env python3
"""
Debug the database query execution specifically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.phased_process_manager import PhasedProcessManager

def debug_database_query():
    """Debug the database query execution step by step"""
    
    print("🔍 DEBUGGING DATABASE QUERY EXECUTION")
    print("=" * 60)
    
    # Initialize the manager
    manager = PhasedProcessManager()
    
    # Get latest session
    sessions = manager.db_manager.execute_query("""
        SELECT session_id FROM audit_sessions 
        ORDER BY created_at DESC 
        LIMIT 1
    """)
    
    if not sessions:
        print("❌ No sessions found")
        return
    
    session_id = sessions[0]['session_id']
    manager.session_id = session_id
    print(f"📋 Testing session: {session_id}")
    
    # Test the exact query from _load_extracted_data
    print("\n🔍 TESTING EXACT QUERY FROM _load_extracted_data:")
    print("-" * 50)
    
    query = '''SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
               FROM extracted_data
               WHERE session_id = ? AND period_type = ?
               ORDER BY employee_id, section_name, item_label'''
    
    print(f"Query: {query}")
    print(f"Parameters: ({session_id}, 'current')")
    
    try:
        rows = manager.db_manager.execute_query(query, (session_id, 'current'))
        print(f"Query result: {len(rows)} rows")
        
        if rows:
            print(f"First row type: {type(rows[0])}")
            print(f"First row: {rows[0]}")
            
            # Test row unpacking
            print("\n🔍 TESTING ROW UNPACKING:")
            print("-" * 30)
            
            try:
                first_row = rows[0]
                if isinstance(first_row, dict):
                    print("Row is dictionary format")
                    emp_id = first_row['employee_id']
                    emp_name = first_row['employee_name']
                    section = first_row['section_name']
                    label = first_row['item_label']
                    value = first_row['item_value']
                    numeric_val = first_row['numeric_value']
                    print(f"Unpacked: {emp_id}, {emp_name}, {section}, {label}, {value}, {numeric_val}")
                else:
                    print("Row is tuple format")
                    emp_id, emp_name, section, label, value, numeric_val = first_row
                    print(f"Unpacked: {emp_id}, {emp_name}, {section}, {label}, {value}, {numeric_val}")
                    
            except Exception as e:
                print(f"❌ Error unpacking row: {e}")
                print(f"Row content: {first_row}")
        
        # Test manual implementation of _load_extracted_data logic
        print("\n🔍 TESTING MANUAL IMPLEMENTATION:")
        print("-" * 50)
        
        employees = {}
        processed_count = 0
        
        for row in rows[:100]:  # Test with first 100 rows
            processed_count += 1
            
            try:
                if isinstance(row, dict):
                    emp_id = row['employee_id']
                    emp_name = row['employee_name']
                    section = row['section_name']
                    label = row['item_label']
                    value = row['item_value']
                    numeric_val = row['numeric_value']
                else:
                    emp_id, emp_name, section, label, value, numeric_val = row
                
                if emp_id not in employees:
                    employees[emp_id] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'sections': {}
                    }
                
                if section not in employees[emp_id]['sections']:
                    employees[emp_id]['sections'][section] = {}
                
                employees[emp_id]['sections'][section][label] = {
                    'value': value,
                    'numeric_value': numeric_val
                }
                
                if processed_count <= 3:
                    print(f"  Processed row {processed_count}: {emp_id} - {section}.{label}")
                    
            except Exception as e:
                print(f"❌ Error processing row {processed_count}: {e}")
                print(f"Row: {row}")
                break
        
        print(f"Manual implementation result: {len(employees)} employees from {processed_count} rows")
        
        if employees:
            sample_emp_id = list(employees.keys())[0]
            sample_emp = employees[sample_emp_id]
            print(f"Sample employee: {sample_emp_id}")
            print(f"  Name: {sample_emp['employee_name']}")
            print(f"  Sections: {list(sample_emp['sections'].keys())}")
        
    except Exception as e:
        print(f"❌ Error executing query: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    
    print("\n" + "=" * 60)
    print("🎯 DEBUG COMPLETE")

if __name__ == "__main__":
    debug_database_query()
