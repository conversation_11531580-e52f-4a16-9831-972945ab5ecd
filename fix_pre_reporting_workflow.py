#!/usr/bin/env python3
"""
Fix PRE-REPORTING workflow to wait for user interaction
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_pre_reporting_workflow():
    """Fix PRE-REPORTING workflow to wait for user interaction"""
    print("🔧 FIXING PRE-REPORTING WORKFLOW")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 CURRENT SESSION STATUS:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id, get_session_manager
            
            current_session = get_current_session_id()
            session_manager = get_session_manager()
            
            print(f"   ✅ Current session: {current_session}")
            
            # Check current phase status
            status = session_manager.get_session_status()
            print(f"   📊 Current phase statuses:")
            for phase in status['phases']:
                print(f"     {phase['name']}: {phase['status']} ({phase['data_count']} records)")
        
        except Exception as e:
            print(f"   ❌ Could not get session status: {e}")
            return
        
        # 2. Check if PRE_REPORTING was marked as completed incorrectly
        print("\n2. 🔍 CHECKING PRE_REPORTING STATUS:")
        
        pre_reporting_phase = next((p for p in status['phases'] if p['name'] == 'PRE_REPORTING'), None)
        
        if pre_reporting_phase:
            print(f"   Current status: {pre_reporting_phase['status']}")
            print(f"   Data count: {pre_reporting_phase['data_count']}")
            
            if pre_reporting_phase['status'] == 'COMPLETED':
                print("   ❌ PROBLEM: PRE_REPORTING marked as COMPLETED without user interaction!")
                
                # Check if we have pre_reporting_results
                cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                pre_reporting_count = cursor.fetchone()[0]
                
                if pre_reporting_count > 0:
                    print(f"   ✅ Pre-reporting data exists: {pre_reporting_count} records")
                    print("   🔧 Fixing: Changing status to WAITING_FOR_USER")
                    
                    # Update phase status to waiting for user
                    session_manager.update_phase_status('PRE_REPORTING', 'WAITING_FOR_USER', pre_reporting_count)
                    
                    print("   ✅ PRE_REPORTING status updated to WAITING_FOR_USER")
                else:
                    print("   ❌ No pre-reporting data found")
            else:
                print(f"   ✅ Status is correct: {pre_reporting_phase['status']}")
        else:
            print("   ❌ PRE_REPORTING phase not found")
        
        # 3. Test if UI can load pre-reporting data
        print("\n3. 🧪 TESTING UI DATA AVAILABILITY:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            result = manager.get_pre_reporting_data()
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                session_id = result.get('session_id', 'unknown')
                
                print(f"   ✅ UI data available:")
                print(f"     Session: {session_id}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                # Check auto-selection status
                auto_selected = sum(1 for item in result['data'] if item.get('selected_for_report'))
                print(f"     Auto-selected: {auto_selected}")
                print(f"     Pending user review: {data_count - auto_selected}")
                
                print("   ✅ UI should be able to load interactive pre-reporting interface")
            else:
                print(f"   ❌ UI data not available: {result}")
        
        except Exception as e:
            print(f"   ❌ UI data test failed: {e}")
        
        # 4. Create a mechanism to complete PRE_REPORTING after user interaction
        print("\n4. 🔧 CREATING USER INTERACTION COMPLETION MECHANISM:")
        
        # Add a method to mark PRE_REPORTING as truly complete after user approval
        completion_script = '''
def complete_pre_reporting_after_user_approval(session_id, selected_changes_count):
    """Complete PRE_REPORTING phase after user has made selections"""
    try:
        from core.session_manager import get_session_manager
        
        session_manager = get_session_manager()
        session_manager.update_phase_status('PRE_REPORTING', 'COMPLETED', selected_changes_count)
        
        print(f"✅ PRE_REPORTING completed with {selected_changes_count} user-selected changes")
        return True
    except Exception as e:
        print(f"❌ Failed to complete PRE_REPORTING: {e}")
        return False

def trigger_report_generation_after_user_approval():
    """Trigger report generation after user approves selections"""
    try:
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager()
        
        # This should be called by the UI when user clicks "Generate Report"
        result = manager._phase_report_generation({})
        
        if result:
            print("✅ Report generation completed")
            return True
        else:
            print("❌ Report generation failed")
            return False
    except Exception as e:
        print(f"❌ Report generation error: {e}")
        return False
'''
        
        # Save the completion mechanism
        with open('core/pre_reporting_completion.py', 'w') as f:
            f.write(completion_script)
        
        print("   ✅ Created pre_reporting_completion.py with user interaction handlers")
        
        # 5. Check current workflow state
        print("\n5. 📊 CURRENT WORKFLOW STATE:")
        
        # Get updated status
        updated_status = session_manager.get_session_status()
        
        print("   Phase progression:")
        for phase in updated_status['phases']:
            status_icon = "✅" if phase['status'] == 'COMPLETED' else "⏳" if phase['status'] == 'WAITING_FOR_USER' else "❌"
            print(f"     {status_icon} {phase['name']}: {phase['status']} ({phase['data_count']} records)")
        
        # 6. Provide instructions for UI integration
        print("\n6. 🎯 UI INTEGRATION INSTRUCTIONS:")
        
        print("   The PRE_REPORTING workflow should now work as follows:")
        print("   1. ✅ Backend processes and categorizes changes")
        print("   2. ✅ Backend stores pre_reporting_results")
        print("   3. ✅ Backend sets status to WAITING_FOR_USER")
        print("   4. ⏳ UI detects WAITING_FOR_USER status")
        print("   5. ⏳ UI loads interactive pre-reporting interface")
        print("   6. ⏳ User reviews and selects changes")
        print("   7. ⏳ User clicks 'Generate Report' button")
        print("   8. ⏳ UI calls completion mechanism")
        print("   9. ⏳ Backend marks PRE_REPORTING as COMPLETED")
        print("   10. ⏳ Backend proceeds to REPORT_GENERATION")
        
        print(f"\n   🔧 NEXT STEPS:")
        print("   1. Update phased_process_manager.py to use WAITING_FOR_USER status")
        print("   2. Update UI to detect WAITING_FOR_USER and show interactive interface")
        print("   3. Update 'Generate Report' button to call completion mechanism")
        print("   4. Test the complete workflow")
        
        # 7. Show what needs to be changed in the code
        print("\n7. 📝 CODE CHANGES NEEDED:")
        
        print("   In phased_process_manager.py _phase_pre_reporting method:")
        print("   CHANGE:")
        print("     session_manager.update_phase_status('PRE_REPORTING', 'COMPLETED', len(categorized_changes))")
        print("   TO:")
        print("     session_manager.update_phase_status('PRE_REPORTING', 'WAITING_FOR_USER', len(categorized_changes))")
        
        print("\n   In interactive_pre_reporting.js proceedToReportGeneration method:")
        print("   ADD:")
        print("     // Mark PRE_REPORTING as completed after user approval")
        print("     await window.api.completePREReportingPhase(selectedCount);")
        
        print(f"\n🎉 PRE-REPORTING WORKFLOW FIX READY!")
        print(f"✅ Phase status corrected to WAITING_FOR_USER")
        print(f"✅ UI data available for interaction")
        print(f"✅ Completion mechanism created")
        print(f"✅ Integration instructions provided")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_pre_reporting_workflow()
