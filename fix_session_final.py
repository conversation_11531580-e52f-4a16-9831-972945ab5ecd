#!/usr/bin/env python3
"""
Final fix for session management - set the correct current session
"""

import sys
import sqlite3

def fix_session_final():
    """Set the correct current session with data"""
    print("🔧 FINAL SESSION FIX")
    print("=" * 40)
    
    try:
        # 1. Find the session with the most recent data
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Find the session that was just created with 2891 changes
        cursor.execute("""
            SELECT session_id, COUNT(*) as count, MAX(created_at) as latest
            FROM pre_reporting_results 
            GROUP BY session_id 
            ORDER BY latest DESC, count DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        
        if result:
            session_id, count, latest = result
            print(f"✅ Found latest session: {session_id}")
            print(f"   Pre-reporting records: {count}")
            print(f"   Created: {latest}")
            
            # 2. Set as current session
            cursor.execute("""
                INSERT OR REPLACE INTO current_session 
                (id, session_id, session_name, status, updated_at)
                VALUES (1, ?, ?, 'ACTIVE', datetime('now'))
            """, (session_id, f"Fixed_Session_{session_id[-8:]}"))
            
            # 3. Set PRE_REPORTING phase to WAITING_FOR_USER
            cursor.execute("""
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status, started_at, data_count)
                VALUES (?, ?, ?, datetime('now'), ?)
            """, (session_id, 'PRE_REPORTING', 'WAITING_FOR_USER', count))
            
            # 4. Set session status to pre_reporting_ready
            cursor.execute("""
                UPDATE audit_sessions SET status = 'pre_reporting_ready' WHERE session_id = ?
            """, (session_id,))
            
            conn.commit()
            
            # 5. Verify the fix
            cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
            current = cursor.fetchone()
            
            if current and current[0] == session_id:
                print(f"🎉 SUCCESS! Current session set to: {session_id}")
                print(f"   ✅ {count} pre-reporting records available")
                print(f"   ✅ PRE_REPORTING phase: WAITING_FOR_USER")
                print(f"   ✅ Session status: pre_reporting_ready")
                print(f"   ✅ UI should now show Pre-Reporting interface")
            else:
                print(f"❌ Failed to set current session")
        else:
            print("❌ No sessions with pre-reporting data found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_session_final()
