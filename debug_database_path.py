#!/usr/bin/env python3
"""
Debug database path issue in PhasedProcessManager
"""

import sys
import os
sys.path.append('.')

def debug_database_path():
    """Debug database path differences"""
    print("🔍 DEBUGGING DATABASE PATH ISSUE")
    print("=" * 50)
    
    try:
        # Test 1: Check expected database path
        expected_path = os.path.join(os.path.dirname(__file__), 'data', 'templar_payroll_auditor.db')
        print(f"1. Expected database path: {expected_path}")
        print(f"   File exists: {os.path.exists(expected_path)}")
        
        if os.path.exists(expected_path):
            print(f"   File size: {os.path.getsize(expected_path)} bytes")
        
        # Test 2: Check PythonDatabaseManager path
        from core.python_database_manager import PythonDatabaseManager
        
        db_manager = PythonDatabaseManager()
        print(f"\n2. PythonDatabaseManager path: {db_manager.db_path}")
        print(f"   File exists: {os.path.exists(db_manager.db_path)}")
        
        if os.path.exists(db_manager.db_path):
            print(f"   File size: {os.path.getsize(db_manager.db_path)} bytes")
        
        # Test 3: Check PhasedProcessManager path
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        print(f"\n3. PhasedProcessManager database path: {manager.db_manager.db_path}")
        print(f"   File exists: {os.path.exists(manager.db_manager.db_path)}")
        
        if os.path.exists(manager.db_manager.db_path):
            print(f"   File size: {os.path.getsize(manager.db_manager.db_path)} bytes")
        
        # Test 4: Compare paths
        print(f"\n4. Path comparison:")
        print(f"   Expected == PythonDatabaseManager: {expected_path == db_manager.db_path}")
        print(f"   Expected == PhasedProcessManager: {expected_path == manager.db_manager.db_path}")
        print(f"   PythonDatabaseManager == PhasedProcessManager: {db_manager.db_path == manager.db_manager.db_path}")
        
        # Test 5: Check if they're the same file
        if os.path.exists(expected_path) and os.path.exists(manager.db_manager.db_path):
            expected_stat = os.stat(expected_path)
            manager_stat = os.stat(manager.db_manager.db_path)
            
            print(f"\n5. File identity check:")
            print(f"   Same inode: {expected_stat.st_ino == manager_stat.st_ino}")
            print(f"   Same size: {expected_stat.st_size == manager_stat.st_size}")
            print(f"   Same mtime: {expected_stat.st_mtime == manager_stat.st_mtime}")
        
        # Test 6: Check database content
        print(f"\n6. Database content comparison:")
        
        import sqlite3
        
        # Direct connection
        conn1 = sqlite3.connect(expected_path)
        cursor1 = conn1.cursor()
        cursor1.execute('SELECT COUNT(*) FROM comparison_results')
        direct_count = cursor1.fetchone()[0]
        conn1.close()
        
        print(f"   Direct connection count: {direct_count}")
        
        # PythonDatabaseManager
        python_results = db_manager.execute_query('SELECT COUNT(*) FROM comparison_results')
        python_count = python_results[0]['COUNT(*)'] if python_results and isinstance(python_results[0], dict) else python_results[0] if python_results else 0
        print(f"   PythonDatabaseManager count: {python_count}")
        
        # PhasedProcessManager
        manager_results = manager.db_manager.execute_query('SELECT COUNT(*) FROM comparison_results')
        manager_count = manager_results[0]['COUNT(*)'] if manager_results and isinstance(manager_results[0], dict) else manager_results[0] if manager_results else 0
        print(f"   PhasedProcessManager count: {manager_count}")
        
        # Test 7: Check current working directory
        print(f"\n7. Working directory check:")
        print(f"   Current working directory: {os.getcwd()}")
        print(f"   Script directory: {os.path.dirname(__file__)}")
        
        # Test 8: Check if PhasedProcessManager is creating a new database
        print(f"\n8. Database initialization check:")
        
        # Check if the manager is calling any schema creation that might clear data
        print("   Checking if schema creation clears data...")
        
        # Insert test data via direct connection
        conn2 = sqlite3.connect(expected_path)
        cursor2 = conn2.cursor()
        cursor2.execute('DELETE FROM comparison_results')
        cursor2.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority,
             numeric_difference, percentage_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('test_session', 'TEST_BEFORE', 'Test Before Manager', 'TEST', 'TEST_ITEM',
              '100.00', '200.00', 'INCREASED', 'High', 100.00, 100.0))
        conn2.commit()
        
        cursor2.execute('SELECT COUNT(*) FROM comparison_results')
        before_manager = cursor2.fetchone()[0]
        conn2.close()
        
        print(f"   Records before manager init: {before_manager}")
        
        # Create new manager instance
        manager2 = PhasedProcessManager(debug_mode=True)
        
        # Check after manager init
        conn3 = sqlite3.connect(expected_path)
        cursor3 = conn3.cursor()
        cursor3.execute('SELECT COUNT(*) FROM comparison_results')
        after_manager = cursor3.fetchone()[0]
        conn3.close()
        
        print(f"   Records after manager init: {after_manager}")
        
        if before_manager > after_manager:
            print("   🚨 FOUND THE ISSUE: PhasedProcessManager is clearing data during initialization!")
            return False
        elif manager2.db_manager.db_path != expected_path:
            print("   🚨 FOUND THE ISSUE: PhasedProcessManager is using a different database file!")
            return False
        else:
            print("   ⚠️ Issue is more subtle - needs deeper investigation")
            return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_database_path()
    
    if success:
        print("\n✅ DATABASE PATH ISSUE RESOLVED")
    else:
        print("\n🚨 DATABASE PATH ISSUE IDENTIFIED")
        print("   This is the root cause of the pre-reporting data problem!")
