#!/usr/bin/env python3
"""
Test the production-reliable fix for PRE_REPORTING phase
"""

import sys
import os
import sqlite3

def test_production_fix():
    """Test if the production fix works correctly"""
    print("🧪 TESTING PRODUCTION-RELIABLE FIX")
    print("=" * 60)
    
    db_path = "data/templar_payroll_auditor.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current session status
        print("\n1. 📋 CURRENT SESSION STATUS:")
        
        cursor.execute("""
            SELECT session_id, status, created_at 
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        sessions = cursor.fetchall()
        
        for session in sessions:
            print(f"   Session: {session[0]}")
            print(f"   Status: {session[1]}")
            print(f"   Created: {session[2]}")
            
            # Check phase status for this session
            cursor.execute("""
                SELECT phase_name, status, data_count
                FROM session_phases 
                WHERE session_id = ?
                ORDER BY phase_name
            """, (session[0],))
            phases = cursor.fetchall()
            
            if phases:
                print(f"   Phases:")
                for phase in phases:
                    print(f"     {phase[0]}: {phase[1]} ({phase[2]} items)")
            else:
                print(f"   ❌ No phase records found")
            print()
        
        # 2. Check for sessions with pre-reporting data but wrong status
        print("\n2. 🔍 CHECKING FOR SESSIONS NEEDING FIX:")
        
        cursor.execute("""
            SELECT a.session_id, a.status, COUNT(p.id) as pre_reporting_count,
                   sp.status as phase_status
            FROM audit_sessions a
            LEFT JOIN pre_reporting_results p ON a.session_id = p.session_id
            LEFT JOIN session_phases sp ON a.session_id = sp.session_id AND sp.phase_name = 'PRE_REPORTING'
            WHERE a.status != 'pre_reporting_ready'
            GROUP BY a.session_id, a.status, sp.status
            HAVING pre_reporting_count > 0
            ORDER BY a.created_at DESC
            LIMIT 3
        """)
        
        sessions_needing_fix = cursor.fetchall()
        
        if sessions_needing_fix:
            print("   Sessions that need fixing:")
            for session in sessions_needing_fix:
                print(f"   Session: {session[0]}")
                print(f"   Current status: {session[1]} (should be: pre_reporting_ready)")
                print(f"   Pre-reporting records: {session[2]}")
                print(f"   Phase status: {session[3] or 'MISSING'} (should be: WAITING_FOR_USER)")
                print()
        else:
            print("   ✅ No sessions need fixing")
        
        # 3. Apply the production fix to the latest session with data
        if sessions_needing_fix:
            latest_session = sessions_needing_fix[0]
            session_id = latest_session[0]
            pre_reporting_count = latest_session[2]
            
            print(f"\n3. 🔧 APPLYING PRODUCTION FIX TO: {session_id}")
            
            # Apply the same logic as in the production fix
            cursor.execute(
                '''INSERT OR REPLACE INTO session_phases 
                   (session_id, phase_name, status, started_at, data_count)
                   VALUES (?, ?, ?, datetime('now'), ?)''',
                (session_id, 'PRE_REPORTING', 'WAITING_FOR_USER', pre_reporting_count)
            )
            
            cursor.execute(
                'UPDATE audit_sessions SET status = ? WHERE session_id = ?',
                ('pre_reporting_ready', session_id)
            )
            
            conn.commit()
            
            print(f"   ✅ Applied production fix")
            print(f"   - Session status: pre_reporting_ready")
            print(f"   - Phase status: WAITING_FOR_USER")
            print(f"   - Data count: {pre_reporting_count}")
            
            # 4. Verify the fix
            print(f"\n4. ✅ VERIFYING FIX:")
            
            cursor.execute(
                'SELECT status FROM audit_sessions WHERE session_id = ?',
                (session_id,)
            )
            session_status = cursor.fetchone()[0]
            
            cursor.execute(
                'SELECT status, data_count FROM session_phases WHERE session_id = ? AND phase_name = ?',
                (session_id, 'PRE_REPORTING')
            )
            phase_info = cursor.fetchone()
            
            print(f"   Session status: {session_status}")
            print(f"   Phase status: {phase_info[0] if phase_info else 'NOT FOUND'}")
            print(f"   Data count: {phase_info[1] if phase_info else 'N/A'}")
            
            if session_status == 'pre_reporting_ready' and phase_info and phase_info[0] == 'WAITING_FOR_USER':
                print(f"   🎉 PRODUCTION FIX SUCCESSFUL!")
                print(f"   - UI should now detect waiting status")
                print(f"   - Pre-Reporting interface should appear")
                print(f"   - System will wait for user interaction")
            else:
                print(f"   ❌ Fix verification failed")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_production_fix()
