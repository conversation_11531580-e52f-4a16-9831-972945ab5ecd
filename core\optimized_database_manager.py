#!/usr/bin/env python3
"""
Optimized Database Manager
Implements batch operations and connection pooling for enhanced performance
"""

import sqlite3
import threading
import os
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from dataclasses import dataclass
import time

@dataclass
class BatchInsertResult:
    """Result of batch insert operation"""
    success: bool
    inserted_count: int
    failed_count: int
    execution_time: float
    errors: List[str] = None

class OptimizedDatabaseManager:
    """
    Optimized database manager with batch operations and connection pooling
    """
    
    def __init__(self, db_path: str = None, pool_size: int = 5):
        if db_path is None:
            # Use the main application database by default
            db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'templar_payroll_auditor.db')
        self.db_path = db_path
        self.pool_size = pool_size
        self.connection_pool = []
        self.pool_lock = threading.Lock()
        self._initialize_pool()
    
    def _initialize_pool(self):
        """Initialize connection pool"""
        for _ in range(self.pool_size):
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            # Enable WAL mode for better concurrent access
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            self.connection_pool.append(conn)
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool"""
        with self.pool_lock:
            if self.connection_pool:
                conn = self.connection_pool.pop()
            else:
                # Create new connection if pool is empty
                conn = sqlite3.connect(self.db_path, check_same_thread=False)
                conn.row_factory = sqlite3.Row
        
        try:
            yield conn
        finally:
            with self.pool_lock:
                if len(self.connection_pool) < self.pool_size:
                    self.connection_pool.append(conn)
                else:
                    conn.close()
    
    def batch_insert_in_house_loans(self, loans: List[Dict[str, Any]]) -> BatchInsertResult:
        """
        Batch insert in-house loans for optimal performance
        
        Args:
            loans: List of loan dictionaries
            
        Returns:
            BatchInsertResult with operation statistics
        """
        start_time = time.time()
        inserted_count = 0
        failed_count = 0
        errors = []
        
        if not loans:
            return BatchInsertResult(True, 0, 0, 0.0, [])
        
        insert_sql = """
            INSERT INTO in_house_loans 
            (employee_no, employee_name, department, loan_type, loan_amount,
             period_month, period_year, period_acquired, source_session, remarks)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            with self.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                
                for loan in loans:
                    try:
                        conn.execute(insert_sql, (
                            loan.get('employee_no', ''),
                            loan.get('employee_name', ''),
                            loan.get('department', ''),
                            loan.get('loan_type', ''),
                            loan.get('loan_amount', 0.0),
                            loan.get('period_month', ''),
                            loan.get('period_year', ''),
                            loan.get('period_acquired', ''),
                            loan.get('source_session', ''),
                            loan.get('remarks', 'Batch Insert - Enhanced Detection')
                        ))
                        inserted_count += 1
                        
                    except Exception as e:
                        failed_count += 1
                        errors.append(f"Loan insert error: {str(e)}")
                
                conn.execute("COMMIT")
                
        except Exception as e:
            failed_count = len(loans)
            errors.append(f"Batch transaction error: {str(e)}")
        
        execution_time = time.time() - start_time
        
        return BatchInsertResult(
            success=failed_count == 0,
            inserted_count=inserted_count,
            failed_count=failed_count,
            execution_time=execution_time,
            errors=errors
        )
    
    def batch_insert_external_loans(self, loans: List[Dict[str, Any]]) -> BatchInsertResult:
        """
        Batch insert external loans for optimal performance
        """
        start_time = time.time()
        inserted_count = 0
        failed_count = 0
        errors = []
        
        if not loans:
            return BatchInsertResult(True, 0, 0, 0.0, [])
        
        insert_sql = """
            INSERT INTO external_loans 
            (employee_no, employee_name, department, loan_type, loan_amount,
             period_month, period_year, period_acquired, source_session)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            with self.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                
                for loan in loans:
                    try:
                        conn.execute(insert_sql, (
                            loan.get('employee_no', ''),
                            loan.get('employee_name', ''),
                            loan.get('department', ''),
                            loan.get('loan_type', ''),
                            loan.get('loan_amount', 0.0),
                            loan.get('period_month', ''),
                            loan.get('period_year', ''),
                            loan.get('period_acquired', ''),
                            loan.get('source_session', '')
                        ))
                        inserted_count += 1
                        
                    except Exception as e:
                        failed_count += 1
                        errors.append(f"External loan insert error: {str(e)}")
                
                conn.execute("COMMIT")
                
        except Exception as e:
            failed_count = len(loans)
            errors.append(f"Batch transaction error: {str(e)}")
        
        execution_time = time.time() - start_time
        
        return BatchInsertResult(
            success=failed_count == 0,
            inserted_count=inserted_count,
            failed_count=failed_count,
            execution_time=execution_time,
            errors=errors
        )
    
    def batch_insert_motor_vehicles(self, vehicles: List[Dict[str, Any]]) -> BatchInsertResult:
        """
        Batch insert motor vehicle maintenance records for optimal performance
        """
        start_time = time.time()
        inserted_count = 0
        failed_count = 0
        errors = []
        
        if not vehicles:
            return BatchInsertResult(True, 0, 0, 0.0, [])
        
        insert_sql = """
            INSERT INTO motor_vehicle_maintenance 
            (employee_no, employee_name, department, allowance_type, payable_amount,
             period_month, period_year, period_acquired, source_session, remarks)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            with self.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                
                for vehicle in vehicles:
                    try:
                        conn.execute(insert_sql, (
                            vehicle.get('employee_no', ''),
                            vehicle.get('employee_name', ''),
                            vehicle.get('department', ''),
                            vehicle.get('allowance_type', ''),
                            vehicle.get('payable_amount', 0.0),
                            vehicle.get('period_month', ''),
                            vehicle.get('period_year', ''),
                            vehicle.get('period_acquired', ''),
                            vehicle.get('source_session', ''),
                            vehicle.get('remarks', 'Batch Insert - Enhanced Detection')
                        ))
                        inserted_count += 1
                        
                    except Exception as e:
                        failed_count += 1
                        errors.append(f"Motor vehicle insert error: {str(e)}")
                
                conn.execute("COMMIT")
                
        except Exception as e:
            failed_count = len(vehicles)
            errors.append(f"Batch transaction error: {str(e)}")
        
        execution_time = time.time() - start_time
        
        return BatchInsertResult(
            success=failed_count == 0,
            inserted_count=inserted_count,
            failed_count=failed_count,
            execution_time=execution_time,
            errors=errors
        )
    
    def execute_batch_query(self, query: str, params_list: List[tuple]) -> BatchInsertResult:
        """
        Execute batch query with multiple parameter sets
        
        Args:
            query: SQL query with placeholders
            params_list: List of parameter tuples
            
        Returns:
            BatchInsertResult with operation statistics
        """
        start_time = time.time()
        inserted_count = 0
        failed_count = 0
        errors = []
        
        if not params_list:
            return BatchInsertResult(True, 0, 0, 0.0, [])
        
        try:
            with self.get_connection() as conn:
                conn.execute("BEGIN TRANSACTION")
                
                for params in params_list:
                    try:
                        conn.execute(query, params)
                        inserted_count += 1
                    except Exception as e:
                        failed_count += 1
                        errors.append(f"Query execution error: {str(e)}")
                
                conn.execute("COMMIT")
                
        except Exception as e:
            failed_count = len(params_list)
            errors.append(f"Batch transaction error: {str(e)}")
        
        execution_time = time.time() - start_time
        
        return BatchInsertResult(
            success=failed_count == 0,
            inserted_count=inserted_count,
            failed_count=failed_count,
            execution_time=execution_time,
            errors=errors
        )
    
    def optimize_database(self):
        """
        Optimize database performance
        """
        optimization_queries = [
            "VACUUM",
            "ANALYZE",
            "PRAGMA optimize"
        ]
        
        try:
            with self.get_connection() as conn:
                for query in optimization_queries:
                    conn.execute(query)
                    
            print("✅ Database optimization completed")
            
        except Exception as e:
            print(f"❌ Database optimization failed: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get database performance statistics
        """
        try:
            with self.get_connection() as conn:
                # Get database size
                size_result = conn.execute("PRAGMA page_count").fetchone()
                page_size_result = conn.execute("PRAGMA page_size").fetchone()
                
                db_size = (size_result[0] if size_result else 0) * (page_size_result[0] if page_size_result else 0)
                
                # Get table counts
                tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
                table_counts = {}
                
                for table in tables:
                    try:
                        count_result = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()
                        table_counts[table] = count_result[0] if count_result else 0
                    except:
                        table_counts[table] = 0
                
                return {
                    'database_size_bytes': db_size,
                    'database_size_mb': round(db_size / (1024 * 1024), 2),
                    'table_counts': table_counts,
                    'connection_pool_size': len(self.connection_pool),
                    'total_records': sum(table_counts.values())
                }
                
        except Exception as e:
            return {
                'error': str(e),
                'database_size_bytes': 0,
                'table_counts': {},
                'connection_pool_size': 0
            }
    
    def close_all_connections(self):
        """
        Close all connections in the pool
        """
        with self.pool_lock:
            for conn in self.connection_pool:
                try:
                    conn.close()
                except:
                    pass
            self.connection_pool.clear()
        
        print("✅ All database connections closed")

# Global optimized database instance
_optimized_db_instance = None
_db_lock = threading.Lock()

def get_optimized_database(db_path: str = None) -> OptimizedDatabaseManager:
    """
    Get singleton optimized database instance
    
    Args:
        db_path: Optional database path. If not provided, uses the main application database
    """
    import os
    if db_path is None:
        # Use the main application database by default
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'templar_payroll_auditor.db')
    global _optimized_db_instance
    
    with _db_lock:
        if _optimized_db_instance is None:
            _optimized_db_instance = OptimizedDatabaseManager(db_path)
        
        return _optimized_db_instance
