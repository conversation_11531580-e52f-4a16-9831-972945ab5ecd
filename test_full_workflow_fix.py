#!/usr/bin/env python3
"""
Test the complete workflow to verify the comparison fix
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from core.phased_process_manager import PhasedProcessManager
from core.python_database_manager import PythonDatabaseManager
import time

def test_full_workflow_fix():
    """Test the complete workflow to verify comparison fix"""
    
    print("🔧 Testing Complete Workflow - Verifying Comparison Fix")
    print("=" * 70)
    
    # Initialize phased process manager with debug mode
    manager = PhasedProcessManager(debug_mode=True)
    
    # Test PDFs - Using smaller test files
    current_pdf = r"C:\Users\<USER>\Desktop\LAN\JUNE001.pdf"
    previous_pdf = r"C:\Users\<USER>\Desktop\LAN\MAY001.pdf"
    
    if not os.path.exists(current_pdf):
        print(f"❌ Current PDF not found: {current_pdf}")
        return False
    
    if not os.path.exists(previous_pdf):
        print(f"❌ Previous PDF not found: {previous_pdf}")
        return False
    
    print(f"✅ Current PDF: {current_pdf}")
    print(f"✅ Previous PDF: {previous_pdf}")
    
    try:
        # Test options
        options = {
            'current_month': 'June',
            'current_year': '2025',
            'previous_month': 'May',
            'previous_year': '2025',
            'report_name': 'Comparison Fix Test Report',
            'signature_name': 'Test Auditor'
        }
        
        print(f"\n🚀 Testing complete workflow with small test files...")
        print(f"📋 This will test: Extraction → Comparison → Other Phases")
        
        start_time = time.time()
        
        # Execute the complete workflow with limited pages for testing
        print(f"\n🚀 EXECUTING COMPLETE WORKFLOW")
        print("=" * 40)

        # Remove max_pages since we're using small test files
        # options['max_pages'] = 5

        result = manager.execute_complete_workflow(current_pdf, previous_pdf, options)

        end_time = time.time()
        execution_time = end_time - start_time

        print(f"📊 Complete Workflow Result:")
        print(f"  ⏱️  Total execution time: {execution_time:.2f} seconds")

        # Handle string result format (SUCCESS:session_id or ERROR:message)
        if isinstance(result, str):
            if result.startswith('SUCCESS:'):
                session_id = result[8:]  # Remove "SUCCESS:" prefix
                print(f"  🎉 SUCCESS! Complete workflow executed successfully!")
                print(f"  🎉 The 'No comparison data found' error is RESOLVED!")
                print(f"  📋 Session ID: {session_id}")
                success = True
            elif result.startswith('ERROR:'):
                error_msg = result[6:]  # Remove "ERROR:" prefix
                print(f"  ❌ FAILED: {error_msg}")
                success = False
                session_id = None
            else:
                print(f"  ❌ FAILED: Unexpected result format: {result}")
                success = False
                session_id = None
        else:
            # Handle dictionary result format (fallback)
            success = result.get('success', False) if hasattr(result, 'get') else False
            session_id = result.get('session_id', '') if hasattr(result, 'get') else None
            print(f"  Success: {success}")
            print(f"  Message: {result.get('message', 'No message') if hasattr(result, 'get') else 'No message'}")

        if success:
            print(f"  🎉 SUCCESS! Complete workflow executed successfully!")
            print(f"  🎉 The 'No comparison data found' error is RESOLVED!")

            if session_id:
                # Check comparison results to verify the fix
                db_manager = PythonDatabaseManager()

                comparison_count = db_manager.execute_query(
                    "SELECT COUNT(*) as count FROM comparison_results WHERE session_id = ?",
                    (session_id,)
                )[0]['count']

                print(f"  📊 Comparison results generated: {comparison_count}")

                if comparison_count > 0:
                    print(f"  ✅ Comparison data successfully generated!")

                    # Show sample comparison results
                    print(f"\n📋 Sample Comparison Results:")
                    sample_comparisons = db_manager.execute_query(
                        """SELECT employee_id, employee_name, section_name, item_label,
                                  change_type, current_value, previous_value, priority_level
                           FROM comparison_results
                           WHERE session_id = ?
                           ORDER BY priority_level DESC, employee_id
                           LIMIT 10""",
                        (session_id,)
                    )

                    change_types = {}
                    for row in sample_comparisons:
                        change_type = row['change_type']
                        priority = row['priority_level']
                        employee = f"{row['employee_id']} ({row['employee_name']})"
                        item = f"{row['section_name']}: {row['item_label']}"

                        # Count change types
                        change_types[change_type] = change_types.get(change_type, 0) + 1

                        if change_type == 'NEW':
                            print(f"  🆕 {priority} - {employee} - {item} = {row['current_value']} (NEW)")
                        elif change_type == 'REMOVED':
                            print(f"  🗑️  {priority} - {employee} - {item} = {row['previous_value']} (REMOVED)")
                        elif change_type == 'CHANGED':
                            print(f"  🔄 {priority} - {employee} - {item}: {row['previous_value']} → {row['current_value']}")

                    print(f"\n📊 Change Type Summary:")
                    for change_type, count in change_types.items():
                        print(f"  {change_type}: {count} changes")

                    print(f"\n🎉 COMPLETE WORKFLOW FIX VERIFICATION: SUCCESS!")
                    print(f"  ✅ Extraction: WORKING")
                    print(f"  ✅ Comparison: WORKING (No comparison data found error RESOLVED!)")
                    print(f"  ✅ Complete workflow: WORKING")

                    return True

                else:
                    print(f"  ⚠️  Workflow completed but no comparison results generated")
                    print(f"     This might indicate no changes were found between periods")
                    return True
            else:
                print(f"  ⚠️  No session ID returned")
                return True

        else:
            if isinstance(result, str) and result.startswith('ERROR:'):
                error_msg = result[6:]  # Remove "ERROR:" prefix
                print(f"  ❌ Complete workflow failed: {error_msg}")

                # Check if it's still the old error
                if 'no comparison data found' in error_msg.lower():
                    print(f"  ❌ STILL GETTING 'No comparison data found' ERROR!")
                    print(f"     The fix may not be complete or there's another issue")
                    return False
                else:
                    print(f"  ℹ️  Different error - the 'No comparison data found' error is resolved")
                    print(f"     But there's a new issue to investigate: {error_msg}")
                    return False
            else:
                # Handle dictionary result format (fallback)
                error_msg = result.get('error', 'Unknown error') if hasattr(result, 'get') else 'Unknown error'
                print(f"  ❌ Complete workflow failed: {error_msg}")

                # Check if it's still the old error
                if 'no comparison data found' in error_msg.lower():
                    print(f"  ❌ STILL GETTING 'No comparison data found' ERROR!")
                    print(f"     The fix may not be complete or there's another issue")
                    return False
                else:
                    print(f"  ℹ️  Different error - the 'No comparison data found' error is resolved")
                    print(f"     But there's a new issue to investigate: {error_msg}")
                    return False
            
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_workflow_fix()
    if success:
        print(f"\n🎉 FINAL RESULT: COMPARISON FIX SUCCESSFUL!")
        print(f"   The 'No comparison data found' error has been resolved.")
        print(f"   The complete workflow is now working correctly.")
    else:
        print(f"\n❌ FINAL RESULT: COMPARISON FIX NEEDS MORE WORK")
        print(f"   The issue may not be fully resolved yet.")
