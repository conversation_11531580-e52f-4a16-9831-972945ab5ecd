/**
 * CONTENT SWITCHING MANAGER
 * Manages stable UI transformations using content switching containers
 * Prevents layout shifts and provides smooth transitions between process phases
 */

// COMMENTED OUT: Real ContentSwitchingManager - Using fallback version for now
// TODO: Re-enable this later after fallback is fully tested and working
/*
// Define ContentSwitchingManager class for Electron renderer process
// This implementation uses a self-executing function to ensure proper initialization
(function() {
  'use strict';

  // Register a load event to ensure DOM is ready before any initialization
  window.addEventListener('DOMContentLoaded', function() {
    console.log('\u2705 ContentSwitchingManager: DOM content loaded, preparing initialization');
    initializeManager();
  });

  // Main initialization function
  function initializeManager() {
    // Create the manager class if it doesn't exist yet
    if (typeof window.ContentSwitchingManager !== 'undefined') {
      console.log('\u2705 ContentSwitchingManager already defined, skipping definition');
      return;
    }
    
    // Define the ContentSwitchingManager class
    window.ContentSwitchingManager = class ContentSwitchingManager {
    constructor() {
        this.currentPhase = null;
        // UPDATED: Align with new phased process manager sequence
        this.phases = [
            'extraction',
            'comparison',
            'auto-learning',
            'tracker-feeding',
            'pre-reporting',
            'report-generation'
        ];

        // UPDATED: Align progress ranges with phased process manager
        this.phaseProgress = {
            'extraction': { start: 0, end: 20 },
            'comparison': { start: 20, end: 35 },
            'auto-learning': { start: 35, end: 50 },
            'tracker-feeding': { start: 50, end: 65 },
            'pre-reporting': { start: 65, end: 80 },
            'report-generation': { start: 80, end: 100 }
        };
        
        this.initialized = false;
    }

    /**
     * Initialize content switching container
     */
    initialize() {
        if (this.initialized) return;
        
        this.createContentSwitchingContainer();
        this.setupEventListeners();
        this.initialized = true;
        
        console.log('✅ Content Switching Manager initialized');
    }
    
    /**
     * Set up event listeners for user interactions and backend events
     */
    setupEventListeners() {
        console.log('🎧 Setting up content switching event listeners');
        
        // Get the start button
        const startButton = document.getElementById('start-audit-button');
        if (startButton) {
            startButton.addEventListener('click', this.onStartButtonClick.bind(this));
            console.log('✅ Start button event listener attached');
        } else {
            console.warn('⚠️ Start button not found');
        }
        
        // Set up listeners for backend phase completion events
        this.setupBackendEventListeners();
    }
    
    /**
     * Handle start button click
     */
    onStartButtonClick() {
        console.log('🔎 Start button clicked, validating inputs');
        
        // Get file paths from the UI
        const currentPath = document.getElementById('current-pdf-path')?.textContent;
        const previousPath = document.getElementById('previous-pdf-path')?.textContent;
        
        if (!currentPath || currentPath === 'No file selected' || 
            !previousPath || previousPath === 'No file selected') {
            alert('Please select both current and previous payroll PDF files before starting the audit.');
            return;
        }
        
        console.log('✅ Files validated, starting audit process');
        
        // Start the audit process via backend API
        if (window.api && window.api.startPayrollAudit) {
            // Disable the start button and show loading
            const startButton = document.getElementById('start-audit-button');
            if (startButton) {
                startButton.disabled = true;
                startButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }
            
            // Update UI to show extraction phase
            this.switchToPhase('extraction');
            
            // Call backend API to start audit
            window.api.startPayrollAudit(currentPath, previousPath)
                .then(response => {
                    console.log('✅ Audit process started:', response);
                })
                .catch(error => {
                    console.error('❌ Error starting audit:', error);
                    alert(`Error starting audit: ${error.message}`);
                    
                    // Re-enable the start button
                    if (startButton) {
                        startButton.disabled = false;
                        startButton.innerHTML = '<i class="fas fa-play"></i> Start Audit';
                    }
                });
        } else {
            console.error('❌ Audit API not available');
            alert('Error: Audit API not available. Please restart the application.');
        }
    }
    
    /**
     * Set up listeners for backend phase completion events
     * This connects the backend workflow to the UI phase transitions
     */
    setupBackendEventListeners() {
        if (!window.api) {
            console.error('❌ Backend API not available for event listeners');
            return;
        }
        
        console.log('📡 Setting up backend phase completion listeners');
        
        // Listen for extraction phase completion
        if (window.api.onExtractionComplete) {
            window.api.onExtractionComplete((data) => {
                console.log('✅ Extraction phase complete:', data);
                this.updateStatsPanel(data?.stats || {});
                this.switchToPhase('pre-auditing', data);
            });
        } else {
            console.warn('⚠️ onExtractionComplete event listener not available');
        }
        
        // Listen for pre-auditing/comparison phase completion
        // NOTE: In the UI, this is shown as just "Pre-Auditing" - there is no separate comparison phase
        if (window.api.onPreAuditingComplete) {
            window.api.onPreAuditingComplete((data) => {
                console.log('✅ Pre-auditing/Comparison phase complete:', data);
                // Process all comparison data including categorization
                this.categorizeChanges(data);
                this.updateStatsPanel(data?.stats || {});
                // Move directly to tracker feeding phase
                this.switchToPhase('tracker-feeding', data);
            });
        } else {
            console.warn('⚠️ onPreAuditingComplete event listener not available');
        }
        
        // Listen for tracker feeding phase completion
        if (window.api.onTrackerFeedingComplete) {
            window.api.onTrackerFeedingComplete((data) => {
                console.log('✅ Tracker feeding phase complete:', data);
                this.updateStatsPanel(data?.stats || {});
                this.switchToPhase('auto-learning', data);
            });
        } else {
            console.warn('⚠️ onTrackerFeedingComplete event listener not available');
        }
        
        // Listen for auto-learning phase completion
        if (window.api.onAutoLearningComplete) {
            window.api.onAutoLearningComplete((data) => {
                console.log('✅ Auto learning phase complete:', data);
                this.updateStatsPanel(data?.stats || {});
                this.switchToPhase('pre-reporting', data);
            });
        } else {
            console.warn('⚠️ onAutoLearningComplete event listener not available');
        }
        
        // Listen for report generation completion
        if (window.api.onReportGenerationComplete) {
            window.api.onReportGenerationComplete((data) => {
                console.log('✅ Report generation complete:', data);
                this.updateStatsPanel(data?.stats || {});
                this.showReportGenerationComplete(document.getElementById('report-generation-content'), data);
            });
        } else {
            console.warn('⚠️ onReportGenerationComplete event listener not available');
        }
        
        // Listen for real-time progress updates
        if (window.api.onEnhancedProgressUpdate) {
            window.api.onEnhancedProgressUpdate((progressData) => {
                console.log('📊 Real-time progress update:', progressData);
                this.updateRealTimeProgress(progressData);
            });
        } else {
            console.warn('⚠️ onEnhancedProgressUpdate event listener not available');
        }
        
        console.log('✅ Backend phase completion listeners set up successfully');
    }
    
    /**
     * Categorize changes by priority and apply professional terminology
     * @param {Object} data - Comparison data containing changes
     * @returns {Object} The processed data with categorized changes
     */
    categorizeChanges(data) {
        console.log('📊 Categorizing changes by priority and terminology');
        
        if (!data || !data.changes || !Array.isArray(data.changes)) {
            console.warn('No valid changes data to categorize');
            return data;
        }
        
        // Section priority mapping based on business requirements
        const sectionPriorities = {
            'PERSONAL DETAILS': 'High',
            'EARNINGS': 'High',
            'DEDUCTIONS': 'High',
            'LOANS': 'Medium', // Moderate converted to Medium
            'EMPLOYERS CONTRIBUTION': 'Low',
            'EMPLOYEE BANK DETAILS': 'Medium'  // Moderate converted to Medium
        };
        
        // Known routine bulk changes patterns (items to filter out from final report)
        const routinePatterns = [
            'STAFF CREDIT UNION',
            'CREDIT UNION CONTRIBUTION',
            'STAFF SAVINGS',
            'VOLUNTARY DEDUCTION',
            'SEASONAL ADJUSTMENT'
        ];
        
        // Keep track of stats for the categorized changes
        const stats = {
            total: data.changes.length,
            highPriority: 0,
            mediumPriority: 0,
            lowPriority: 0,
            routineChanges: 0
        };
        
        // Process each change
        data.changes = data.changes.map(change => {
            // Apply section-based priority
            const section = change.section ? change.section.toUpperCase() : 'UNKNOWN';
            const priority = sectionPriorities[section] || 'Low';
            
            // Check if this is a routine change
            const isRoutine = routinePatterns.some(pattern => 
                change.item && change.item.toUpperCase().includes(pattern)
            );
            
            // Apply professional terminology
            let description = '';
            
            if (change.type === 'added') {
                // Use "introduced" for new items
                description = `${change.employee || 'Employee'}: ${change.item} introduced in ${change.currentPeriod}: ${change.currentValue || ''}`;
            } else if (change.type === 'removed') {
                description = `${change.employee || 'Employee'}: ${change.item} removed from ${change.previousPeriod}`;
            } else {
                // Use "changed" for modifications
                const difference = this.calculateDifference(change.previousValue, change.currentValue);
                const changeDirection = difference > 0 ? 'increase' : 'decrease';
                description = `${change.employee || 'Employee'}: ${change.item} changed from ${change.previousValue || ''} in ${change.previousPeriod} to ${change.currentValue || ''} in ${change.currentPeriod}: ${changeDirection} of ${Math.abs(difference)}`;
            }
            
            // Update stats
            if (isRoutine) {
                stats.routineChanges++;
            } else if (priority === 'High') {
                stats.highPriority++;
            } else if (priority === 'Medium') {
                stats.mediumPriority++;
            } else {
                stats.lowPriority++;
            }
            
            // Return enhanced change object
            return {
                ...change,
                priority: priority,
                routine: isRoutine,
                description: description,
                includeInReport: (priority === 'High' || priority === 'Medium') && !isRoutine
            };
        });
        
        // Update the data object with stats and enhanced changes
        data.stats = {
            ...data.stats,
            highPriorityChanges: stats.highPriority,
            mediumPriorityChanges: stats.mediumPriority,
            lowPriorityChanges: stats.lowPriority,
            routineChanges: stats.routineChanges
        };
        
        console.log(`✅ Changes categorized: ${stats.highPriority} high, ${stats.mediumPriority} medium, ${stats.lowPriority} low, ${stats.routineChanges} routine`);
        return data;
    }
    
    /**
     * Calculate the numeric difference between two values
     * @param {string|number} prevValue - Previous value
     * @param {string|number} currValue - Current value
     * @returns {number} - The calculated difference
     */
    calculateDifference(prevValue, currValue) {
        // Extract numeric values if they're in currency format (e.g. $1,234.56)
        const extractNumber = (val) => {
            if (typeof val === 'string') {
                // Remove currency symbols, commas and other non-numeric characters except decimal points
                const numericStr = val.replace(/[^0-9.-]/g, '');
                return parseFloat(numericStr) || 0;
            }
            return parseFloat(val) || 0;
        };
        
        const prev = extractNumber(prevValue);
        const curr = extractNumber(currValue);
        return curr - prev;
    }
    
    /**
     * Switch to a specific phase in the workflow - OPTIMIZED for phased process manager
     * @param {string} phase - The phase to switch to
     * @param {Object} data - Optional data to pass to the phase
     */
    switchToPhase(phase, data = {}) {
        console.log(`🔄 Switching to phase: ${phase}`);

        // Validate the phase
        if (!this.phases.includes(phase)) {
            console.error(`❌ Invalid phase: ${phase}`);
            return false;
        }

        // Set the current phase
        this.currentPhase = phase;

        // OPTIMIZED: Single coordinated UI update
        this.performPhaseTransition(phase, data);

        return true;
    }

    /**
     * Perform coordinated phase transition - OPTIMIZED
     * @param {string} phase - The phase to switch to
     * @param {Object} data - Optional data to pass to the phase
     */
    performPhaseTransition(phase, data = {}) {
        console.log(`🎯 Performing optimized transition to: ${phase}`);

        // 1. Update phase indicators (visual feedback first)
        this.updatePhaseIndicators(phase);

        // 2. Update progress and UI state
        this.updateProgressForPhase(phase);

        // 3. Show appropriate content panel with smooth transition
        this.showContentPanelSmooth(phase);

        // 4. Initialize phase-specific UI
        this.initializePhaseUI(phase, data);

        // 5. Update enhanced progress panel
        this.updateEnhancedProgressPanel(phase, data);

        console.log(`✅ Phase transition completed: ${phase}`);
    }
    
    /**
     * Update UI phase indicators
     * @param {string} activePhase - The active phase
     */
    updatePhaseIndicators(activePhase) {
        // Update phase indicators on the UI
        const phaseSteps = document.querySelectorAll('.phase-step');
        phaseSteps.forEach(step => {
            const stepPhase = step.getAttribute('data-phase');
            const phaseIndex = this.phases.indexOf(stepPhase);
            const activeIndex = this.phases.indexOf(activePhase);
            
            // Remove all status classes
            step.classList.remove('active', 'completed', 'pending');
            
            // Add appropriate status class
            if (stepPhase === activePhase) {
                step.classList.add('active');
            } else if (phaseIndex < activeIndex) {
                step.classList.add('completed');
            } else {
                step.classList.add('pending');
            }
        });
    }
    
    /**
     * Show the content panel for the active phase - LEGACY
     * @param {string} phase - The active phase
     */
    showContentPanel(phase) {
        // Hide all content panels
        const contentPanels = document.querySelectorAll('.content-panel');
        contentPanels.forEach(panel => {
            panel.classList.remove('active');
        });

        // Show the active content panel
        const activePanel = document.getElementById(`${phase}-panel`);
        if (activePanel) {
            activePanel.classList.add('active');
        } else {
            console.warn(`⚠️ No content panel found for phase: ${phase}`);
        }
    }

    /**
     * Show content panel with smooth transition - OPTIMIZED
     * @param {string} phase - The active phase
     */
    showContentPanelSmooth(phase) {
        const contentPanels = document.querySelectorAll('.content-panel');
        let activePanel = document.getElementById(`${phase}-panel`);

        // CRITICAL FIX: Also check for phase-content elements
        if (!activePanel) {
            activePanel = document.getElementById(`${phase}-content`);
        }

        if (!activePanel) {
            console.warn(`⚠️ No content panel found for phase: ${phase} (checked both ${phase}-panel and ${phase}-content)`);
            return;
        }

        console.log(`✅ Found active panel for ${phase}:`, activePanel.id);

        // CRITICAL FIX: Handle both content-panel and phase-content elements
        const allPanels = document.querySelectorAll('.content-panel, .phase-content');

        // Add transition class for smooth animation
        allPanels.forEach(panel => {
            panel.style.transition = 'opacity 0.3s ease-in-out';
            if (panel.classList.contains('active')) {
                panel.style.opacity = '0';
                setTimeout(() => {
                    panel.classList.remove('active');
                    panel.style.opacity = '';
                }, 150);
            }
        });

        // Show new panel with fade-in
        setTimeout(() => {
            activePanel.style.opacity = '0';
            activePanel.classList.add('active');
            setTimeout(() => {
                activePanel.style.opacity = '1';
            }, 50);
        }, 150);
    }

    /**
     * Update enhanced progress panel for phase - OPTIMIZED
     * @param {string} phase - The active phase
     * @param {Object} data - Phase data
     */
    updateEnhancedProgressPanel(phase, data = {}) {
        // Update enhanced progress panel if available
        if (window.updateEnhancedPhaseInfo) {
            const phaseMessages = {
                'extraction': 'Extracting payroll data from PDF files...',
                'comparison': 'Comparing current vs previous payroll data...',
                'auto-learning': 'Learning new payroll patterns automatically...',
                'tracker-feeding': 'Feeding new items to loan and allowance trackers...',
                'pre-reporting': 'Preparing changes for user review and selection...',
                'report-generation': 'Generating final reports in multiple formats...'
            };

            const message = data.message || phaseMessages[phase] || `Processing ${phase} phase...`;
            window.updateEnhancedPhaseInfo(phase, message);
        }

        // Update phase indicators if available
        if (window.updatePhaseIndicator) {
            window.updatePhaseIndicator(phase, 'in-progress');
        }

        // Show enhanced progress panel
        const enhancedProgressPanel = document.getElementById('enhanced-progress-panel');
        if (enhancedProgressPanel) {
            enhancedProgressPanel.style.display = 'block';
        }

        // Hide old progress section
        const oldProgressSection = document.getElementById('processing-progress-section');
        if (oldProgressSection) {
            oldProgressSection.style.display = 'none';
        }
    }
    
    /**
     * Initialize phase-specific UI elements
     * @param {string} phase - The active phase
     * @param {Object} data - Phase-specific data
     */
    initializePhaseUI(phase, data) {
        switch(phase) {
            case 'pre-reporting':
                this.initializePreReportingPhase(data);
                break;
            case 'report-generation':
                this.initializeReportGenerationPhase(data);
                break;
            // Add other phases as needed
        }
    }
    
    /**
     * Initialize the Pre-Reporting phase UI
     * @param {Object} data - The changes data to display
     */
    initializePreReportingPhase(data) {
        console.log('📋 Initializing pre-reporting phase UI');
        
        // Get the pre-reporting content container
        const preReportingContent = document.getElementById('pre-reporting-content');
        if (!preReportingContent) {
            console.error('❌ Pre-reporting content container not found');
            return;
        }
        
        // Check if we have the interactive pre-reporting module available
        if (typeof InteractivePreReporting !== 'undefined') {
            console.log('✅ Interactive pre-reporting module found');
            
            // Create instance of interactive pre-reporting if not already created
            if (!window.interactivePreReporting) {
                window.interactivePreReporting = new InteractivePreReporting();
            }
            
            // Configure the interactive pre-reporting UI with the correct settings
            const preReporting = window.interactivePreReporting;
            preReporting.container = preReportingContent;
            preReporting.analyzedChanges = data.changes || data;
            
            // Set priority categorization based on user requirements
            preReporting.priorityConfig = {
                sections: {
                    'PERSONAL DETAILS': 'high',
                    'EARNINGS': 'high',
                    'DEDUCTIONS': 'high',
                    'LOANS': 'moderate',
                    'EMPLOYERS CONTRIBUTION': 'low',
                    'EMPLOYEE BANK DETAILS': 'moderate'
                },
                // Define which changes to include in reports based on priority
                includeInReport: ['high', 'moderate']
            };
            
            // Set routine bulk change filters to remove items like STAFF CREDIT UNION
            preReporting.routineChangeFilters = [
                {
                    name: 'Staff Credit Union',
                    pattern: /STAFF CREDIT UNION|CREDIT UNION/i,
                    reasonToFilter: 'Routine employee-requested adjustment'
                },
                // Add more routine change patterns here as needed
            ];
            
            // Set professional language terminology preferences
            preReporting.terminologyPreferences = {
                modification: 'changed',  // For existing items with value changes
                introduction: 'introduced' // For new items appearing for first time
            };
            
            // Initialize the interactive pre-reporting module
            preReporting.initialize();
            
            // Add event listener for the 'continue to report generation' button
            const continueButton = document.getElementById('continue-to-report-generation');
            if (continueButton) {
                continueButton.addEventListener('click', () => {
                    const selectedChanges = preReporting.getSelectedChanges();
                    this.switchToPhase('report-generation', { selected_changes: selectedChanges });
                });
            }
            
            // Update the phase status
            const phaseStatus = document.querySelector('.panel-status');
            if (phaseStatus) {
                phaseStatus.textContent = 'Active';
                phaseStatus.className = 'panel-status status-active';
            }
        } else {
            console.error('❌ Interactive pre-reporting module not found');
            preReportingContent.innerHTML = `
                <div class="error-container">
                    <h4>❌ Error: Interactive Pre-Reporting Module Not Found</h4>
                    <p>The interactive pre-reporting module could not be loaded. Please check script inclusion in index.html.</p>
                </div>
            `;
        }
    }
    
    /**
     * Initialize the Report Generation phase UI
     * @param {Object} data - The selected changes data to generate reports from
     */
    initializeReportGenerationPhase(data) {
        console.log('📊 Initializing report generation phase UI');
        
        // Get the report generation content container
        const reportGenContent = document.getElementById('report-generation-content');
        if (!reportGenContent) {
            console.error('❌ Report generation content container not found');
            return;
        }
        
        // Show report generation status
        reportGenContent.innerHTML = `
            <div class="report-generation-status">
                <h4>📈 Generating Reports</h4>
                <p>Generating Comparison Report and Final Report based on selected changes...</p>
                <div class="progress-bar">
                    <div class="progress" style="width: 0%"></div>
                </div>
                <div class="report-count">
                    <span class="report-count-value">0</span> of <span class="report-total-value">${data?.selected_changes?.length || 0}</span> changes processed
                </div>
            </div>
        `;
        
        // Call the actual backend API to generate reports
        this.generateReports(data, reportGenContent);
    }
    
    /**
     * Generate reports using the backend API
     * @param {Object} data - The selected changes data
     * @param {HTMLElement} container - The report generation container
     */
    generateReports(data, container) {
        // Call the backend API to generate reports
        if (window.api && window.api.generateReports) {
            // Create a progress event listener
            if (window.api.onReportProgress) {
                window.api.onReportProgress((progress) => {
                    const progressBar = container.querySelector('.progress');
                    const reportCount = container.querySelector('.report-count-value');
                    
                    if (progressBar) {
                        progressBar.style.width = `${progress.percentage}%`;
                    }
                    
                    if (reportCount) {
                        reportCount.textContent = progress.processed || 0;
                    }
                });
            }
            
            // Start report generation
            window.api.generateReports(data)
                .then(result => {
                    console.log('✅ Reports generated successfully:', result);
                    this.showReportGenerationComplete(container, result);
                })
                .catch(error => {
                    console.error('❌ Error generating reports:', error);
                    container.innerHTML = `
                        <div class="error-container">
                            <h4>❌ Error Generating Reports</h4>
                            <p>There was an error generating the reports: ${error.message}</p>
                            <button class="btn btn-primary" id="retry-report-generation">Retry</button>
                        </div>
                    `;
                    
                    // Add event listener to retry button
                    const retryButton = container.querySelector('#retry-report-generation');
                    if (retryButton) {
                        retryButton.addEventListener('click', () => {
                            this.initializeReportGenerationPhase(data);
                        });
                    }
                });
        } else {
            console.error('❌ Report generation API not available');
            container.innerHTML = `
                <div class="error-container">
                    <h4>❌ API Not Available</h4>
                    <p>The report generation API is not available. Please restart the application.</p>
                </div>
            `;
        }
    }
    
    /**
     * Show report generation complete UI
     * @param {HTMLElement} container - The report generation container
     * @param {Object} data - The selected changes data
     */
    showReportGenerationComplete(container, data) {
        container.innerHTML = `
            <div class="report-generation-complete">
                <h4>✅ Reports Generated Successfully</h4>
                <p>${data?.selected_changes?.length || 0} changes processed and included in the reports.</p>
                
                <div class="report-buttons">
                    <button class="btn btn-primary" id="view-comparison-report">
                        <i class="fas fa-chart-line"></i> View Comparison Report
                    </button>
                    <button class="btn btn-success" id="view-final-report">
                        <i class="fas fa-clipboard-check"></i> View Final Report
                    </button>
                    <button class="btn btn-secondary" id="export-reports">
                        <i class="fas fa-file-export"></i> Export All Reports
                    </button>
                </div>
            </div>
        `;
        
        // Add event listeners to report buttons
        container.querySelector('#view-comparison-report').addEventListener('click', () => {
            this.viewReport('comparison', data);
        });
        
        container.querySelector('#view-final-report').addEventListener('click', () => {
            this.viewReport('final', data);
        });
        
        container.querySelector('#export-reports').addEventListener('click', () => {
            this.exportReports(data);
        });
    }
    
    /**
     * View a specific report type
     * @param {string} reportType - The type of report to view ('comparison' or 'final')
     * @param {Object} data - The report data
     */
    viewReport(reportType, data) {
        console.log(`📝 Viewing ${reportType} report`);
        
        // Call the backend API to retrieve the report
        if (window.api && window.api.getReport) {
            window.api.getReport(reportType, data)
                .then(report => {
                    console.log(`✅ ${reportType} report retrieved successfully`);
                    this.showReportViewer(reportType, report);
                })
                .catch(error => {
                    console.error(`❌ Error retrieving ${reportType} report:`, error);
                    alert(`Error retrieving ${reportType} report: ${error.message}`);
                });
        } else {
            console.error(`❌ Report API not available`);
            alert(`Error: Report generation API not available. Please restart the application.`);
        }
    }
    
    /**
     * Show the report viewer UI
     * @param {string} reportType - The type of report ('comparison' or 'final')
     * @param {Object} reportData - The report data from the backend
     */
    showReportViewer(reportType, reportData) {
        // Get the report container
        const reportContainer = document.getElementById('report-viewer-container');
        if (!reportContainer) {
            console.error(`❌ Report viewer container not found`);
            return;
        }
        
        const reportTitle = reportType === 'comparison' ? 'Comparison Report' : 'Final Report';
        
        // Show the report viewer
        reportContainer.innerHTML = `
            <div class="report-viewer">
                <div class="report-header">
                    <h3>${reportTitle}</h3>
                    <button class="btn btn-sm btn-secondary close-report">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
                <div class="report-content" id="report-content-${reportType}">
                    <!-- Report content will be rendered by the backend -->
                </div>
            </div>
        `;
        
        // Show the report container
        reportContainer.style.display = 'block';
        
        // Add event listener to close button
        const closeButton = reportContainer.querySelector('.close-report');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                reportContainer.style.display = 'none';
            });
        }
        
        // Render the report content using the backend API
        if (window.api && window.api.renderReport) {
            const reportContentContainer = document.getElementById(`report-content-${reportType}`);
            window.api.renderReport(reportType, reportData, reportContentContainer);
        }
    }
    
    /**
     * Update progress bar based on current phase
     * @param {string} phase - The current active phase
     */
    updateProgressForPhase(phase) {
        const phaseProgress = this.phaseProgress[phase];
        if (!phaseProgress) return;
        
        // Start with the minimum progress for the phase
        const progressPercentage = phaseProgress.start;
        const progressBar = document.querySelector('.progress-bar .progress');
        if (progressBar) {
            progressBar.style.width = `${progressPercentage}%`;
        }
        
        const progressText = document.getElementById('progress-percentage');
        if (progressText) {
            progressText.textContent = `${Math.round(progressPercentage)}%`;
        }
        
        // Update the processing progress display
        const processingProgress = document.getElementById('processing-progress');
        if (processingProgress) {
            processingProgress.style.display = 'inline-block';
        }
        
        // Update phase status in UI
        this.updatePhaseStatus(phase, 'Processing');
    }
    
    /**
     * Update progress indicators with real-time data from backend
     * @param {Object} progressData - Real-time progress data
     */
    updateRealTimeProgress(progressData) {
        if (!progressData) return;
        
        // Update the overall progress percentage if provided
        if (progressData.percentage !== undefined) {
            const progressBar = document.querySelector('.progress-bar .progress');
            if (progressBar) {
                progressBar.style.width = `${progressData.percentage}%`;
            }
            
            const progressText = document.getElementById('progress-percentage');
            if (progressText) {
                progressText.textContent = `${Math.round(progressData.percentage)}%`;
            }
        }
        
        // Update phase-specific status if provided
        if (progressData.phase && progressData.status) {
            this.updatePhaseStatus(progressData.phase, progressData.status);
        }
        
        // Update processing message if provided
        if (progressData.message) {
            const processingMessage = document.getElementById('processing-message');
            if (processingMessage) {
                processingMessage.textContent = progressData.message;
            }
        }
        
        // Update stats panel if provided
        if (progressData.stats) {
            this.updateStatsPanel(progressData.stats);
        }
    }
    
    /**
     * Update phase status indicator in UI
     * @param {string} phase - The phase to update
     * @param {string} status - The status text to display
     */
    updatePhaseStatus(phase, status) {
        const phaseStep = document.querySelector(`.phase-step[data-phase="${phase}"]`);
        if (phaseStep) {
            const statusElement = phaseStep.querySelector('.step-status');
            if (statusElement) {
                statusElement.textContent = status;
                
                // Update status styling based on status value
                statusElement.classList.remove('status-pending', 'status-processing', 'status-complete', 'status-error');
                
                if (status === 'Pending') {
                    statusElement.classList.add('status-pending');
                } else if (status === 'Processing') {
                    statusElement.classList.add('status-processing');
                } else if (status === 'Complete') {
                    statusElement.classList.add('status-complete');
                } else if (status === 'Error') {
                    statusElement.classList.add('status-error');
                }
            }
        }
    }
    
    /**
     * Update the stats panel with real data from backend
     * @param {Object} stats - Statistics data from backend
     */
    updateStatsPanel(stats) {
        const statsPanel = document.getElementById('stats-panel');
        if (!statsPanel || !stats) return;
        
        // Define the stats to display based on received data
        const statsToDisplay = [
            { label: 'Items Processed', value: stats.itemsProcessed || 0 },
            { label: 'Changes Detected', value: stats.changesDetected || 0 },
            { label: 'High Priority', value: stats.highPriorityChanges || 0 },
            { label: 'Medium Priority', value: stats.mediumPriorityChanges || 0 },
            { label: 'Low Priority', value: stats.lowPriorityChanges || 0 },
            { label: 'Routine Changes', value: stats.routineChanges || 0 }
        ];
        
        // Create the stats panel HTML
        const statsHtml = statsToDisplay.map(stat => `
            <div class="stats-item">
                <div class="stats-label">${stat.label}</div>
                <div class="stats-value">${stat.value}</div>
            </div>
        `).join('');
        
        statsPanel.innerHTML = `
            <div class="stats-panel-header">
                <h4>Audit Statistics</h4>
            </div>
            <div class="stats-panel-content">
                ${statsHtml}
            </div>
        `;
        
        // Make stats panel visible
        statsPanel.style.display = 'block';
    }
    

    /**
     * Create the main content switching container - PRESERVE ORIGINAL DESIGN
     */
    createContentSwitchingContainer() {
        const auditTab = document.getElementById('payroll-audit-tab');
        if (!auditTab) {
            console.error('Payroll audit tab not found');
            return;
        }

        // DON'T OVERRIDE THE BEAUTIFUL ORIGINAL DESIGN!
        // Just add hidden file inputs and enhance existing elements
        console.log('✅ Preserving original professional design');

        this.addHiddenFileInputs();
        this.enhanceExistingElements();
        this.addMinimalStyles();
    }

    /**
     * Add hidden file inputs to work with original design
     */
    addHiddenFileInputs() {
        // Add hidden file inputs that the original design expects
        const existingCurrentInput = document.getElementById('current-pdf-input');
        const existingPreviousInput = document.getElementById('previous-pdf-input');

        if (!existingCurrentInput) {
            const currentInput = document.createElement('input');
            currentInput.type = 'file';
            currentInput.id = 'current-pdf-input';
            currentInput.accept = '.pdf';
            currentInput.style.display = 'none';
            document.body.appendChild(currentInput);
        }

        if (!existingPreviousInput) {
            const previousInput = document.createElement('input');
            previousInput.type = 'file';
            previousInput.id = 'previous-pdf-input';
            previousInput.accept = '.pdf';
            previousInput.style.display = 'none';
            document.body.appendChild(previousInput);
        }
    }

    /**
     * Enhance existing elements without overriding design
     */
    enhanceExistingElements() {
        // Connect original browse buttons to hidden inputs
        const browseCurrent = document.getElementById('browse-current-payroll');
        const browsePrevious = document.getElementById('browse-previous-payroll');

        if (browseCurrent) {
            browseCurrent.addEventListener('click', () => {
                document.getElementById('current-pdf-input')?.click();
            });
        }

        if (browsePrevious) {
            browsePrevious.addEventListener('click', () => {
                document.getElementById('previous-pdf-input')?.click();
            });
        }

        // Enhance the original Start button with DIRECT validation and execution
        const startButton = document.getElementById('start-payroll-audit');
        if (startButton) {
            startButton.addEventListener('click', () => {
                // Direct implementation - no dependency on external function
                console.log('🚀 Start Enhanced Payroll button clicked - direct implementation');
                
                // Get required file paths
                const currentPdfPath = window.auditCurrentPdfPath || document.getElementById('current-pdf-input')?.dataset?.path;
                const previousPdfPath = window.auditPreviousPdfPath || document.getElementById('previous-pdf-input')?.dataset?.path;
                
                // Check files
                if (!currentPdfPath || !previousPdfPath) {
                    alert('Please select both current and previous payroll files before starting the audit.');
                    return;
                }
                
                // Get signature information
                const signatureName = document.getElementById('signature-name')?.value?.trim();
                const signatureDesignation = document.getElementById('signature-designation')?.value?.trim();
                
                if (!signatureName || !signatureDesignation) {
                    alert('Please enter your name and designation for the report signature.');
                    return;
                }
                
                // Hide start button
                startButton.style.display = 'none';
                
                // Show processing indicator
                this.showProcessingIndicator('Starting Enhanced Audit...');
                
                // Call backend API directly using window.api
                if (window.api && typeof window.api.enhancedPayrollAudit === 'function') {
                    try {
                        console.log('✅ Calling backend API directly: enhancedPayrollAudit');
                        console.log('Current PDF path:', currentPdfPath);
                        console.log('Previous PDF path:', previousPdfPath);
                        
                        // Create audit data object
                        const auditData = {
                            currentMonth: document.getElementById('current-month')?.value || '',
                            currentYear: document.getElementById('current-year')?.value || '',
                            previousMonth: document.getElementById('previous-month')?.value || '',
                            previousYear: document.getElementById('previous-year')?.value || '',
                            signatureName: signatureName,
                            signatureDesignation: signatureDesignation,
                            reportType: document.getElementById('report-type-select')?.value || 'traditional'
                        };
                        
                        // Call NEW phased process manager API
                        window.api.enhancedPayrollAudit(currentPdfPath, previousPdfPath, auditData)
                            .then(result => {
                                console.log('🎉 Phased audit workflow completed:', result);

                                if (result && result.success) {
                                    if (result.ready_for_user_review) {
                                        console.log('📋 Workflow ready for user review - pre-reporting UI should load automatically');
                                        // Pre-reporting UI will be loaded automatically via phase update mechanism
                                    } else if (result.phase === 'COMPLETED') {
                                        console.log('✅ Complete workflow finished');
                                        if (window.contentSwitchingManager) {
                                            window.contentSwitchingManager.switchToPhase('completed');
                                        }
                                    }
                                } else {
                                    throw new Error(result?.error || 'Workflow failed');
                                }
                            })
                            .catch(error => {
                                console.error('❌ Error during phased audit:', error);
                                alert(`Error during payroll audit: ${error.message}`);
                                startButton.style.display = 'block';
                            });
                    } catch (error) {
                        console.error('❌ Error starting audit:', error);
                        alert(`An error occurred: ${error.message}`);
                        startButton.style.display = 'block';
                    }
                } else {
                    console.error('❌ window.api.enhancedPayrollAudit not available');
                    alert('Error: Payroll audit API not available. Please reload the application.');
                    startButton.style.display = 'block';
                }
            });
        }
    }

    /**
     * Add minimal styles without overriding original design
     */
    addMinimalStyles() {
        if (document.getElementById('content-switching-minimal-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'content-switching-minimal-styles';
        styles.textContent = `
            /* Minimal enhancements to preserve original design */
            .processing-phase-indicator {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(26, 35, 126, 0.9);
                color: white;
                padding: 10px 15px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                display: none;
            }

            .processing-phase-indicator.active {
                display: block;
            }
        `;
        document.head.appendChild(styles);
    }

    /**
     * Start enhanced audit using original design with improved error handling and function polling
     */
    startEnhancedAudit() {
        console.log('🚀 Starting enhanced audit from content switching manager');

        try {
            // Debug information to help troubleshoot
            console.log('DEBUG - Current path:', window.auditCurrentPdfPath);
            console.log('DEBUG - Previous path:', window.auditPreviousPdfPath);
            console.log('DEBUG - startPayrollAuditProcess available in window:', typeof window.startPayrollAuditProcess === 'function');
            console.log('DEBUG - startPayrollAuditProcess available globally:', typeof startPayrollAuditProcess === 'function');

            // FIXED: Use the main system's global variables instead of hidden inputs
            // Check if files are selected using the main system's variables
            if (!window.auditCurrentPdfPath || !window.auditPreviousPdfPath) {
                alert('Please select both current and previous payroll PDF files before starting the audit.');
                return;
            }

            const signatureName = document.getElementById('signature-name')?.value?.trim();
            const signatureDesignation = document.getElementById('signature-designation')?.value?.trim();

            if (!signatureName || !signatureDesignation) {
                alert('Please complete the signature name and designation fields before starting the audit.');
                return;
            }

            console.log('✅ All requirements met, starting payroll audit process');

            // Show processing indicator
            this.showProcessingIndicator('Starting Enhanced Audit...');

            // If window.startPayrollAuditProcess is not defined, let's try to find it
            if (typeof window.startPayrollAuditProcess !== 'function') {
                console.log('🐛 startPayrollAuditProcess not found in window, checking alternatives...');
                
                // Try the global scope
                if (typeof startPayrollAuditProcess === 'function') {
                    console.log('🐛 Found startPayrollAuditProcess in global scope, copying to window');
                    window.startPayrollAuditProcess = startPayrollAuditProcess;
                } else {
                    // Try polling for the function (it might be defined later)
                    console.log('🐛 Polling for startPayrollAuditProcess function...');
                    this.pollForStartPayrollFunction();
                    return; // Exit and let the polling handle it
                }
            }

            // FIXED: Call the correct main function with more robust handling
            if (typeof window.startPayrollAuditProcess === 'function') {
                console.log('✅ Calling window.startPayrollAuditProcess()');
                try {
                    window.startPayrollAuditProcess();
                } catch (callError) {
                    console.error('❌ Error calling window.startPayrollAuditProcess:', callError);
                    alert(`Error starting payroll audit process: ${callError.message}. Please try again.`);
                }
            } else {
                console.error('❌ startPayrollAuditProcess function not available after attempted fixes');
                alert('Error: Payroll audit function not available. The system will try again in a moment...');
                this.pollForStartPayrollFunction();
            }
        } catch (error) {
            console.error('❌ Error in startEnhancedAudit method:', error);
            alert(`An unexpected error occurred: ${error.message}. Please reload the application.`);
        }
    }

    /**
     * Poll for the startPayrollAuditProcess function to become available
     */
    pollForStartPayrollFunction() {
        console.log('🕐 Starting to poll for startPayrollAuditProcess function...');
        let attemptCount = 0;
        const maxAttempts = 10;
        const pollInterval = 500; // 500ms

        const pollTimer = setInterval(() => {
            attemptCount++;
            console.log(`🕐 Poll attempt ${attemptCount}/${maxAttempts} for startPayrollAuditProcess...`);

            if (typeof window.startPayrollAuditProcess === 'function') {
                clearInterval(pollTimer);
                console.log('✅ Found startPayrollAuditProcess function during polling!');
                this.showProcessingIndicator('Function found! Starting audit...');
                setTimeout(() => {
                    try {
                        window.startPayrollAuditProcess();
                    } catch (callError) {
                        console.error('❌ Error calling window.startPayrollAuditProcess during poll callback:', callError);
                        alert(`Error starting payroll audit process: ${callError.message}. Please try again.`);
                    }
                }, 100);
                return;
            }

            // Check if we've tried too many times
            if (attemptCount >= maxAttempts) {
                clearInterval(pollTimer);
                console.error('❌ Failed to find startPayrollAuditProcess after', maxAttempts, 'attempts');
                alert('Error: Could not start the payroll audit process. Please reload the application and try again.');
                this.hideProcessingIndicator();
            }
        }, pollInterval);
    }

    /**
     * Show processing indicator
     */
    showProcessingIndicator(message) {
        let indicator = document.querySelector('.processing-phase-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'processing-phase-indicator';
            document.body.appendChild(indicator);
        }

        indicator.textContent = message;
        indicator.classList.add('active');
    }

    /**
     * Hide processing indicator
     */
    hideProcessingIndicator() {
        const indicator = document.querySelector('.processing-phase-indicator');
        if (indicator) {
            indicator.classList.remove('active');
        }
    }

    /**
     * Create phase steps indicator
     */
    createPhaseSteps() {
        const phaseNames = {
            'extraction': 'Extraction',
            'pre-auditing': 'Pre-Auditing',
            'tracker-feeding': 'Tracker Feeding',
            'auto-learning': 'Auto-Learning',
            'pre-reporting': 'Pre-Reporting',
            'report-generation': 'Report Generation'
        };

        return this.phases.map((phase, index) => `
            <div class="phase-step" id="step-${phase}" data-phase="${phase}">
                <div class="step-number">${index + 1}</div>
                <div class="step-content">
                    <div class="step-title">${phaseNames[phase]}</div>
                    <div class="step-status">Pending</div>
                </div>
                <div class="step-progress">
                    <div class="step-progress-fill"></div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Create content panels for each phase
     */
    createContentPanels() {
        return `
            <!-- Phase 1: Extraction -->
            <div id="extraction-panel" class="content-panel active" data-phase="extraction">
                <div class="panel-header">
                    <h3>📄 Data Extraction Phase</h3>
                    <div class="panel-status">Ready</div>
                </div>
                <div class="panel-content">
                    <div class="extraction-content">
                        <div class="file-selection">
                            <h4>Select Payroll Files</h4>
                            <div class="file-inputs">
                                <div class="file-input-group">
                                    <label>Current Payroll PDF:</label>
                                    <input type="file" id="current-pdf-input" accept=".pdf">
                                    <span class="file-status" id="current-file-status">No file selected</span>
                                </div>
                                <div class="file-input-group">
                                    <label>Previous Payroll PDF:</label>
                                    <input type="file" id="previous-pdf-input" accept=".pdf">
                                    <span class="file-status" id="previous-file-status">No file selected</span>
                                </div>
                            </div>
                            <button class="btn btn-primary" id="start-extraction-btn" disabled>
                                Start Extraction
                            </button>
                        </div>
                        <!-- Extraction progress panel completely removed - using main progress panel instead -->
                    </div>
                </div>
            </div>

            <!-- Phase 2: Comparison -->
            <div id="comparison-panel" class="content-panel" data-phase="comparison">
                <div class="panel-header">
                    <h3>🔍 Comparison & Analysis Phase</h3>
                    <div class="panel-status">Waiting</div>
                </div>
                <div class="panel-content">
                    <div class="comparison-content">
                        <div class="comparison-progress" id="comparison-progress">
                            <div class="progress-message">Waiting for extraction to complete...</div>
                        </div>
                        <div class="comparison-results" id="comparison-results" style="display: none;">
                            <!-- Comparison results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase 3: Tracker & Learning -->
            <div id="tracker-learning-panel" class="content-panel" data-phase="tracker-learning">
                <div class="panel-header">
                    <h3>📊 Tracker & Learning Phase</h3>
                    <div class="panel-status">Waiting</div>
                </div>
                <div class="panel-content">
                    <div class="tracker-learning-content">
                        <div class="tracker-section">
                            <h4>🏦 Tracker Table Feeding</h4>
                            <div class="tracker-progress" id="tracker-progress">
                                <div class="progress-item">
                                    <span class="progress-label">NEW Loans Detection:</span>
                                    <span class="progress-status" id="loans-status">Pending</span>
                                </div>
                                <div class="progress-item">
                                    <span class="progress-label">NEW Motor Vehicles Detection:</span>
                                    <span class="progress-status" id="vehicles-status">Pending</span>
                                </div>
                                <div class="progress-item">
                                    <span class="progress-label">Database Feeding:</span>
                                    <span class="progress-status" id="database-status">Pending</span>
                                </div>
                            </div>
                        </div>
                        <div class="learning-section">
                            <h4>🧠 Auto Learning</h4>
                            <div class="learning-progress" id="learning-progress">
                                <div class="progress-item">
                                    <span class="progress-label">Dictionary Analysis:</span>
                                    <span class="progress-status" id="dictionary-status">Pending</span>
                                </div>
                                <div class="progress-item">
                                    <span class="progress-label">New Items Detection:</span>
                                    <span class="progress-status" id="new-items-status">Pending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase 4: Pre-reporting -->
            <div id="pre-reporting-panel" class="content-panel" data-phase="pre-reporting">
                <div class="panel-header">
                    <h3>📋 Pre-reporting Phase</h3>
                    <div class="panel-status">Waiting</div>
                </div>
                <div class="panel-content">
                    <div class="pre-reporting-content" id="pre-reporting-content">
                        <!-- Pre-reporting interface will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Phase 5: Report Generation -->
            <div id="report-generation-panel" class="content-panel" data-phase="report-generation">
                <div class="panel-header">
                    <h3>📈 Report Generation Phase</h3>
                    <div class="panel-status">Waiting</div>
                </div>
                <div class="panel-content">
                    <div class="report-generation-content">
                        <div class="report-progress" id="report-progress">
                            <div class="progress-message">Waiting for pre-reporting to complete...</div>
                        </div>
                        <div class="report-results" id="report-results" style="display: none;">
                            <!-- Report results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Switch to a specific phase
     */
    switchToPhase(phaseName) {
        if (!this.phases.includes(phaseName)) {
            console.error(`Invalid phase: ${phaseName}`);
            return;
        }

        // Use enhanced progress panel instead of old progress section
        const enhancedProgressPanel = document.getElementById('enhanced-progress-panel');
        if (enhancedProgressPanel) {
            enhancedProgressPanel.style.display = 'block';

            // Update enhanced progress panel with phase information
            if (typeof updateEnhancedPhaseInfo === 'function') {
                const readablePhase = phaseName.replace(/-/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
                updateEnhancedPhaseInfo(phaseName.toUpperCase(), `Processing ${readablePhase} Phase...`);
            }
        }

        // Hide old progress section completely
        const oldProgressSection = document.getElementById('processing-progress-section');
        if (oldProgressSection) {
            oldProgressSection.style.display = 'none';
        }

        // Hide all panels
        document.querySelectorAll('.content-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Show target panel
        const targetPanel = document.getElementById(`${phaseName}-panel`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }

        // Update phase steps
        this.updatePhaseSteps(phaseName);

        // Special handling for pre-reporting phase
        if (phaseName === 'pre-reporting') {
            this.initializePreReportingPhase();
        }

        this.currentPhase = phaseName;
        console.log(`📋 Switched to phase: ${phaseName}`);
    }

    /**
     * Initialize pre-reporting phase with interactive UI
     */
    initializePreReportingPhase() {
        console.log('📋 Initializing pre-reporting phase...');

        // Load interactive pre-reporting script if not already loaded
        if (!window.interactivePreReporting) {
            const script = document.createElement('script');
            script.src = './ui/interactive_pre_reporting.js';
            script.onload = () => {
                console.log('✅ Interactive pre-reporting script loaded');
                this.setupPreReportingInterface();
            };
            script.onerror = () => {
                console.error('❌ Failed to load interactive pre-reporting script');
                this.showPreReportingError();
            };
            document.head.appendChild(script);
        } else {
            this.setupPreReportingInterface();
        }
    }

    /**
     * Setup pre-reporting interface with data
     */
    setupPreReportingInterface() {
        // Update panel status
        const panelStatus = document.querySelector('#pre-reporting-panel .panel-status');
        if (panelStatus) {
            panelStatus.textContent = 'Ready for Review';
            panelStatus.style.background = '#28a745';
            panelStatus.style.color = 'white';
        }

        // Wait for analyzed changes data from the backend
        this.waitForPreReportingData();
    }

    /**
     * Wait for pre-reporting data from backend
     */
    waitForPreReportingData() {
        // Listen for pre-reporting data
        if (window.appEvents) {
            window.appEvents.on('pre-reporting-data-ready', (data) => {
                console.log('📊 Pre-reporting data received:', data);
                this.loadPreReportingInterface(data);
            });
        }

        // Show loading state
        const content = document.getElementById('pre-reporting-content');
        if (content) {
            content.innerHTML = `
                <div class="pre-reporting-loading">
                    <div class="loading-spinner"></div>
                    <h4>📊 Analyzing Changes...</h4>
                    <p>Categorizing changes by priority and bulk size for your review</p>
                    <button onclick="window.contentSwitchingManager.loadPreReportingFromDatabase()"
                            class="btn btn-primary mt-3">
                        Load Pre-reporting Data
                    </button>
                </div>
            `;
        }

        // Also try to load data from database after a delay
        setTimeout(() => {
            this.loadPreReportingFromDatabase();
        }, 3000);
    }

    /**
     * Load pre-reporting data directly from database (fallback method)
     */
    async loadPreReportingFromDatabase(sessionId = null) {
        try {
            console.log('📊 Attempting to load pre-reporting data from database...', sessionId ? `Session: ${sessionId}` : 'Latest');

            // Check if API is available
            if (!window.api || !window.api.invoke) {
                console.log('📊 API not available, skipping database load');
                this.showPreReportingError('API not available');
                return;
            }

            // Call backend to get pre-reporting data
            const result = sessionId
                ? await window.api.invoke('get-pre-reporting-data', sessionId)
                : await window.api.invoke('get-latest-pre-reporting-data');

            if (result && result.success && result.data) {
                console.log('📊 Pre-reporting data loaded from database:', result.data);
                this.loadPreReportingInterface(result.data);
            } else {
                console.log('📊 No pre-reporting data available in database');
                this.showPreReportingError('No pre-reporting data available. Please run the payroll audit first.');
            }
        } catch (error) {
            console.error('📊 Error loading pre-reporting data from database:', error);
            this.showPreReportingError('Error loading pre-reporting data.');
        }
    }

    /**
     * Handle pre-reporting data ready from backend
     */
    handlePreReportingDataReady(data) {
        console.log('📊 Content switching manager received pre-reporting data:', data);

        // Switch to pre-reporting phase if not already there
        this.switchToPhase('pre-reporting');

        // The backend sends a summary, we need to load the full data
        if (data.session_id) {
            this.loadPreReportingFromDatabase(data.session_id);
        } else {
            // Fallback: try to load latest data
            this.loadPreReportingFromDatabase();
        }
    }

    /**
     * Load pre-reporting interface with analyzed data
     */
    loadPreReportingInterface(preReportingData) {
        console.log('📊 Loading pre-reporting interface with data:', preReportingData);
        console.log('📊 Data keys:', Object.keys(preReportingData));

        if (window.interactivePreReporting) {
            // Handle different possible data structures
            let analyzedChanges = preReportingData.analyzed_changes ||
                                preReportingData.changes ||
                                preReportingData;

            console.log('📊 Analyzed changes:', analyzedChanges);
            window.interactivePreReporting.initialize(analyzedChanges);

            // Update progress
            this.updatePhaseProgress('pre-reporting', 100, 'Ready for user review');

            console.log('✅ Pre-reporting interface loaded successfully');
        } else {
            console.error('❌ Interactive pre-reporting not available');
            this.showPreReportingError();
        }
    }

    /**
     * Show pre-reporting error state
     */
    showPreReportingError() {
        const content = document.getElementById('pre-reporting-content');
        if (content) {
            content.innerHTML = `
                <div class="pre-reporting-error">
                    <h4>❌ Pre-reporting Interface Error</h4>
                    <p>Unable to load the interactive pre-reporting interface.</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        🔄 Reload Application
                    </button>
                </div>
            `;
        }
    }

    /**
     * Handle successful completion of the payroll audit process
     * @param {string} message - Completion message to display
     */
    handleProcessCompletion(message) {
        console.log('🏁 Process completed:', message);
        
        // Update the enhanced progress panel to show completion
        const progressFillEnhanced = document.getElementById('progress-fill-enhanced');
        const progressPercentageEnhanced = document.getElementById('progress-percentage-enhanced');
        const progressStatusEnhanced = document.getElementById('progress-status-enhanced');

        if (progressFillEnhanced) {
            progressFillEnhanced.style.width = '100%';
            progressFillEnhanced.classList.add('completed');
        }

        if (progressPercentageEnhanced) {
            progressPercentageEnhanced.textContent = '100%';
        }

        if (progressStatusEnhanced) {
            progressStatusEnhanced.textContent = message || 'Process completed successfully!';
        }

        // Add completion activity
        if (typeof addRealTimeActivity === 'function') {
            addRealTimeActivity('SYSTEM', message || 'Process completed successfully!', 100);
        }

        // Stop the processing timer
        if (typeof stopProcessingTimer === 'function') {
            stopProcessingTimer();
        }
        
        // Update enhanced progress panel phase indicators as completed
        document.querySelectorAll('.phase-indicator').forEach(indicator => {
            indicator.classList.add('completed');
            indicator.classList.remove('active');
            const statusElement = indicator.querySelector('.phase-status');
            if (statusElement) {
                statusElement.textContent = 'Completed';
            }
        });
        
        // After 3 seconds, hide the enhanced progress panel and show success message
        setTimeout(() => {
            // Hide the enhanced progress panel
            const enhancedProgressPanel = document.getElementById('enhanced-progress-panel');
            if (enhancedProgressPanel) {
                enhancedProgressPanel.style.display = 'none';
            }

            // Also hide old progress panel if it exists
            const oldProgressPanel = document.getElementById('processing-progress-section');
            if (oldProgressPanel) {
                oldProgressPanel.style.display = 'none';
            }

            // Show success message
            this.showSuccessMessage(message || 'Payroll audit completed successfully!');

            // Switch to the report-viewer phase
            this.switchToPhase('report-viewer');
        }, 3000);
    }
    
    /**
     * Show a success message after process completion
     * @param {string} message - Message to display
     */
    showSuccessMessage(message) {
        // Get or create success message container
        let successContainer = document.getElementById('process-success-container');
        if (!successContainer) {
            successContainer = document.createElement('div');
            successContainer.id = 'process-success-container';
            successContainer.className = 'success-message-container';
            
            // Add to the payroll audit tab content
            const tabContent = document.getElementById('payroll-audit-tab');
            if (tabContent) {
                tabContent.appendChild(successContainer);
            }
        }
        
        // Set the success message
        successContainer.innerHTML = `
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <h3>Process Complete</h3>
                <p>${message}</p>
                <button id="view-reports-btn" class="btn btn-primary">View Reports</button>
            </div>
        `;
        
        // Show the container
        successContainer.style.display = 'flex';
        
        // Add event listener to the view reports button
        const viewReportsBtn = document.getElementById('view-reports-btn');
        if (viewReportsBtn) {
            viewReportsBtn.addEventListener('click', () => {
                this.switchToPhase('report-viewer');
                successContainer.style.display = 'none';
            });
        }
    }
    
    /**
     * Update phase steps indicator - now uses enhanced progress panel indicators
     */
    updatePhaseSteps(currentPhase) {
        // Update enhanced progress panel phase indicators
        if (typeof updatePhaseIndicators === 'function') {
            updatePhaseIndicators(currentPhase.toUpperCase());
        }

        // Legacy support for any remaining old phase steps (should be removed)
        document.querySelectorAll('.phase-step').forEach(step => {
            const phase = step.dataset.phase;
            if (!phase) return;

            if (phase === currentPhase) {
                step.classList.add('active');
                step.classList.remove('completed');
                const statusElement = step.querySelector('.step-status');
                if (statusElement) {
                    statusElement.textContent = 'In Progress';
                }
            } else if (this.phases.indexOf(phase) < this.phases.indexOf(currentPhase)) {
                step.classList.remove('active');
                step.classList.add('completed');
                const statusElement = step.querySelector('.step-status');
                if (statusElement) {
                    statusElement.textContent = 'Completed';
                }
            } else {
                step.classList.remove('active', 'completed');
                const statusElement = step.querySelector('.step-status');
                if (statusElement) {
                    statusElement.textContent = 'Pending';
                }
            }
        });
    }
    
    /**
     * Update progress for the current phase in the main progress panel
     * @param {string} phase - The phase name
     * @param {number} percentage - Progress percentage (0-100)
     * @param {string} message - Progress message
     */
    updatePhaseProgress(phase, percentage, message) {
        if (!this.phases.includes(phase)) {
            console.error(`Invalid phase: ${phase}`);
            return;
        }
        
        // Check if process is complete (100% in report-generation phase)
        if (phase === 'report-generation' && percentage >= 100) {
            this.handleProcessCompletion(message || 'Process completed successfully!');
            return;
        }
        
        // Calculate overall percentage based on phase progress ranges
        const phaseRange = this.phaseProgress[phase] || { start: 0, end: 100 };
        const overallPercentage = Math.round(phaseRange.start + (percentage / 100 * (phaseRange.end - phaseRange.start)));
        
        // Update the enhanced progress panel (A2)
        const progressFillEnhanced = document.getElementById('progress-fill-enhanced');
        const progressPercentageEnhanced = document.getElementById('progress-percentage-enhanced');
        const progressStatusEnhanced = document.getElementById('progress-status-enhanced');

        if (progressFillEnhanced) {
            progressFillEnhanced.style.width = `${overallPercentage}%`;
        }

        if (progressPercentageEnhanced) {
            progressPercentageEnhanced.textContent = `${overallPercentage}%`;
        }

        if (progressStatusEnhanced && message) {
            progressStatusEnhanced.textContent = message;
        }

        // Also update phase information and add activity
        if (typeof updateEnhancedPhaseInfo === 'function') {
            updateEnhancedPhaseInfo(phase.toUpperCase(), message);
        }

        if (typeof addRealTimeActivity === 'function') {
            addRealTimeActivity(phase.toUpperCase(), message, overallPercentage);
        }
        
        // Update operation details if provided
        if (message) {
            const operationDetails = document.getElementById('operation-details');
            if (operationDetails) {
                operationDetails.textContent = message;
            }
        }
        
        console.log(`📊 Phase progress updated: ${phase} - ${percentage}% - ${message}`);
    }

    /**
     * Update overall progress
     */
    updateProgress(percentage, message) {
        const progressFill = document.getElementById('overall-progress-fill');
        const progressText = document.getElementById('overall-progress-text');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${percentage}% - ${message}`;
        }
    }

    /**
     * Update phase-specific progress
     */
    updatePhaseProgress(phaseName, percentage, message) {
        const phaseRange = this.phaseProgress[phaseName];
        if (!phaseRange) return;

        // Calculate overall progress
        const overallProgress = phaseRange.start + (percentage / 100) * (phaseRange.end - phaseRange.start);
        this.updateProgress(Math.round(overallProgress), message);

        // Update phase step progress
        const stepElement = document.getElementById(`step-${phaseName}`);
        if (stepElement) {
            const progressFill = stepElement.querySelector('.step-progress-fill');
            const statusElement = stepElement.querySelector('.step-status');
            
            if (progressFill) {
                progressFill.style.width = `${percentage}%`;
            }
            
            if (statusElement) {
                statusElement.textContent = percentage === 100 ? 'Completed' : 'Processing...';
            }
        }
    }

    /**
     * Add CSS styles for content switching
     */
    addContentSwitchingStyles() {
        if (document.getElementById('content-switching-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'content-switching-styles';
        styles.textContent = `
            .content-switching-container {
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .phase-progress-indicator {
                background: white;
                border-bottom: 1px solid #e0e0e0;
                padding: 20px;
            }

            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }

            .overall-progress {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .progress-bar {
                width: 200px;
                height: 8px;
                background: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #45a049);
                transition: width 0.3s ease;
            }

            .phase-steps {
                display: flex;
                gap: 20px;
                overflow-x: auto;
            }

            .phase-step {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 10px 15px;
                border-radius: 8px;
                background: #f8f9fa;
                min-width: 200px;
                position: relative;
            }

            .phase-step.active {
                background: #e3f2fd;
                border: 2px solid #2196F3;
            }

            .phase-step.completed {
                background: #e8f5e8;
                border: 2px solid #4CAF50;
            }

            .step-number {
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: #ddd;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
            }

            .phase-step.active .step-number {
                background: #2196F3;
                color: white;
            }

            .phase-step.completed .step-number {
                background: #4CAF50;
                color: white;
            }

            .step-content {
                flex: 1;
            }

            .step-title {
                font-weight: 600;
                margin-bottom: 4px;
            }

            .step-status {
                font-size: 12px;
                color: #666;
            }

            .content-panels {
                flex: 1;
                position: relative;
                overflow: hidden;
            }

            .content-panel {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
                background: white;
                display: flex;
                flex-direction: column;
            }

            .content-panel.active {
                opacity: 1;
                visibility: visible;
            }

            .panel-header {
                background: #f8f9fa;
                padding: 15px 20px;
                border-bottom: 1px solid #e0e0e0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .panel-content {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }

            .panel-status {
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
                background: #e0e0e0;
                color: #666;
            }

            .file-input-group {
                margin-bottom: 15px;
            }

            .file-input-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: 600;
            }

            .file-status {
                display: block;
                margin-top: 5px;
                font-size: 12px;
                color: #666;
            }

            .progress-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;
            }

            .progress-status {
                padding: 2px 8px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 600;
            }

            .progress-status:contains("Pending") {
                background: #fff3cd;
                color: #856404;
            }

            .progress-status:contains("Processing") {
                background: #cce5ff;
                color: #004085;
            }

            .progress-status:contains("Completed") {
                background: #d4edda;
                color: #155724;
            }

            .pre-reporting-loading {
                text-align: center;
                padding: 60px 20px;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .pre-reporting-loading h4 {
                color: #007bff;
                margin-bottom: 10px;
            }

            .pre-reporting-loading p {
                color: #6c757d;
                margin: 0;
            }

            .pre-reporting-error {
                text-align: center;
                padding: 60px 20px;
                color: #dc3545;
            }

            .pre-reporting-error h4 {
                margin-bottom: 15px;
            }

            .pre-reporting-error p {
                margin-bottom: 20px;
                color: #6c757d;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // File input change handlers
        document.getElementById('current-pdf-input')?.addEventListener('change', (e) => {
            this.handleFileSelection(e, 'current');
        });

        document.getElementById('previous-pdf-input')?.addEventListener('change', (e) => {
            this.handleFileSelection(e, 'previous');
        });

        // Start extraction button
        document.getElementById('start-extraction-btn')?.addEventListener('click', () => {
            this.startExtractionProcess();
        });
    }

    /**
     * Handle file selection
     */
    handleFileSelection(event, type) {
        const file = event.target.files[0];
        const statusElement = document.getElementById(`${type}-file-status`);
        
        if (file && file.type === 'application/pdf') {
            statusElement.textContent = `Selected: ${file.name}`;
            statusElement.style.color = '#4CAF50';
        } else {
            statusElement.textContent = 'Please select a valid PDF file';
            statusElement.style.color = '#f44336';
        }

        this.checkExtractionReadiness();
    }

    /**
     * Check if extraction can be started
     */
    checkExtractionReadiness() {
        const currentFile = document.getElementById('current-pdf-input').files[0];
        const previousFile = document.getElementById('previous-pdf-input').files[0];
        const startButton = document.getElementById('start-extraction-btn');

        if (currentFile && previousFile && 
            currentFile.type === 'application/pdf' && 
            previousFile.type === 'application/pdf') {
            startButton.disabled = false;
        } else {
            startButton.disabled = true;
        }
    }

    /**
     * Start extraction process
     */
    async startExtractionProcess() {
        console.log('🚀 Starting phased extraction process...');
        
        // Switch to main progress view instead of extraction-specific view
        document.querySelector('.file-selection').style.display = 'none';
        
        // Ensure the main processing progress section is visible
        const mainProgressSection = document.getElementById('processing-progress-section');
        if (mainProgressSection) {
            mainProgressSection.style.display = 'block';
        }
        
        // Remove any extraction panels that might exist
        document.querySelectorAll('.extraction-panel, .extraction-progress, [id^="extraction"]').forEach(panel => {
            console.log(`Content switching: Removing extraction panel: ${panel.id || 'unnamed panel'}`);
            if (panel.parentNode) {
                panel.parentNode.removeChild(panel);
            }
        });
        
        // Update the main panel status instead
        const mainPanelStatus = document.querySelector('#processing-progress-section .panel-status');
        if (mainPanelStatus) {
            mainPanelStatus.textContent = 'Processing';
            mainPanelStatus.style.background = '#cce5ff';
            mainPanelStatus.style.color = '#004085';
        }

        // Start the phased process
        // This would integrate with the PhasedProcessManager
        this.simulateExtractionProcess();
    }

    /**
     * Simulate extraction process (for demonstration)
     */
    async simulateExtractionProcess() {
        // Phase 1: Extraction
        this.updatePhaseProgress('extraction', 0, 'Starting extraction...');
        await this.delay(1000);
        
        this.updatePhaseProgress('extraction', 50, 'Extracting current payroll...');
        await this.delay(2000);
        
        this.updatePhaseProgress('extraction', 100, 'Extraction completed');
        await this.delay(500);
        
        // Switch to comparison phase
        this.switchToPhase('comparison');
        this.updatePhaseProgress('comparison', 0, 'Starting comparison...');
        await this.delay(1000);
        
        this.updatePhaseProgress('comparison', 100, 'Comparison completed');
        await this.delay(500);
        
        // Switch to tracker & learning phase
        this.switchToPhase('tracker-learning');
        this.updateTrackerProgress();
    }

    /**
     * Update tracker progress
     */
    async updateTrackerProgress() {
        document.getElementById('loans-status').textContent = 'Processing...';
        await this.delay(1000);
        document.getElementById('loans-status').textContent = 'Completed';
        
        document.getElementById('vehicles-status').textContent = 'Processing...';
        await this.delay(1000);
        document.getElementById('vehicles-status').textContent = 'Completed';
        
        document.getElementById('database-status').textContent = 'Processing...';
        await this.delay(1000);
        document.getElementById('database-status').textContent = 'Completed';
        
        document.getElementById('dictionary-status').textContent = 'Processing...';
        await this.delay(1000);
        document.getElementById('dictionary-status').textContent = 'Completed';
        
        document.getElementById('new-items-status').textContent = 'Processing...';
        await this.delay(1000);
        document.getElementById('new-items-status').textContent = 'Completed';
        
        this.updatePhaseProgress('tracker-learning', 100, 'Tracker & Learning completed');
        
        // Switch to pre-reporting
        this.switchToPhase('pre-reporting');
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
  };
  
  // Create a singleton instance for immediate use if needed
  if (window.ContentSwitchingManager && !window.contentSwitchingManager) {
    window.contentSwitchingManager = new window.ContentSwitchingManager();
  }

  console.log('✅ ContentSwitchingManager defined:', typeof window.ContentSwitchingManager);
  console.log('✅ contentSwitchingManager instance created:', window.contentSwitchingManager ? 'yes' : 'no');

  // Dispatch an event to notify that ContentSwitchingManager is ready
  const readyEvent = new CustomEvent('contentSwitchingManagerReady');
  document.dispatchEvent(readyEvent);
  }
})();
*/

// FALLBACK NOTICE: Using fallback ContentSwitchingManager from renderer.js
console.log('📋 Real ContentSwitchingManager commented out - using fallback version');

console.log('✅ ContentSwitchingManager initialization module loaded');
