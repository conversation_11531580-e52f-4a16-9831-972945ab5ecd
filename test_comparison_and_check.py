#!/usr/bin/env python3
"""
Test comparison and immediately check database
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_comparison_and_check():
    """Test comparison and immediately check database"""
    print("🔍 TESTING COMPARISON AND CHECKING DATABASE")
    print("=" * 60)
    
    try:
        # Import the manager
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        # Get the latest session from database
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return
        
        session_id = session_result[0]
        print(f"🎯 Testing session: {session_id}")
        
        # Check initial state
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
        initial_count = cursor.fetchone()[0]
        print(f"📊 Initial comparison results: {initial_count}")
        
        # Create manager and set session
        manager = PhasedProcessManager()
        manager.session_id = session_id
        
        # Load extracted data
        print("\n1. 📊 LOADING EXTRACTED DATA:")
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        print(f"   Current data: {len(current_data)} employees")
        print(f"   Previous data: {len(previous_data)} employees")
        
        # Run comparison
        print("\n2. 🔄 RUNNING COMPARISON:")
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        print(f"   Comparison results: {len(comparison_results)} changes found")
        
        # Check database before storing
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
        before_store_count = cursor.fetchone()[0]
        print(f"   Before storing: {before_store_count} results in database")
        
        # Store results
        print("\n3. 💾 STORING RESULTS:")
        manager._store_comparison_results(comparison_results)
        print("   Results stored")
        
        # Check database immediately after storing
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
        after_store_count = cursor.fetchone()[0]
        print(f"   After storing: {after_store_count} results in database")
        
        # Check total in database
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        total_count = cursor.fetchone()[0]
        print(f"   Total in database: {total_count} results")
        
        # Sample some results
        if after_store_count > 0:
            cursor.execute("""
                SELECT id, employee_id, section_name, item_label, change_type, priority
                FROM comparison_results 
                WHERE session_id = ?
                LIMIT 5
            """, (session_id,))
            
            sample_results = cursor.fetchall()
            print("\n   Sample stored results:")
            for row in sample_results:
                print(f"     {row[0]}: {row[1]} - {row[2]}.{row[3]} ({row[4]}, {row[5]})")
        
        # Test loading the results back
        print("\n4. 🔄 TESTING LOADING RESULTS:")
        loaded_results = manager._load_all_comparison_results()
        print(f"   Loaded results: {len(loaded_results)} changes")
        
        if loaded_results:
            print("   Sample loaded result:")
            sample = loaded_results[0]
            print(f"     {sample['id']}: {sample['employee_id']} - {sample['section_name']}.{sample['item_label']}")
        
        # Test PRE-REPORTING phase
        print("\n5. 🔄 TESTING PRE-REPORTING PHASE:")
        
        options = {
            'report_name': 'Test Report',
            'report_designation': 'Test Designation'
        }
        
        result = manager._phase_pre_reporting(options)
        
        if result:
            print("   ✅ PRE-REPORTING phase completed successfully!")
            
            # Check pre-reporting results
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
            pre_reporting_count = cursor.fetchone()[0]
            print(f"   ✅ Pre-reporting results stored: {pre_reporting_count}")
            
        else:
            print("   ❌ PRE-REPORTING phase failed")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_comparison_and_check()
