#!/usr/bin/env python3
"""
Test database persistence to find the root cause
"""

import sqlite3
import sys
import os
sys.path.append('.')

def test_database_persistence():
    """Test database persistence step by step"""
    print("🔍 TESTING DATABASE PERSISTENCE")
    print("=" * 50)
    
    try:
        # Test 1: Direct SQLite connection
        print("1. Testing direct SQLite connection...")
        
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"   Current session: {current_session}")
        
        # Clear and insert test data
        cursor.execute('DELETE FROM comparison_results WHERE session_id = ?', (current_session,))
        cursor.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority,
             numeric_difference, percentage_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (current_session, 'TEST001', 'Test Employee', 'EARNINGS', 'BASIC SALARY',
              '1000.00', '1100.00', 'INCREASED', 'High', 100.00, 10.0))
        
        conn.commit()
        
        # Verify immediately
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count1 = cursor.fetchone()[0]
        print(f"   Direct insert result: {count1} records")
        
        conn.close()
        
        # Test 2: Verify persistence after connection close
        print("\n2. Testing persistence after connection close...")
        
        conn2 = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor2 = conn2.cursor()
        
        cursor2.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count2 = cursor2.fetchone()[0]
        print(f"   After reconnection: {count2} records")
        
        if count2 > 0:
            print("   ✅ Direct SQLite persistence working")
        else:
            print("   ❌ Direct SQLite persistence failed")
            return False
        
        conn2.close()
        
        # Test 3: PythonDatabaseManager
        print("\n3. Testing PythonDatabaseManager...")
        
        from core.python_database_manager import PythonDatabaseManager
        
        db_manager = PythonDatabaseManager()
        
        # Clear and insert via manager
        db_manager.execute_update('DELETE FROM comparison_results WHERE session_id = ?', (current_session,))
        
        affected_rows = db_manager.execute_update('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority,
             numeric_difference, percentage_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (current_session, 'TEST002', 'Test Employee 2', 'DEDUCTIONS', 'INCOME TAX',
              '200.00', '220.00', 'INCREASED', 'Medium', 20.00, 10.0))
        
        print(f"   Manager insert affected rows: {affected_rows}")
        
        # Query via manager
        results = db_manager.execute_query('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count3 = results[0]['COUNT(*)'] if results and isinstance(results[0], dict) else results[0] if results else 0
        print(f"   Manager query result: {count3} records")
        
        if count3 > 0:
            print("   ✅ PythonDatabaseManager working")
        else:
            print("   ❌ PythonDatabaseManager failed")
            return False
        
        # Test 4: Verify with fresh connection
        print("\n4. Testing with fresh connection...")
        
        conn3 = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor3 = conn3.cursor()
        
        cursor3.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count4 = cursor3.fetchone()[0]
        print(f"   Fresh connection result: {count4} records")
        
        if count4 > 0:
            print("   ✅ Data persisted across connections")
        else:
            print("   ❌ Data lost across connections")
            return False
        
        conn3.close()
        
        # Test 5: PhasedProcessManager database access
        print("\n5. Testing PhasedProcessManager database access...")
        
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=False)
        
        # Query via manager
        manager_results = manager.db_manager.execute_query(
            'SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', 
            (current_session,)
        )
        
        if manager_results:
            if isinstance(manager_results[0], dict):
                count5 = manager_results[0]['COUNT(*)']
            else:
                count5 = manager_results[0]
        else:
            count5 = 0
        
        print(f"   PhasedProcessManager query result: {count5} records")
        
        if count5 > 0:
            print("   ✅ PhasedProcessManager database access working")
            
            # Test the actual get_pre_reporting_data method
            print("\n6. Testing get_pre_reporting_data method...")
            
            result = manager.get_pre_reporting_data(current_session)
            
            print(f"   Method success: {result.get('success')}")
            print(f"   Method data count: {len(result.get('data', []))}")
            
            if result.get('success') and len(result.get('data', [])) > 0:
                print("   ✅ get_pre_reporting_data working!")
                return True
            else:
                print("   ❌ get_pre_reporting_data failed")
                print(f"   Error: {result.get('error', 'Unknown')}")
                
                # Debug the method's query
                print("\n   Debugging method query...")
                debug_results = manager.db_manager.execute_query('''
                    SELECT id, employee_id, employee_name, section_name, item_label,
                           previous_value, current_value, change_type, priority,
                           numeric_difference, percentage_change
                    FROM comparison_results
                    WHERE session_id = ?
                    ORDER BY priority DESC, section_name, employee_id
                ''', (current_session,))
                
                print(f"   Debug query returned: {len(debug_results) if debug_results else 0} rows")
                
                if debug_results:
                    print(f"   Sample row: {debug_results[0]}")
                    print("   ⚠️ Query works but method processing fails")
                else:
                    print("   ❌ Query itself fails in method context")
                
                return False
        else:
            print("   ❌ PhasedProcessManager database access failed")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_persistence()
    
    if success:
        print("\n🎉 DATABASE PERSISTENCE WORKING!")
        print("   The issue is resolved - API should work now.")
    else:
        print("\n⚠️ DATABASE PERSISTENCE ISSUES FOUND")
        print("   Root cause identified for debugging.")
