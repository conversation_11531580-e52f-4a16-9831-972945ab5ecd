#!/usr/bin/env python3
"""
Final PRE-REPORTING fix - regenerate all data and ensure persistence
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def final_pre_reporting_fix():
    """Final PRE-REPORTING fix"""
    print("🔧 FINAL PRE-REPORTING FIX - COMPLETE REGENERATION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 GETTING CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Verify extracted data exists
        print("\n2. 📊 VERIFYING EXTRACTED DATA:")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'current'", (current_session,))
        current_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'previous'", (current_session,))
        previous_count = cursor.fetchone()[0]
        
        print(f"   Current period data: {current_count}")
        print(f"   Previous period data: {previous_count}")
        
        if current_count == 0 or previous_count == 0:
            print("   ❌ Insufficient extracted data")
            return
        
        # 3. Regenerate comparison results
        print("\n3. 🔄 REGENERATING COMPARISON RESULTS:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Clear any existing comparison results for this session
            cursor.execute("DELETE FROM comparison_results WHERE session_id = ?", (current_session,))
            conn.commit()
            print("   ✅ Cleared existing comparison results")
            
            # Load extracted data
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                
                # Generate comparison results
                comparison_results = manager._compare_payroll_data(current_data, previous_data)
                
                if comparison_results:
                    print(f"   ✅ Generated {len(comparison_results)} comparison results")
                    
                    # Store comparison results
                    manager._store_comparison_results(comparison_results)
                    
                    # Verify storage
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                    stored_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
                    new_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Stored {stored_count} comparison results")
                    print(f"   ✅ Found {new_count} NEW items")
                    
                    if stored_count == 0:
                        print("   ❌ Comparison results not stored properly")
                        return
                else:
                    print("   ❌ No comparison results generated")
                    return
            else:
                print("   ❌ Could not load extracted data")
                return
        
        except Exception as e:
            print(f"   ❌ Comparison regeneration failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 4. Generate PRE-REPORTING results
        print("\n4. 🔄 GENERATING PRE-REPORTING RESULTS:")
        
        try:
            # Clear any existing pre-reporting results
            cursor.execute("DELETE FROM pre_reporting_results WHERE session_id = ?", (current_session,))
            conn.commit()
            print("   ✅ Cleared existing pre-reporting results")
            
            # Load comparison results
            cursor.execute("""
                SELECT id, employee_id, employee_name, section_name, item_label,
                       previous_value, current_value, change_type, priority,
                       numeric_difference, percentage_change
                FROM comparison_results 
                WHERE session_id = ?
                ORDER BY priority DESC, section_name, employee_id
            """, (current_session,))
            
            comparison_rows = cursor.fetchall()
            
            if comparison_rows:
                print(f"   ✅ Loaded {len(comparison_rows)} comparison results")
                
                # Convert to proper format
                all_changes = []
                for row in comparison_rows:
                    change = {
                        'id': row[0],
                        'employee_id': row[1],
                        'employee_name': row[2],
                        'section_name': row[3],
                        'item_label': row[4],
                        'previous_value': row[5],
                        'current_value': row[6],
                        'change_type': row[7],
                        'priority': row[8],
                        'numeric_difference': row[9],
                        'percentage_change': row[10]
                    }
                    all_changes.append(change)
                
                # Categorize changes
                categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                print(f"   ✅ Categorized {len(categorized_changes)} changes")
                
                # Apply auto-selection
                auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                print(f"   ✅ Applied auto-selection rules")
                
                # Store pre-reporting results
                manager._store_pre_reporting_results(categorized_changes, auto_selected)
                
                # Verify storage
                cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                pre_reporting_count = cursor.fetchone()[0]
                
                print(f"   ✅ Stored {pre_reporting_count} pre-reporting results")
                
                if pre_reporting_count == 0:
                    print("   ❌ Pre-reporting results not stored properly")
                    return
            else:
                print("   ❌ No comparison results to process")
                return
        
        except Exception as e:
            print(f"   ❌ Pre-reporting generation failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 5. Test UI data methods
        print("\n5. 🧪 TESTING UI DATA METHODS:")
        
        try:
            # Test get_pre_reporting_data
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                
                print(f"   ✅ get_pre_reporting_data: {data_count} items, {total_changes} total")
                
                # Test get_latest_pre_reporting_data
                latest_result = manager.get_latest_pre_reporting_data()
                
                if latest_result.get('success') and latest_result.get('data'):
                    latest_count = len(latest_result.get('data', []))
                    latest_total = latest_result.get('total_changes', 0)
                    latest_session_id = latest_result.get('session_id', 'unknown')
                    
                    print(f"   ✅ get_latest_pre_reporting_data:")
                    print(f"     Session: {latest_session_id}")
                    print(f"     Items: {latest_count}")
                    print(f"     Total: {latest_total}")
                    
                    # Analyze for UI
                    categories = {}
                    auto_selected_count = 0
                    change_types = {}
                    
                    for item in latest_result['data']:
                        # Category analysis
                        category = item.get('bulk_category', 'Unknown')
                        categories[category] = categories.get(category, 0) + 1
                        
                        # Auto-selection analysis
                        if item.get('selected_for_report'):
                            auto_selected_count += 1
                        
                        # Change type analysis
                        change_type = item.get('change_type', 'Unknown')
                        change_types[change_type] = change_types.get(change_type, 0) + 1
                    
                    print(f"   📊 UI Data Analysis:")
                    print(f"     Categories: {list(categories.keys())}")
                    for category, count in categories.items():
                        print(f"       {category}: {count} changes")
                    
                    print(f"     Change types:")
                    for change_type, count in change_types.items():
                        print(f"       {change_type}: {count} changes")
                    
                    print(f"     Auto-selected: {auto_selected_count}")
                    print(f"     Pending review: {latest_count - auto_selected_count}")
                    
                    # Show sample for UI verification
                    if latest_count > 0:
                        sample = latest_result['data'][0]
                        print(f"   📋 Sample UI data:")
                        print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                        print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                        print(f"     Values: {sample.get('previous_value')} → {sample.get('current_value')}")
                        print(f"     Type: {sample.get('change_type')}")
                        print(f"     Category: {sample.get('bulk_category')}")
                        print(f"     Priority: {sample.get('priority')}")
                        print(f"     Selected: {sample.get('selected_for_report')}")
                    
                    print(f"\n🎉 PRE-REPORTING INTERACTIVE UI FULLY OPERATIONAL!")
                    print(f"✅ Session: {current_session}")
                    print(f"✅ Comparison results: {stored_count}")
                    print(f"✅ Pre-reporting results: {pre_reporting_count}")
                    print(f"✅ UI data: {latest_count} changes")
                    print(f"✅ Categories: {len(categories)}")
                    print(f"✅ Auto-selected: {auto_selected_count}")
                    
                    print(f"\n🎯 INTERACTIVE UI READY WITH:")
                    print(f"📋 ✅ {latest_count} changes for user review")
                    print(f"📋 ✅ {len(categories)} bulk categories")
                    print(f"📋 ✅ {auto_selected_count} auto-selected changes")
                    print(f"📋 ✅ {latest_count - auto_selected_count} changes pending user review")
                    print(f"📋 ✅ Interactive selection/deselection")
                    print(f"📋 ✅ Generate Final Report functionality")
                    
                    print(f"\n🚀 UI WILL NOW LOAD INTERACTIVE PRE-REPORTING PAGE!")
                    print(f"🎯 Users can review, select/deselect changes before final report generation")
                    
                else:
                    print(f"   ❌ get_latest_pre_reporting_data failed: {latest_result}")
            else:
                print(f"   ❌ get_pre_reporting_data failed: {result}")
        
        except Exception as e:
            print(f"   ❌ UI data test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Update session status
        print("\n6. 📊 UPDATING SESSION STATUS:")
        
        try:
            from core.session_manager import get_session_manager
            session_manager = get_session_manager()
            
            session_manager.update_phase_status('COMPARISON', 'COMPLETED', stored_count)
            session_manager.update_phase_status('PRE_REPORTING', 'COMPLETED', pre_reporting_count)
            
            print(f"   ✅ Updated session phase statuses")
        except Exception as e:
            print(f"   ⚠️ Could not update session status: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during final fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_pre_reporting_fix()
