#!/usr/bin/env python3
"""
Force fix the comparison_results table schema by completely recreating it
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def force_schema_fix():
    """Force fix the schema by completely recreating the table"""
    print("🔧 FORCE FIXING COMPARISON_RESULTS TABLE SCHEMA")
    print("=" * 60)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return
    
    print(f"📁 Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current table structure
        print("\n1. 📋 CHECKING CURRENT TABLE:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        
        if result:
            print("   Current table definition:")
            print(f"   {result[0]}")
        else:
            print("   Table does not exist")
        
        # 2. Drop table completely
        print("\n2. 🗑️ DROPPING TABLE COMPLETELY:")
        cursor.execute("DROP TABLE IF EXISTS comparison_results")
        conn.commit()
        print("   Table dropped")
        
        # 3. Create new table with correct schema
        print("\n3. 🆕 CREATING NEW TABLE:")
        create_sql = '''
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT CHECK(change_type IN ('NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED')),
                priority TEXT CHECK(priority IN ('HIGH', 'MODERATE', 'LOW')),
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''
        
        cursor.execute(create_sql)
        conn.commit()
        print("   New table created with correct schema")
        
        # 4. Verify new table
        print("\n4. ✅ VERIFYING NEW TABLE:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        
        if result:
            print("   New table definition:")
            print(f"   {result[0]}")
        
        # 5. Test inserting a sample record
        print("\n5. 🧪 TESTING SAMPLE INSERT:")
        test_data = (
            'test_session', 'TEST001', 'Test Employee', 'EARNINGS', 'BASIC SALARY',
            '1000.00', '1100.00', 'INCREASED', 'HIGH', 100.0, 10.0
        )
        
        cursor.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_data)
        
        conn.commit()
        print("   ✅ Sample record inserted successfully")
        
        # Test all change types
        change_types = ['NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED']
        print("\n6. 🧪 TESTING ALL CHANGE TYPES:")
        
        for i, change_type in enumerate(change_types):
            test_data = (
                'test_session', f'TEST{i:03d}', f'Test Employee {i}', 'EARNINGS', 'TEST ITEM',
                '100.00', '200.00', change_type, 'HIGH', 100.0, 100.0
            )
            
            try:
                cursor.execute('''
                    INSERT INTO comparison_results 
                    (session_id, employee_id, employee_name, section_name, item_label,
                     previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', test_data)
                print(f"   ✅ {change_type}: SUCCESS")
            except Exception as e:
                print(f"   ❌ {change_type}: FAILED - {e}")
        
        conn.commit()
        
        # Clean up test data
        cursor.execute("DELETE FROM comparison_results WHERE session_id = 'test_session'")
        conn.commit()
        
        print("\n✅ SCHEMA FIX COMPLETED SUCCESSFULLY!")
        print("   The comparison_results table now supports all change types")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error fixing schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    force_schema_fix()
