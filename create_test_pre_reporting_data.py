#!/usr/bin/env python3
"""
Create test pre-reporting data directly to bypass the comparison issue
"""

import sqlite3
import json

def create_test_pre_reporting_data():
    """Create test pre-reporting data directly"""
    print("🔧 CREATING TEST PRE-REPORTING DATA")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"1. Current session: {current_session}")
        
        # Clear existing comparison results
        cursor.execute('DELETE FROM comparison_results WHERE session_id = ?', (current_session,))
        print("2. Cleared existing comparison results")
        
        # Create test comparison results
        print("3. Creating test comparison results...")
        
        test_data = [
            {
                'employee_id': 'E001',
                'employee_name': '<PERSON>',
                'section_name': 'EARNINGS',
                'item_label': 'BASIC SALARY',
                'previous_value': '5000.00',
                'current_value': '5500.00',
                'change_type': 'INCREASED',
                'priority': 'High',
                'numeric_difference': 500.00,
                'percentage_change': 10.0
            },
            {
                'employee_id': 'E002',
                'employee_name': 'Jane Smith',
                'section_name': 'DEDUCTIONS',
                'item_label': 'INCOME TAX',
                'previous_value': '800.00',
                'current_value': '850.00',
                'change_type': 'INCREASED',
                'priority': 'Medium',
                'numeric_difference': 50.00,
                'percentage_change': 6.25
            },
            {
                'employee_id': 'E003',
                'employee_name': 'Bob Johnson',
                'section_name': 'PERSONAL DETAILS',
                'item_label': 'DEPARTMENT',
                'previous_value': 'IT',
                'current_value': 'HR',
                'change_type': 'CHANGED',
                'priority': 'High',
                'numeric_difference': 0,
                'percentage_change': 0
            },
            {
                'employee_id': 'E004',
                'employee_name': 'Alice Brown',
                'section_name': 'LOANS',
                'item_label': 'CAR LOAN',
                'previous_value': '2000.00',
                'current_value': '',
                'change_type': 'REMOVED',
                'priority': 'Medium',
                'numeric_difference': -2000.00,
                'percentage_change': -100.0
            },
            {
                'employee_id': 'E005',
                'employee_name': 'Charlie Wilson',
                'section_name': 'EARNINGS',
                'item_label': 'OVERTIME',
                'previous_value': '',
                'current_value': '300.00',
                'change_type': 'NEW',
                'priority': 'Low',
                'numeric_difference': 300.00,
                'percentage_change': 0
            }
        ]
        
        # Insert test data
        for i, data in enumerate(test_data):
            cursor.execute('''
                INSERT INTO comparison_results 
                (session_id, employee_id, employee_name, section_name, item_label,
                 previous_value, current_value, change_type, priority,
                 numeric_difference, percentage_change)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                current_session,
                data['employee_id'],
                data['employee_name'],
                data['section_name'],
                data['item_label'],
                data['previous_value'],
                data['current_value'],
                data['change_type'],
                data['priority'],
                data['numeric_difference'],
                data['percentage_change']
            ))
        
        conn.commit()
        print(f"   ✅ Created {len(test_data)} test comparison results")
        
        # Verify data
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count = cursor.fetchone()[0]
        print(f"4. Verification: {count} comparison results in database")
        
        if count > 0:
            print("   ✅ Test data created successfully")
            
            # Test the pre-reporting API
            print("\n5. Testing pre-reporting API...")
            
            # Create a simple test of the get_pre_reporting_data method
            import sys
            import os
            sys.path.append('.')
            
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager(debug_mode=False)
            result = manager.get_pre_reporting_data(current_session)
            
            print(f"   API Result success: {result.get('success')}")
            print(f"   API Data count: {len(result.get('data', []))}")
            print(f"   API Total changes: {result.get('total_changes', 0)}")
            
            if result.get('success') and result.get('data'):
                print("   ✅ PRE-REPORTING API WORKING!")
                
                # Sample the data
                data = result.get('data', [])
                print(f"\n6. Sample API data (first 2 items):")
                for i, item in enumerate(data[:2]):
                    print(f"   {i+1}. {item.get('employee_id')}: {item.get('section_name')}.{item.get('item_label')}")
                    print(f"      {item.get('previous_value')} → {item.get('current_value')} ({item.get('change_type')})")
                    print(f"      Priority: {item.get('priority')}, Category: {item.get('bulk_category')}")
                
                # Test the JSON output for the UI
                print(f"\n7. Testing JSON output for UI...")
                json_output = json.dumps(result, indent=2)
                print(f"   JSON output length: {len(json_output)} characters")
                print("   ✅ JSON serialization working")
                
                return True
            else:
                print("   ❌ PRE-REPORTING API FAILED")
                print(f"   Error: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("   ❌ Test data creation failed")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Test creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_pre_reporting_data()
    
    if success:
        print("\n🎉 TEST PRE-REPORTING DATA CREATED!")
        print("   The Pre-Reporting UI should now work with test data.")
        print("   This proves the API and UI integration is working.")
        print("   The original issue was the comparison data persistence.")
    else:
        print("\n⚠️ TEST DATA CREATION FAILED")
        print("   The issue is deeper than expected.")
