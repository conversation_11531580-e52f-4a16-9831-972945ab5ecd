#!/usr/bin/env python3
"""
Debug PRE-REPORTING query to see why it's returning empty results
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_pre_reporting_query():
    """Debug PRE-REPORTING query"""
    print("🔍 DEBUGGING PRE-REPORTING QUERY")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check table structures
        print("\n2. 📊 TABLE STRUCTURES:")
        
        # Check comparison_results table
        cursor.execute("PRAGMA table_info(comparison_results)")
        comp_columns = cursor.fetchall()
        print("   comparison_results columns:")
        for col in comp_columns:
            print(f"     {col[1]} {col[2]}")
        
        # Check pre_reporting_results table
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        pre_columns = cursor.fetchall()
        print("   pre_reporting_results columns:")
        for col in pre_columns:
            print(f"     {col[1]} {col[2]}")
        
        # 3. Check data counts
        print("\n3. 📊 DATA COUNTS:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comp_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_count = cursor.fetchone()[0]
        
        print(f"   comparison_results: {comp_count}")
        print(f"   pre_reporting_results: {pre_count}")
        
        # 4. Check sample data
        print("\n4. 📊 SAMPLE DATA:")
        
        # Sample comparison_results
        cursor.execute("SELECT * FROM comparison_results WHERE session_id = ? LIMIT 3", (current_session,))
        comp_samples = cursor.fetchall()
        
        if comp_samples:
            print("   comparison_results sample:")
            for i, row in enumerate(comp_samples):
                print(f"     Row {i+1}: {str(row)[:100]}...")
        
        # Sample pre_reporting_results
        cursor.execute("SELECT * FROM pre_reporting_results WHERE session_id = ? LIMIT 3", (current_session,))
        pre_samples = cursor.fetchall()
        
        if pre_samples:
            print("   pre_reporting_results sample:")
            for i, row in enumerate(pre_samples):
                print(f"     Row {i+1}: {str(row)[:100]}...")
        
        # 5. Test the actual query used by get_pre_reporting_data
        print("\n5. 🔍 TESTING ACTUAL QUERY:")
        
        # This is the query from phased_process_manager.py
        query = '''SELECT cr.id, cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                          cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                          pr.bulk_category, pr.bulk_size, pr.selected_for_report
                   FROM comparison_results cr
                   LEFT JOIN pre_reporting_results pr ON cr.id = pr.change_id
                   WHERE cr.session_id = ?
                   ORDER BY cr.priority DESC, cr.section_name, cr.employee_id'''
        
        cursor.execute(query, (current_session,))
        query_results = cursor.fetchall()
        
        print(f"   Query returned: {len(query_results)} rows")
        
        if query_results:
            print("   Sample query result:")
            sample = query_results[0]
            print(f"     {sample}")
            
            # Check if JOIN is working
            has_pre_reporting_data = any(row[9] is not None for row in query_results[:10])  # bulk_category
            print(f"   JOIN working: {has_pre_reporting_data}")
            
            if not has_pre_reporting_data:
                print("   ❌ LEFT JOIN not returning pre_reporting_results data")
                
                # Check if change_id matches
                cursor.execute("SELECT change_id FROM pre_reporting_results WHERE session_id = ? LIMIT 5", (current_session,))
                pre_change_ids = cursor.fetchall()
                
                cursor.execute("SELECT id FROM comparison_results WHERE session_id = ? LIMIT 5", (current_session,))
                comp_ids = cursor.fetchall()
                
                print(f"   Sample pre_reporting change_ids: {[row[0] for row in pre_change_ids]}")
                print(f"   Sample comparison_results ids: {[row[0] for row in comp_ids]}")
        else:
            print("   ❌ Query returned no results")
        
        # 6. Try direct pre_reporting_results query
        print("\n6. 🔍 DIRECT PRE_REPORTING_RESULTS QUERY:")
        
        cursor.execute("""
            SELECT pr.change_id, pr.bulk_category, pr.bulk_size, pr.selected_for_report,
                   cr.employee_id, cr.section_name, cr.item_label
            FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            WHERE pr.session_id = ?
            LIMIT 5
        """, (current_session,))
        
        direct_results = cursor.fetchall()
        
        print(f"   Direct query returned: {len(direct_results)} rows")
        
        if direct_results:
            print("   Sample direct result:")
            for row in direct_results:
                print(f"     Change ID: {row[0]}, Category: {row[1]}, Employee: {row[4]}, Change: {row[5]}.{row[6]}")
        
        # 7. Fix the issue if found
        print("\n7. 🔧 ATTEMPTING TO FIX:")
        
        if len(query_results) > 0 and not has_pre_reporting_data:
            print("   Issue: pre_reporting_results not joining properly")
            
            # Check if change_id field exists and has correct values
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ? AND change_id IS NOT NULL", (current_session,))
            valid_change_ids = cursor.fetchone()[0]
            
            print(f"   Valid change_ids in pre_reporting_results: {valid_change_ids}")
            
            if valid_change_ids == 0:
                print("   ❌ No valid change_ids in pre_reporting_results")
                print("   Need to regenerate pre_reporting_results with proper change_ids")
            else:
                print("   ✅ change_ids exist, checking data types...")
                
                # Check data types
                cursor.execute("SELECT typeof(change_id) FROM pre_reporting_results WHERE session_id = ? LIMIT 1", (current_session,))
                pre_type = cursor.fetchone()[0]
                
                cursor.execute("SELECT typeof(id) FROM comparison_results WHERE session_id = ? LIMIT 1", (current_session,))
                comp_type = cursor.fetchone()[0]
                
                print(f"   pre_reporting_results.change_id type: {pre_type}")
                print(f"   comparison_results.id type: {comp_type}")
        
        elif len(query_results) == 0:
            print("   Issue: No comparison_results found")
        
        else:
            print("   ✅ Query working correctly")
            
            # Test the actual method
            print("\n8. 🧪 TESTING ACTUAL METHOD:")
            
            try:
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = current_session
                
                result = manager.get_pre_reporting_data(current_session)
                
                print(f"   Method result: {result}")
                
                if result.get('success') and result.get('data'):
                    print(f"   ✅ Method working: {len(result['data'])} items")
                else:
                    print(f"   ❌ Method failed or returned empty")
            
            except Exception as e:
                print(f"   ❌ Method test failed: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_pre_reporting_query()
