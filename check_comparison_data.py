#!/usr/bin/env python3
"""
Check comparison data availability across all sessions
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_comparison_data():
    """Check comparison data across all sessions"""
    print("🔍 CHECKING COMPARISON DATA ACROSS ALL SESSIONS")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check all sessions
        print("\n1. 📋 ALL AUDIT SESSIONS:")
        cursor.execute("""
            SELECT session_id, status, created_at
            FROM audit_sessions 
            ORDER BY created_at DESC
        """)
        sessions = cursor.fetchall()
        
        if not sessions:
            print("   ❌ No audit sessions found")
            return
        
        for session in sessions:
            print(f"   Session: {session[0]}")
            print(f"   Status: {session[1]}")
            print(f"   Created: {session[2]}")
            print()
        
        # 2. Check comparison results by session
        print("2. 📊 COMPARISON RESULTS BY SESSION:")
        cursor.execute("""
            SELECT session_id, COUNT(*) as count
            FROM comparison_results 
            GROUP BY session_id
            ORDER BY count DESC
        """)
        comparison_by_session = cursor.fetchall()
        
        if comparison_by_session:
            for row in comparison_by_session:
                print(f"   Session {row[0]}: {row[1]} comparison results")
        else:
            print("   ❌ No comparison results found in any session")
        
        # 3. Check extracted data by session
        print("\n3. 📊 EXTRACTED DATA BY SESSION:")
        cursor.execute("""
            SELECT session_id, period_type, COUNT(*) as count
            FROM extracted_data 
            GROUP BY session_id, period_type
            ORDER BY session_id DESC, period_type
        """)
        extracted_by_session = cursor.fetchall()
        
        if extracted_by_session:
            for row in extracted_by_session:
                print(f"   Session {row[0]} ({row[1]}): {row[2]} extracted records")
        else:
            print("   ❌ No extracted data found in any session")
        
        # 4. Find the best session for testing
        print("\n4. 🎯 FINDING BEST SESSION FOR TESTING:")
        
        if comparison_by_session:
            best_session = comparison_by_session[0][0]  # Session with most comparison results
            best_count = comparison_by_session[0][1]
            
            print(f"   Best session: {best_session} ({best_count} comparison results)")
            
            # Test PRE-REPORTING with this session
            print("\n5. 🔄 TESTING PRE-REPORTING WITH BEST SESSION:")
            
            try:
                sys.path.append(os.path.dirname(__file__))
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = best_session
                
                # Test loading comparison results
                all_changes = manager._load_all_comparison_results()
                print(f"   ✅ Loaded {len(all_changes)} comparison results")
                
                if all_changes:
                    # Test categorization
                    categorized = manager._categorize_changes_for_reporting(all_changes)
                    print(f"   ✅ Categorized changes:")
                    for category, changes in categorized.items():
                        print(f"     {category}: {len(changes)} changes")
                    
                    # Test auto-selection
                    auto_selected = manager._apply_auto_selection_rules(categorized)
                    selected_count = sum(1 for selected in auto_selected.values() if selected)
                    print(f"   ✅ Auto-selected {selected_count} out of {len(all_changes)} changes")
                    
                    # Test storing results
                    try:
                        manager._store_pre_reporting_results(categorized, auto_selected)
                        print("   ✅ Pre-reporting results stored successfully")
                        
                        # Verify storage
                        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (best_session,))
                        stored_count = cursor.fetchone()[0]
                        print(f"   ✅ Verified: {stored_count} pre-reporting results in database")
                        
                    except Exception as e:
                        print(f"   ❌ Failed to store pre-reporting results: {e}")
                
            except Exception as e:
                print(f"   ❌ PRE-REPORTING test failed: {e}")
                import traceback
                traceback.print_exc()
        
        else:
            print("   ❌ No sessions with comparison results found")
            print("   Need to run the comparison phase first")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during comparison data check: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_comparison_data()
