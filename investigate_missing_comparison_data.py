#!/usr/bin/env python3
"""
Investigate why comparison results are missing and restore them
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_missing_comparison_data():
    """Investigate why comparison results are missing"""
    print("🔍 INVESTIGATING MISSING COMPARISON DATA")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check all tables for any data
        print("\n1. 📊 CHECKING ALL TABLES FOR DATA:")
        
        tables_to_check = [
            'audit_sessions',
            'extracted_data', 
            'comparison_results',
            'pre_reporting_results',
            'tracker_results',
            'auto_learning_results'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
                
                if count > 0 and table in ['audit_sessions', 'extracted_data']:
                    # Show recent records
                    cursor.execute(f"SELECT * FROM {table} ORDER BY rowid DESC LIMIT 3")
                    recent = cursor.fetchall()
                    for i, row in enumerate(recent):
                        if i == 0:  # Show first row details
                            print(f"     Sample: {str(row)[:100]}...")
            except Exception as e:
                print(f"   {table}: Error - {e}")
        
        # 2. Check for our known session
        print("\n2. 🔍 CHECKING FOR KNOWN SESSION:")
        
        known_session = "audit_session_1750779866_4cb1949e_5870"
        print(f"   Looking for session: {known_session}")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (known_session,))
        extracted_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (known_session,))
        comparison_count = cursor.fetchone()[0]
        
        print(f"   Extracted data: {extracted_count}")
        print(f"   Comparison results: {comparison_count}")
        
        if extracted_count > 0 and comparison_count == 0:
            print("   ⚠️ Extracted data exists but comparison results missing!")
            
            # 3. Regenerate comparison results for this session
            print("\n3. 🔄 REGENERATING COMPARISON RESULTS:")
            
            try:
                sys.path.append(os.path.dirname(__file__))
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = known_session
                
                # Load extracted data
                current_data = manager._load_extracted_data('current')
                previous_data = manager._load_extracted_data('previous')
                
                if current_data and previous_data:
                    print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                    
                    # Generate comparison results
                    comparison_results = manager._compare_payroll_data(current_data, previous_data)
                    manager._store_comparison_results(comparison_results)
                    
                    # Verify regeneration
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (known_session,))
                    new_comparison_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (known_session,))
                    new_items_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Regenerated {new_comparison_count} comparison results")
                    print(f"   ✅ Found {new_items_count} NEW items")
                    
                    # 4. Generate pre-reporting results
                    print("\n4. 🔄 GENERATING PRE-REPORTING RESULTS:")
                    
                    # Load comparison results
                    cursor.execute("""
                        SELECT id, employee_id, employee_name, section_name, item_label,
                               previous_value, current_value, change_type, priority,
                               numeric_difference, percentage_change
                        FROM comparison_results 
                        WHERE session_id = ?
                        ORDER BY priority DESC, section_name, employee_id
                    """, (known_session,))
                    
                    comparison_data = cursor.fetchall()
                    
                    if comparison_data:
                        # Convert to proper format
                        all_changes = []
                        for row in comparison_data:
                            change = {
                                'id': row[0],
                                'employee_id': row[1],
                                'employee_name': row[2],
                                'section_name': row[3],
                                'item_label': row[4],
                                'previous_value': row[5],
                                'current_value': row[6],
                                'change_type': row[7],
                                'priority': row[8],
                                'numeric_difference': row[9],
                                'percentage_change': row[10]
                            }
                            all_changes.append(change)
                        
                        # Categorize changes
                        categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                        
                        # Apply auto-selection
                        auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                        
                        # Store pre-reporting results
                        manager._store_pre_reporting_results(categorized_changes, auto_selected)
                        
                        # Verify storage
                        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (known_session,))
                        pre_reporting_count = cursor.fetchone()[0]
                        
                        print(f"   ✅ Generated {pre_reporting_count} pre-reporting results")
                        
                        # 5. Test the UI data method
                        print("\n5. 🧪 TESTING UI DATA METHOD:")
                        
                        result = manager.get_pre_reporting_data(known_session)
                        
                        if result.get('success') and result.get('data'):
                            data_count = len(result.get('data', []))
                            total_changes = result.get('total_changes', 0)
                            session_id = result.get('session_id', 'unknown')
                            
                            print(f"   ✅ UI method working:")
                            print(f"     Session: {session_id}")
                            print(f"     Data items: {data_count}")
                            print(f"     Total changes: {total_changes}")
                            
                            # Show sample data structure
                            if data_count > 0:
                                sample = result['data'][0]
                                print(f"   Sample item structure:")
                                for key, value in sample.items():
                                    print(f"     {key}: {value}")
                                    if len(str(value)) > 50:
                                        break
                        else:
                            print(f"   ❌ UI method failed: {result}")
                        
                        # 6. Update session as latest
                        print("\n6. 🔄 UPDATING AS LATEST SESSION:")
                        
                        cursor.execute("""
                            UPDATE audit_sessions 
                            SET created_at = datetime('now')
                            WHERE session_id = ?
                        """, (known_session,))
                        
                        conn.commit()
                        print(f"   ✅ Updated {known_session} as latest session")
                        
                        # 7. Test latest data method
                        print("\n7. 🧪 TESTING LATEST DATA METHOD:")
                        
                        latest_result = manager.get_latest_pre_reporting_data()
                        
                        if latest_result.get('success') and latest_result.get('data'):
                            latest_count = len(latest_result.get('data', []))
                            latest_total = latest_result.get('total_changes', 0)
                            latest_session_id = latest_result.get('session_id', 'unknown')
                            
                            print(f"   ✅ Latest method working:")
                            print(f"     Session: {latest_session_id}")
                            print(f"     Data items: {latest_count}")
                            print(f"     Total changes: {latest_total}")
                            
                            if latest_count > 0:
                                print(f"\n🎉 PRE-REPORTING DATA FULLY RESTORED!")
                                print(f"✅ Comparison results: {new_comparison_count}")
                                print(f"✅ Pre-reporting results: {pre_reporting_count}")
                                print(f"✅ UI data method: {data_count} items")
                                print(f"✅ Latest data method: {latest_count} items")
                                print(f"✅ Session: {known_session}")
                                print(f"\n🎯 UI should now load interactive pre-reporting page!")
                            else:
                                print(f"\n⚠️ Latest method returning 0 items")
                        else:
                            print(f"   ❌ Latest method failed: {latest_result}")
                    else:
                        print("   ❌ No comparison data to process for pre-reporting")
                else:
                    print("   ❌ Could not load extracted data")
            
            except Exception as e:
                print(f"   ❌ Regeneration failed: {e}")
                import traceback
                traceback.print_exc()
        
        elif extracted_count == 0:
            print("   ❌ No extracted data found for known session")
            
            # Check if there's any extracted data at all
            cursor.execute("SELECT DISTINCT session_id FROM extracted_data LIMIT 5")
            available_sessions = cursor.fetchall()
            
            if available_sessions:
                print("   Available sessions with extracted data:")
                for session in available_sessions:
                    session_id = session[0]
                    cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (session_id,))
                    count = cursor.fetchone()[0]
                    print(f"     {session_id}: {count} records")
            else:
                print("   ❌ No extracted data found in database at all")
        
        else:
            print("   ✅ Both extracted data and comparison results exist")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during investigation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_missing_comparison_data()
