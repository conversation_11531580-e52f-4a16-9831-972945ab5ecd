#!/usr/bin/env python3
"""
Test that PRE-REPORTING UI is ready with proper data
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_pre_reporting_ui_ready():
    """Test that PRE-REPORTING UI is ready"""
    print("🧪 TESTING PRE-REPORTING UI READINESS")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check data availability
        print("\n2. 📊 DATA AVAILABILITY:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        
        print(f"   Comparison results: {comparison_count}")
        print(f"   Pre-reporting results: {pre_reporting_count}")
        
        if comparison_count == 0 or pre_reporting_count == 0:
            print("   ❌ Insufficient data for UI")
            return
        
        # 3. Test the UI data method (what the UI actually calls)
        print("\n3. 🧪 TESTING UI DATA METHOD:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            
            # Test get_pre_reporting_data() without parameters (latest session)
            result = manager.get_pre_reporting_data()
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                session_id = result.get('session_id', 'unknown')
                
                print(f"   ✅ get_pre_reporting_data() (latest):")
                print(f"     Session: {session_id}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                # Analyze data structure for UI compatibility
                if data_count > 0:
                    sample = result['data'][0]
                    
                    # Check required fields for UI
                    required_fields = [
                        'employee_id', 'employee_name', 'section_name', 'item_label',
                        'previous_value', 'current_value', 'change_type', 'bulk_category',
                        'selected_for_report', 'priority'
                    ]
                    
                    missing_fields = []
                    for field in required_fields:
                        if field not in sample:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"   ❌ Missing required fields: {missing_fields}")
                        return
                    else:
                        print(f"   ✅ All required UI fields present")
                    
                    # Analyze categories and selections
                    categories = {}
                    auto_selected_count = 0
                    change_types = {}
                    priorities = {}
                    
                    for item in result['data']:
                        # Category analysis
                        category = item.get('bulk_category', 'Unknown')
                        categories[category] = categories.get(category, 0) + 1
                        
                        # Auto-selection analysis
                        if item.get('selected_for_report'):
                            auto_selected_count += 1
                        
                        # Change type analysis
                        change_type = item.get('change_type', 'Unknown')
                        change_types[change_type] = change_types.get(change_type, 0) + 1
                        
                        # Priority analysis
                        priority = item.get('priority', 'Unknown')
                        priorities[priority] = priorities.get(priority, 0) + 1
                    
                    print(f"\n   📊 UI DATA ANALYSIS:")
                    print(f"     Total changes: {data_count}")
                    print(f"     Auto-selected: {auto_selected_count}")
                    print(f"     Pending review: {data_count - auto_selected_count}")
                    
                    print(f"     Bulk categories:")
                    for category, count in categories.items():
                        print(f"       {category}: {count} changes")
                    
                    print(f"     Change types:")
                    for change_type, count in change_types.items():
                        print(f"       {change_type}: {count} changes")
                    
                    print(f"     Priorities:")
                    for priority, count in priorities.items():
                        print(f"       {priority}: {count} changes")
                    
                    # Show sample data
                    print(f"\n   📋 SAMPLE DATA FOR UI:")
                    print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                    print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                    print(f"     Values: {sample.get('previous_value')} → {sample.get('current_value')}")
                    print(f"     Type: {sample.get('change_type')}")
                    print(f"     Category: {sample.get('bulk_category')}")
                    print(f"     Priority: {sample.get('priority')}")
                    print(f"     Auto-selected: {sample.get('selected_for_report')}")
                    
                    print(f"\n🎉 PRE-REPORTING UI IS FULLY READY!")
                    print(f"✅ Session: {current_session}")
                    print(f"✅ Data available: {data_count} changes")
                    print(f"✅ Categories: {len(categories)}")
                    print(f"✅ Auto-selected: {auto_selected_count}")
                    print(f"✅ UI compatibility: All fields present")
                    
                    print(f"\n🎯 INTERACTIVE UI FEATURES READY:")
                    print(f"📋 ✅ Change Review: {data_count} changes to review")
                    print(f"📋 ✅ Bulk Categories: {', '.join(categories.keys())}")
                    print(f"📋 ✅ Auto-Selection: {auto_selected_count} pre-selected")
                    print(f"📋 ✅ Manual Selection: {data_count - auto_selected_count} pending")
                    print(f"📋 ✅ Priority Filtering: {len(priorities)} priority levels")
                    print(f"📋 ✅ Generate Report: Ready when changes selected")
                    
                    print(f"\n🚀 UI WILL LOAD INTERACTIVE PRE-REPORTING PAGE!")
                    print(f"🎯 Users can now:")
                    print(f"   • Review {data_count} detected changes")
                    print(f"   • Select/deselect changes for final report")
                    print(f"   • Filter by category and priority")
                    print(f"   • Generate final reports with selected changes")
                    
                    # Test specific UI method calls
                    print(f"\n4. 🧪 TESTING SPECIFIC UI METHOD CALLS:")
                    
                    # Test with session ID (what UI might call)
                    result_with_session = manager.get_pre_reporting_data(current_session)
                    
                    if result_with_session.get('success'):
                        session_data_count = len(result_with_session.get('data', []))
                        print(f"   ✅ get_pre_reporting_data(session_id): {session_data_count} items")
                    else:
                        print(f"   ❌ get_pre_reporting_data(session_id) failed")
                    
                    print(f"\n✅ ALL UI DATA METHODS WORKING!")
                    print(f"✅ PRE-REPORTING INTERACTIVE UI READY FOR USER INTERACTION!")
                    
                else:
                    print("   ❌ No data items returned")
            else:
                print(f"   ❌ get_pre_reporting_data() failed: {result}")
        
        except Exception as e:
            print(f"   ❌ UI data method test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pre_reporting_ui_ready()
