#!/usr/bin/env python3
"""
Debug script to test the comparison phase specifically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.python_database_manager import PythonDatabaseManager

def debug_comparison_phase():
    """Debug the comparison phase step by step"""
    
    print("🔍 DEBUGGING COMPARISON PHASE")
    print("=" * 60)
    
    # Initialize database manager
    db_manager = PythonDatabaseManager()
    
    # Get latest session
    sessions = db_manager.execute_query("""
        SELECT session_id FROM audit_sessions 
        ORDER BY created_at DESC 
        LIMIT 1
    """)
    
    if not sessions:
        print("❌ No sessions found")
        return
    
    session_id = sessions[0]['session_id']
    print(f"📋 Testing session: {session_id}")
    
    # Test _load_extracted_data method logic
    print("\n🔍 TESTING _load_extracted_data LOGIC:")
    print("-" * 50)
    
    # Load current data
    current_rows = db_manager.execute_query("""
        SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
        FROM extracted_data
        WHERE session_id = ? AND period_type = 'current'
        ORDER BY employee_id, section_name, item_label
    """, (session_id,))
    
    print(f"Current rows from DB: {len(current_rows)}")
    
    # Load previous data  
    previous_rows = db_manager.execute_query("""
        SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
        FROM extracted_data
        WHERE session_id = ? AND period_type = 'previous'
        ORDER BY employee_id, section_name, item_label
    """, (session_id,))
    
    print(f"Previous rows from DB: {len(previous_rows)}")
    
    # Convert to the format expected by comparison logic
    def convert_rows_to_employee_data(rows):
        """Convert database rows to employee data format"""
        employees = {}
        
        for row in rows:
            employee_id = row['employee_id']
            employee_name = row['employee_name']
            section_name = row['section_name']
            item_label = row['item_label']
            item_value = row['item_value']
            
            if employee_id not in employees:
                employees[employee_id] = {
                    'employee_id': employee_id,
                    'employee_name': employee_name,
                    'sections': {}
                }
            
            if section_name not in employees[employee_id]['sections']:
                employees[employee_id]['sections'][section_name] = {}
            
            employees[employee_id]['sections'][section_name][item_label] = item_value
        
        return list(employees.values())
    
    print("\n🔄 Converting data to comparison format...")
    current_data = convert_rows_to_employee_data(current_rows)
    previous_data = convert_rows_to_employee_data(previous_rows)
    
    print(f"Current employees: {len(current_data)}")
    print(f"Previous employees: {len(previous_data)}")
    
    if current_data:
        print(f"Sample current employee: {current_data[0]['employee_id']} - {current_data[0]['employee_name']}")
        print(f"  Sections: {list(current_data[0]['sections'].keys())}")
    
    if previous_data:
        print(f"Sample previous employee: {previous_data[0]['employee_id']} - {previous_data[0]['employee_name']}")
        print(f"  Sections: {list(previous_data[0]['sections'].keys())}")
    
    # Test comparison logic
    print("\n🔍 TESTING COMPARISON LOGIC:")
    print("-" * 50)
    
    if not current_data or not previous_data:
        print("❌ No data to compare!")
        return
    
    # Create lookup dictionaries
    current_lookup = {emp['employee_id']: emp for emp in current_data}
    previous_lookup = {emp['employee_id']: emp for emp in previous_data}
    
    print(f"Current lookup: {len(current_lookup)} employees")
    print(f"Previous lookup: {len(previous_lookup)} employees")
    
    # Get all unique employee IDs
    all_employee_ids = set(current_lookup.keys()) | set(previous_lookup.keys())
    print(f"Total unique employee IDs: {len(all_employee_ids)}")
    
    # Test with first few employees
    test_employees = list(all_employee_ids)[:5]
    print(f"\n🧪 Testing comparison with first 5 employees: {test_employees}")
    
    comparison_results = []
    
    for employee_id in test_employees:
        current_emp = current_lookup.get(employee_id)
        previous_emp = previous_lookup.get(employee_id)
        
        print(f"\n👤 Employee: {employee_id}")
        print(f"   Current: {'✅' if current_emp else '❌'}")
        print(f"   Previous: {'✅' if previous_emp else '❌'}")
        
        if current_emp and previous_emp:
            # Compare sections
            current_sections = current_emp.get('sections', {})
            previous_sections = previous_emp.get('sections', {})
            
            all_sections = set(current_sections.keys()) | set(previous_sections.keys())
            print(f"   Sections to compare: {len(all_sections)}")
            
            for section_name in all_sections:
                current_section = current_sections.get(section_name, {})
                previous_section = previous_sections.get(section_name, {})
                
                all_items = set(current_section.keys()) | set(previous_section.keys())
                
                for item_label in all_items:
                    current_value = current_section.get(item_label, '')
                    previous_value = previous_section.get(item_label, '')
                    
                    if current_value != previous_value:
                        change_type = 'CHANGED'
                        if not previous_value:
                            change_type = 'NEW'
                        elif not current_value:
                            change_type = 'REMOVED'
                        
                        comparison_results.append({
                            'employee_id': employee_id,
                            'employee_name': current_emp['employee_name'],
                            'section_name': section_name,
                            'item_label': item_label,
                            'previous_value': previous_value,
                            'current_value': current_value,
                            'change_type': change_type
                        })
                        
                        print(f"     🔄 CHANGE: {section_name}.{item_label}: '{previous_value}' → '{current_value}' ({change_type})")
        
        elif current_emp and not previous_emp:
            print(f"     🆕 NEW EMPLOYEE")
        elif previous_emp and not current_emp:
            print(f"     🗑️ REMOVED EMPLOYEE")
    
    print(f"\n📊 COMPARISON RESULTS: {len(comparison_results)} changes found")
    
    if comparison_results:
        print("✅ Comparison logic is working!")
        print("❌ The issue is likely in the _load_extracted_data method or data storage")
    else:
        print("⚠️ No changes found in test sample")
        print("   This could be normal if the data is identical")
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS COMPLETE")

if __name__ == "__main__":
    debug_comparison_phase()
