#!/usr/bin/env python3
"""
Debug the pre-reporting API to find the exact issue
"""

import sqlite3
import sys
import os
sys.path.append('.')

def debug_pre_reporting_api():
    """Debug the pre-reporting API step by step"""
    print("🔍 DEBUGGING PRE-REPORTING API")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"1. Current session from database: {current_session}")
        
        # Check comparison results for this session
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count = cursor.fetchone()[0]
        print(f"2. Comparison results for current session: {count}")
        
        if count > 0:
            # Sample the data
            cursor.execute('SELECT employee_id, section_name, item_label, change_type FROM comparison_results WHERE session_id = ? LIMIT 3', (current_session,))
            samples = cursor.fetchall()
            print("3. Sample comparison data:")
            for sample in samples:
                print(f"   {sample[0]}: {sample[1]}.{sample[2]} ({sample[3]})")
        
        # Test the unified session manager
        print("\n4. Testing unified session manager...")
        try:
            from core.unified_session_manager import get_unified_session_manager
            unified_manager = get_unified_session_manager()
            unified_session = unified_manager.get_current_session_id()
            print(f"   Unified session manager returns: {unified_session}")
            
            if unified_session != current_session:
                print(f"   ⚠️ SESSION MISMATCH! Unified: {unified_session}, Database: {current_session}")
                
                # Check if unified session has data
                cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (unified_session,))
                unified_count = cursor.fetchone()[0]
                print(f"   Unified session comparison results: {unified_count}")
            else:
                print("   ✅ Session IDs match")
        except Exception as e:
            print(f"   ❌ Unified session manager error: {e}")
        
        # Test the database manager query directly
        print("\n5. Testing database query directly...")
        
        from core.phased_process_manager import PhasedProcessManager
        manager = PhasedProcessManager(debug_mode=True)
        
        # Test the exact query from the method
        query = '''SELECT id, employee_id, employee_name, section_name, item_label,
                          previous_value, current_value, change_type, priority,
                          numeric_difference, percentage_change
                   FROM comparison_results
                   WHERE session_id = ?
                   ORDER BY priority DESC, section_name, employee_id'''
        
        print(f"   Testing query with session: {current_session}")
        rows = manager.db_manager.execute_query(query, (current_session,))
        
        print(f"   Query returned: {len(rows) if rows else 0} rows")
        
        if rows:
            print("   ✅ Database query working!")
            print("   Sample row:")
            first_row = rows[0]
            if isinstance(first_row, dict):
                print(f"      Dict format: {first_row}")
            else:
                print(f"      Tuple format: {first_row}")
        else:
            print("   ❌ Database query returned no results")
            
            # Check if the database manager is using the right database
            print("   Checking database manager connection...")
            all_comparison = manager.db_manager.execute_query('SELECT COUNT(*) FROM comparison_results')
            print(f"   Total comparison results in database: {all_comparison[0] if all_comparison else 0}")
        
        # Test the full method with explicit session
        print("\n6. Testing full method with explicit session...")
        result = manager.get_pre_reporting_data(current_session)
        
        print(f"   Method result success: {result.get('success')}")
        print(f"   Method data count: {len(result.get('data', []))}")
        print(f"   Method session used: {result.get('session_id')}")
        
        if not result.get('success'):
            print(f"   Method error: {result.get('error')}")
        
        # Test without explicit session (automatic detection)
        print("\n7. Testing automatic session detection...")
        result2 = manager.get_pre_reporting_data()
        
        print(f"   Auto result success: {result2.get('success')}")
        print(f"   Auto data count: {len(result2.get('data', []))}")
        print(f"   Auto session used: {result2.get('session_id')}")
        
        conn.close()
        
        # Final assessment
        print("\n8. 🎯 DIAGNOSIS:")
        
        if count > 0 and rows and len(rows) > 0:
            if result.get('success') and len(result.get('data', [])) > 0:
                print("   ✅ Everything working - API should work")
                return True
            else:
                print("   ⚠️ Data exists, query works, but method fails")
                print("   Issue is in the method's data processing logic")
                return False
        else:
            print("   ❌ No data or query issues")
            return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_pre_reporting_api()
    
    if success:
        print("\n✅ API DEBUGGING COMPLETE - ISSUE IDENTIFIED")
    else:
        print("\n⚠️ API DEBUGGING INCOMPLETE - DEEPER ISSUE")
