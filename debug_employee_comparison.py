#!/usr/bin/env python3
"""
Debug employee-level comparison to understand why no NEW items are found
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_employee_comparison():
    """Debug employee-level comparison"""
    print("🔍 DEBUGGING EMPLOYEE-LEVEL COMPARISON")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Debugging session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check what NEW items exist in comparison results
        print("\n1. 📊 NEW ITEMS IN COMPARISON RESULTS:")
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
        new_count = cursor.fetchone()[0]
        print(f"   Total NEW items in comparison results: {new_count}")
        
        if new_count > 0:
            # Show sample NEW items
            cursor.execute("""
                SELECT employee_id, section_name, item_label, current_value
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW'
                AND (section_name = 'LOANS' OR item_label LIKE '%MOTOR%')
                LIMIT 10
            """, (current_session,))
            
            new_samples = cursor.fetchall()
            print("   Sample NEW items (loans and motor vehicles):")
            for row in new_samples:
                print(f"     {row[0]} - {row[1]}.{row[2]} = {row[3]}")
        
        # 2. Test employee-level comparison manually
        print("\n2. 🔄 TESTING EMPLOYEE-LEVEL COMPARISON:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if not current_data or not previous_data:
                print("   ❌ Could not load extracted data")
                return
            
            print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
            
            # Create lookup for previous data
            previous_lookup = {emp['employee_id']: emp for emp in previous_data}
            
            # Test with specific employees that have NEW items in comparison results
            if new_count > 0:
                cursor.execute("""
                    SELECT DISTINCT employee_id
                    FROM comparison_results 
                    WHERE session_id = ? AND change_type = 'NEW'
                    AND (section_name = 'LOANS' OR item_label LIKE '%MOTOR%')
                    LIMIT 5
                """, (current_session,))
                
                test_employees = cursor.fetchall()
                
                for emp_row in test_employees:
                    test_emp_id = emp_row[0]
                    print(f"\n   Testing employee: {test_emp_id}")
                    
                    # Find this employee in current data
                    current_emp = None
                    for emp in current_data:
                        if emp.get('employee_id') == test_emp_id:
                            current_emp = emp
                            break
                    
                    if not current_emp:
                        print(f"     ❌ Employee {test_emp_id} not found in current data")
                        continue
                    
                    # Find corresponding previous employee
                    previous_emp = previous_lookup.get(test_emp_id)
                    
                    if not previous_emp:
                        print(f"     ❌ Employee {test_emp_id} not found in previous data")
                        continue
                    
                    print(f"     ✅ Found employee in both periods")
                    
                    # Check LOANS section
                    current_loans = current_emp.get('sections', {}).get('LOANS', {})
                    previous_loans = previous_emp.get('sections', {}).get('LOANS', {})
                    
                    print(f"     Current LOANS items: {len(current_loans)}")
                    print(f"     Previous LOANS items: {len(previous_loans)}")
                    
                    if current_loans:
                        print("     Sample current LOANS:")
                        for i, (label, value) in enumerate(list(current_loans.items())[:3]):
                            print(f"       {label} = {value}")
                    
                    if previous_loans:
                        print("     Sample previous LOANS:")
                        for i, (label, value) in enumerate(list(previous_loans.items())[:3]):
                            print(f"       {label} = {value}")
                    
                    # Find NEW loan items
                    new_loan_items = []
                    for loan_item, loan_value in current_loans.items():
                        if loan_item not in previous_loans:
                            new_loan_items.append((loan_item, loan_value))
                    
                    if new_loan_items:
                        print(f"     ✅ Found {len(new_loan_items)} NEW loan items:")
                        for item, value in new_loan_items[:3]:
                            print(f"       NEW: {item} = {value}")
                    else:
                        print("     ❌ No NEW loan items found")
                    
                    # Check PERSONAL DETAILS for motor vehicle
                    current_personal = current_emp.get('sections', {}).get('PERSONAL DETAILS', {})
                    previous_personal = previous_emp.get('sections', {}).get('PERSONAL DETAILS', {})
                    
                    # Find NEW motor vehicle items
                    new_motor_items = []
                    for item_label, item_value in current_personal.items():
                        if (item_label not in previous_personal and 
                            ('MOTOR VEH' in item_label or 'VEHICLE MAINT' in item_label)):
                            new_motor_items.append((item_label, item_value))
                    
                    if new_motor_items:
                        print(f"     ✅ Found {len(new_motor_items)} NEW motor vehicle items:")
                        for item, value in new_motor_items:
                            print(f"       NEW: {item} = {value}")
                    else:
                        print("     ❌ No NEW motor vehicle items found")
                    
                    break  # Test only first employee for detailed analysis
            
            # 3. Check if the issue is with Balance B/F filtering
            print("\n3. 🔍 CHECKING BALANCE B/F FILTERING:")
            
            # Count all NEW loan items vs Balance B/F items
            cursor.execute("""
                SELECT COUNT(*) 
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
            """, (current_session,))
            
            total_new_loans = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
                AND item_label LIKE '%BALANCE B/F%'
            """, (current_session,))
            
            balance_bf_loans = cursor.fetchone()[0]
            
            print(f"   Total NEW loan items: {total_new_loans}")
            print(f"   NEW Balance B/F items: {balance_bf_loans}")
            
            if balance_bf_loans > 0:
                # Show sample Balance B/F items
                cursor.execute("""
                    SELECT employee_id, item_label, current_value
                    FROM comparison_results 
                    WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
                    AND item_label LIKE '%BALANCE B/F%'
                    LIMIT 5
                """, (current_session,))
                
                balance_samples = cursor.fetchall()
                print("   Sample NEW Balance B/F items:")
                for row in balance_samples:
                    print(f"     {row[0]} - {row[1]} = {row[2]}")
            
            # 4. Check motor vehicle items
            print("\n4. 🔍 CHECKING MOTOR VEHICLE ITEMS:")
            
            cursor.execute("""
                SELECT COUNT(*) 
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW' 
                AND (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
            """, (current_session,))
            
            motor_new_count = cursor.fetchone()[0]
            print(f"   NEW motor vehicle items: {motor_new_count}")
            
            if motor_new_count > 0:
                cursor.execute("""
                    SELECT employee_id, section_name, item_label, current_value
                    FROM comparison_results 
                    WHERE session_id = ? AND change_type = 'NEW' 
                    AND (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
                    LIMIT 5
                """, (current_session,))
                
                motor_samples = cursor.fetchall()
                print("   Sample NEW motor vehicle items:")
                for row in motor_samples:
                    print(f"     {row[0]} - {row[1]}.{row[2]} = {row[3]}")
        
        except Exception as e:
            print(f"   ❌ Employee comparison test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_employee_comparison()
