#!/usr/bin/env python3
"""
Test the fallback ContentSwitchingManager system
"""

import sys
import os
import sqlite3

def test_fallback_system():
    """Test that the fallback ContentSwitchingManager is working"""
    print("🧪 TESTING FALLBACK CONTENTSWITCH MANAGER SYSTEM")
    print("=" * 70)
    
    test_results = {}
    
    # 1. Check that real ContentSwitchingManager is commented out
    print("\n1. 🚫 CHECKING REAL CONTENTSWITCH MANAGER IS DISABLED:")
    
    with open('ui/content_switching_manager.js', 'r', encoding='utf-8') as f:
        real_csm_content = f.read()
    
    if '// COMMENTED OUT: Real ContentSwitchingManager' in real_csm_content:
        print("   ✅ Real ContentSwitchingManager is properly commented out")
        test_results['real_csm_disabled'] = True
    else:
        print("   ❌ Real ContentSwitchingManager is still active")
        test_results['real_csm_disabled'] = False
    
    if 'Using fallback version for now' in real_csm_content:
        print("   ✅ Fallback notice is present")
        test_results['fallback_notice'] = True
    else:
        print("   ❌ Fallback notice missing")
        test_results['fallback_notice'] = False
    
    # 2. Check fallback ContentSwitchingManager has required methods
    print("\n2. 🔧 CHECKING FALLBACK CONTENTSWITCH MANAGER:")
    
    with open('renderer.js', 'r', encoding='utf-8') as f:
        fallback_content = f.read()
    
    required_methods = [
        'switchToPhase(',
        'initialize(',
        'initializePreReportingPhase(',
        'getCurrentPhase(',
        'categorizeChanges(',
        'determinePriorityBySection(',
        'getChangeDescription('
    ]
    
    missing_methods = []
    for method in required_methods:
        if method in fallback_content:
            print(f"   ✅ {method.replace('(', '')} method present")
        else:
            print(f"   ❌ {method.replace('(', '')} method missing")
            missing_methods.append(method)
    
    test_results['fallback_methods_complete'] = len(missing_methods) == 0
    
    # 3. Check initialization setup
    print("\n3. 🏗️ CHECKING INITIALIZATION SETUP:")
    
    with open('index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    if 'Fallback ContentSwitchingManager' in html_content:
        print("   ✅ Initialization updated for fallback")
        test_results['initialization_updated'] = True
    else:
        print("   ❌ Initialization not updated for fallback")
        test_results['initialization_updated'] = False
    
    # 4. Check database state for testing
    print("\n4. 🗄️ CHECKING DATABASE STATE:")
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check current session
        cursor.execute("SELECT session_id, status FROM current_session WHERE id = 1")
        current_session_row = cursor.fetchone()
        
        if current_session_row:
            session_id, session_status = current_session_row
            print(f"   ✅ Current session: {session_id}")
            print(f"   ✅ Session status: {session_status}")
            
            # Check PRE_REPORTING phase status
            cursor.execute("""
                SELECT status, data_count FROM session_phases 
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            """, (session_id,))
            phase_row = cursor.fetchone()
            
            if phase_row:
                phase_status, data_count = phase_row
                print(f"   ✅ PRE_REPORTING phase: {phase_status} ({data_count} items)")
                test_results['database_ready'] = phase_status == 'WAITING_FOR_USER' and data_count > 0
            else:
                print("   ❌ PRE_REPORTING phase not found")
                test_results['database_ready'] = False
        else:
            print("   ❌ No current session found")
            test_results['database_ready'] = False
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        test_results['database_ready'] = False
    
    # 5. Check script loading order
    print("\n5. 📜 CHECKING SCRIPT LOADING ORDER:")
    
    script_order = []
    lines = html_content.split('\n')
    for i, line in enumerate(lines):
        if 'renderer.js' in line and '<script' in line:
            script_order.append(('renderer.js', i))
        elif 'content_switching_manager.js' in line and '<script' in line:
            script_order.append(('content_switching_manager.js', i))
    
    if len(script_order) >= 2:
        renderer_line = next((line for name, line in script_order if name == 'renderer.js'), None)
        csm_line = next((line for name, line in script_order if name == 'content_switching_manager.js'), None)
        
        if renderer_line and csm_line and renderer_line < csm_line:
            print("   ✅ renderer.js loads before content_switching_manager.js (correct for fallback)")
            test_results['script_order'] = True
        else:
            print("   ⚠️ Script loading order may cause issues")
            test_results['script_order'] = False
    else:
        print("   ❌ Could not determine script loading order")
        test_results['script_order'] = False
    
    # 6. Final assessment
    print("\n6. 📊 FINAL ASSESSMENT:")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    print(f"   Total tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {total_tests - passed_tests}")
    print(f"   Success rate: {(passed_tests/total_tests)*100:.1f}%")
    
    # Critical tests for fallback system
    critical_tests = [
        'real_csm_disabled',
        'fallback_methods_complete',
        'initialization_updated',
        'database_ready'
    ]
    
    critical_passed = sum(1 for test in critical_tests if test_results.get(test, False))
    
    print(f"\n   CRITICAL TESTS: {critical_passed}/{len(critical_tests)} passed")
    
    if critical_passed == len(critical_tests):
        print(f"\n   🎉 FALLBACK SYSTEM IS READY!")
        print(f"   ✅ Real ContentSwitchingManager disabled")
        print(f"   ✅ Fallback has all required methods")
        print(f"   ✅ Initialization configured for fallback")
        print(f"   ✅ Database ready with pre-reporting data")
        
        print(f"\n   📋 EXPECTED BEHAVIOR:")
        print(f"   1. ✅ App loads with fallback ContentSwitchingManager")
        print(f"   2. ✅ No conflicts between real and fallback versions")
        print(f"   3. ✅ switchToPhase('pre-reporting') should work")
        print(f"   4. ✅ Pre-Reporting UI should appear")
        print(f"   5. ✅ User can interact with 6,000+ changes")
        print(f"   6. ✅ No 'switchToPhase is not a function' errors")
        
    else:
        print(f"\n   ❌ FALLBACK SYSTEM HAS ISSUES:")
        for test in critical_tests:
            if not test_results.get(test, False):
                print(f"      - {test}")
    
    # 7. Next steps
    print(f"\n7. 🚀 NEXT STEPS:")
    
    if critical_passed == len(critical_tests):
        print(f"   1. ✅ Test the Pre-Reporting UI in the application")
        print(f"   2. ✅ Verify phase switching works correctly")
        print(f"   3. ✅ Confirm user interaction is possible")
        print(f"   4. ✅ Later: Re-enable real ContentSwitchingManager if needed")
    else:
        print(f"   1. ❌ Fix the failing critical tests")
        print(f"   2. ❌ Re-run this verification")
        print(f"   3. ❌ Then test the UI")

if __name__ == "__main__":
    test_fallback_system()
