#!/usr/bin/env python3
"""
Test that the Pre-Reporting UI will actually be visible to users
"""

import sys
import os
import sqlite3

def test_ui_visibility_fixes():
    """Test that all fixes are in place for UI visibility"""
    print("🔍 TESTING PRE-REPORTING UI VISIBILITY FIXES")
    print("=" * 70)
    
    fixes_status = {}
    
    # 1. Check ContentSwitchingManager initialization fix
    print("\n1. 🔧 CHECKING CONTENTSWITCH MANAGER INITIALIZATION:")
    
    with open('index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    if 'window.contentSwitchingManager = new window.ContentSwitchingManager()' in html_content:
        print("   ✅ ContentSwitchingManager initialization added")
        fixes_status['csm_initialization'] = True
    else:
        print("   ❌ ContentSwitchingManager initialization missing")
        fixes_status['csm_initialization'] = False
    
    # 2. Check phase switching fallback fix
    print("\n2. 🔄 CHECKING PHASE SWITCHING FALLBACK:")
    
    with open('renderer.js', 'r', encoding='utf-8') as f:
        renderer_content = f.read()
    
    if 'Direct fallback: Show pre-reporting content manually' in renderer_content:
        print("   ✅ Direct phase switching fallback added")
        fixes_status['phase_switching_fallback'] = True
    else:
        print("   ❌ Direct phase switching fallback missing")
        fixes_status['phase_switching_fallback'] = False
    
    # 3. Check ContentSwitchingManager panel detection fix
    print("\n3. 🎯 CHECKING PANEL DETECTION FIX:")
    
    with open('ui/content_switching_manager.js', 'r', encoding='utf-8') as f:
        csm_content = f.read()
    
    if 'document.getElementById(`${phase}-content`)' in csm_content:
        print("   ✅ Phase-content detection added to ContentSwitchingManager")
        fixes_status['panel_detection'] = True
    else:
        print("   ❌ Phase-content detection missing")
        fixes_status['panel_detection'] = False
    
    # 4. Check CSS phase visibility rules
    print("\n4. 🎨 CHECKING CSS PHASE VISIBILITY:")
    
    with open('styles.css', 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    if '.phase-content.active' in css_content and 'display: block' in css_content:
        print("   ✅ CSS phase visibility rules present")
        fixes_status['css_visibility'] = True
    else:
        print("   ❌ CSS phase visibility rules missing")
        fixes_status['css_visibility'] = False
    
    # 5. Check HTML container structure
    print("\n5. 🏗️ CHECKING HTML CONTAINER STRUCTURE:")
    
    pre_reporting_containers = []
    if 'id="pre-reporting-content"' in html_content:
        pre_reporting_containers.append('pre-reporting-content')
    if 'id="pre-reporting-panel"' in html_content:
        pre_reporting_containers.append('pre-reporting-panel')
    
    if len(pre_reporting_containers) > 0:
        print(f"   ✅ Pre-reporting containers found: {', '.join(pre_reporting_containers)}")
        fixes_status['html_containers'] = True
    else:
        print("   ❌ No pre-reporting containers found")
        fixes_status['html_containers'] = False
    
    # 6. Check database state
    print("\n6. 🗄️ CHECKING DATABASE STATE:")
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check current session
        cursor.execute("SELECT session_id, status FROM current_session WHERE id = 1")
        current_session_row = cursor.fetchone()
        
        if current_session_row:
            session_id, session_status = current_session_row
            print(f"   ✅ Current session: {session_id}")
            print(f"   ✅ Session status: {session_status}")
            
            # Check PRE_REPORTING phase status
            cursor.execute("""
                SELECT status, data_count FROM session_phases 
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            """, (session_id,))
            phase_row = cursor.fetchone()
            
            if phase_row:
                phase_status, data_count = phase_row
                print(f"   ✅ PRE_REPORTING phase: {phase_status} ({data_count} items)")
                fixes_status['database_state'] = phase_status == 'WAITING_FOR_USER' and data_count > 0
            else:
                print("   ❌ PRE_REPORTING phase not found")
                fixes_status['database_state'] = False
        else:
            print("   ❌ No current session found")
            fixes_status['database_state'] = False
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        fixes_status['database_state'] = False
    
    # 7. Final assessment
    print("\n7. 📊 FINAL ASSESSMENT:")
    
    total_fixes = len(fixes_status)
    passed_fixes = sum(1 for status in fixes_status.values() if status)
    
    print(f"   Total fixes: {total_fixes}")
    print(f"   Passed: {passed_fixes}")
    print(f"   Failed: {total_fixes - passed_fixes}")
    print(f"   Success rate: {(passed_fixes/total_fixes)*100:.1f}%")
    
    # Critical fixes for UI visibility
    critical_fixes = [
        'csm_initialization',
        'phase_switching_fallback', 
        'panel_detection',
        'css_visibility',
        'html_containers',
        'database_state'
    ]
    
    critical_passed = sum(1 for fix in critical_fixes if fixes_status.get(fix, False))
    
    print(f"\n   CRITICAL FIXES: {critical_passed}/{len(critical_fixes)} passed")
    
    if critical_passed == len(critical_fixes):
        print(f"\n   🎉 ALL CRITICAL FIXES ARE IN PLACE!")
        print(f"   ✅ Pre-Reporting UI SHOULD NOW BE VISIBLE!")
        print(f"   ✅ Phase switching should work correctly")
        print(f"   ✅ User interaction should be possible")
        
        print(f"\n   📋 WHAT SHOULD HAPPEN NOW:")
        print(f"   1. ✅ ContentSwitchingManager initializes on app start")
        print(f"   2. ✅ When PRE_REPORTING phase is ready, UI detects WAITING_FOR_USER")
        print(f"   3. ✅ loadPreReportingUIFromDatabase() is called")
        print(f"   4. ✅ Phase switching makes pre-reporting content visible")
        print(f"   5. ✅ InteractivePreReporting UI loads with data")
        print(f"   6. ✅ User sees the Pre-Reporting interface")
        print(f"   7. ✅ User can interact and review changes")
        print(f"   8. ✅ User clicks 'Generate Report' to complete")
        
    else:
        print(f"\n   ❌ MISSING CRITICAL FIXES:")
        for fix in critical_fixes:
            if not fixes_status.get(fix, False):
                print(f"      - {fix}")
        
        print(f"\n   ❌ Pre-Reporting UI may still not be visible")
        print(f"   ❌ Additional fixes needed")
    
    # 8. Root cause resolution verification
    print(f"\n8. 🎯 ROOT CAUSE RESOLUTION VERIFICATION:")
    
    root_causes_fixed = {
        'ContentSwitchingManager not initialized': fixes_status.get('csm_initialization', False),
        'Phase switching not working': fixes_status.get('phase_switching_fallback', False) and fixes_status.get('panel_detection', False),
        'Phase content hidden by CSS': fixes_status.get('css_visibility', False),
        'No fallback for phase switching': fixes_status.get('phase_switching_fallback', False),
        'Database state not ready': fixes_status.get('database_state', False)
    }
    
    all_root_causes_fixed = all(root_causes_fixed.values())
    
    if all_root_causes_fixed:
        print(f"   🎉 ALL ROOT CAUSES HAVE BEEN FIXED!")
        print(f"   ✅ UI visibility issue should be resolved")
        print(f"   ✅ Auto-completion issue should be resolved")
        print(f"   ✅ System is ready for user interaction")
    else:
        print(f"   ❌ Some root causes still need fixing:")
        for cause, fixed in root_causes_fixed.items():
            status = "✅" if fixed else "❌"
            print(f"      {status} {cause}")

if __name__ == "__main__":
    test_ui_visibility_fixes()
