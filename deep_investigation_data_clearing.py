#!/usr/bin/env python3
"""
Deep investigation into what's clearing extracted data
"""

import sys
import os
import sqlite3
import time
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def deep_investigation_data_clearing():
    """Deep investigation into data clearing"""
    print("🔍 DEEP INVESTIGATION: EXTRACTED DATA CLEARING")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check all tables and their record counts
        print("\n1. 📊 COMPLETE DATABASE STATE:")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        all_tables = cursor.fetchall()
        
        table_data = {}
        for table in all_tables:
            table_name = table[0]
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                table_data[table_name] = count
                print(f"   {table_name}: {count} records")
            except Exception as e:
                print(f"   {table_name}: Error - {e}")
        
        # 2. Check for any DELETE operations in recent history
        print("\n2. 🔍 CHECKING FOR DELETE OPERATIONS:")
        
        # Check if there are any audit logs or transaction logs
        audit_tables = ['audit_log', 'transaction_log', 'operation_log', 'delete_log']
        
        for audit_table in audit_tables:
            if audit_table in table_data:
                cursor.execute(f"SELECT * FROM {audit_table} ORDER BY created_at DESC LIMIT 10")
                logs = cursor.fetchall()
                if logs:
                    print(f"   Found {audit_table}:")
                    for log in logs:
                        print(f"     {log}")
        
        # 3. Check for triggers that might delete data
        print("\n3. 🔍 CHECKING FOR DATABASE TRIGGERS:")
        
        cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='trigger'")
        triggers = cursor.fetchall()
        
        if triggers:
            print("   Database triggers found:")
            for trigger in triggers:
                print(f"     {trigger[0]}: {trigger[1]}")
        else:
            print("   No database triggers found")
        
        # 4. Check for foreign key constraints that might cascade deletes
        print("\n4. 🔍 CHECKING FOREIGN KEY CONSTRAINTS:")
        
        for table_name in ['extracted_data', 'comparison_results', 'pre_reporting_results']:
            if table_name in table_data:
                cursor.execute(f"PRAGMA foreign_key_list({table_name})")
                fks = cursor.fetchall()
                if fks:
                    print(f"   {table_name} foreign keys:")
                    for fk in fks:
                        print(f"     {fk}")
        
        # 5. Check current session data specifically
        print("\n5. 📊 CURRENT SESSION DATA ANALYSIS:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
            
            # Check each critical table for this session
            critical_tables = [
                ('extracted_data', 'session_id'),
                ('comparison_results', 'session_id'),
                ('pre_reporting_results', 'session_id'),
                ('audit_sessions', 'session_id')
            ]
            
            for table_name, session_column in critical_tables:
                if table_name in table_data:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {session_column} = ?", (current_session,))
                    session_count = cursor.fetchone()[0]
                    print(f"   {table_name}: {session_count} records for current session")
                    
                    if session_count == 0 and table_data[table_name] > 0:
                        # Check what sessions exist in this table
                        cursor.execute(f"SELECT DISTINCT {session_column} FROM {table_name} LIMIT 5")
                        other_sessions = cursor.fetchall()
                        print(f"     Other sessions in {table_name}: {[s[0] for s in other_sessions]}")
        
        except Exception as e:
            print(f"   ❌ Session analysis failed: {e}")
        
        # 6. Check for cleanup scripts or scheduled tasks
        print("\n6. 🔍 SEARCHING FOR CLEANUP OPERATIONS:")
        
        # Search for files that might contain DELETE operations
        search_patterns = [
            "DELETE FROM extracted_data",
            "DELETE FROM comparison_results", 
            "DELETE FROM pre_reporting_results",
            "cleanExtractedData",
            "clear_extracted_data",
            "cleanup",
            "memory_cleanup"
        ]
        
        search_directories = [
            ".",
            "core",
            "ui",
            "scripts"
        ]
        
        found_operations = []
        
        for directory in search_directories:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        if file.endswith(('.py', '.js', '.sql')):
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()
                                    for pattern in search_patterns:
                                        if pattern.lower() in content.lower():
                                            found_operations.append((file_path, pattern))
                            except Exception as e:
                                pass
        
        if found_operations:
            print("   Found potential cleanup operations:")
            for file_path, pattern in found_operations:
                print(f"     {file_path}: contains '{pattern}'")
        else:
            print("   No obvious cleanup operations found in code")
        
        # 7. Check for memory cleanup in phased process manager
        print("\n7. 🔍 CHECKING MEMORY CLEANUP IN PHASED PROCESS MANAGER:")
        
        try:
            with open('core/phased_process_manager.py', 'r') as f:
                content = f.read()
                
                # Look for memory cleanup patterns
                cleanup_patterns = [
                    "memory_cleanup",
                    "gc.collect",
                    "clear_data",
                    "DELETE FROM",
                    "DROP TABLE",
                    "TRUNCATE"
                ]
                
                for pattern in cleanup_patterns:
                    if pattern in content:
                        print(f"     Found '{pattern}' in phased_process_manager.py")
                        
                        # Find the context around this pattern
                        lines = content.split('\n')
                        for i, line in enumerate(lines):
                            if pattern in line:
                                start = max(0, i-3)
                                end = min(len(lines), i+4)
                                print(f"       Context (lines {start+1}-{end}):")
                                for j in range(start, end):
                                    marker = ">>>" if j == i else "   "
                                    print(f"       {marker} {j+1}: {lines[j]}")
                                print()
        
        except Exception as e:
            print(f"   ❌ Could not check phased_process_manager.py: {e}")
        
        # 8. Check for database connection issues
        print("\n8. 🔍 CHECKING DATABASE CONNECTION ISSUES:")
        
        # Check database file size and modification time
        db_stat = os.stat(db_path)
        db_size = db_stat.st_size
        db_modified = datetime.fromtimestamp(db_stat.st_mtime)
        
        print(f"   Database file size: {db_size:,} bytes")
        print(f"   Last modified: {db_modified}")
        
        # Check if database is being recreated
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        print(f"   Total tables: {table_count}")
        
        # Check database integrity
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]
        print(f"   Database integrity: {integrity}")
        
        # 9. Monitor for real-time changes
        print("\n9. 🔍 MONITORING FOR REAL-TIME CHANGES:")
        
        print("   Taking snapshot of critical tables...")
        snapshot = {}
        for table_name in ['extracted_data', 'comparison_results', 'pre_reporting_results']:
            if table_name in table_data:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                snapshot[table_name] = cursor.fetchone()[0]
        
        print(f"   Snapshot: {snapshot}")
        print("   Waiting 5 seconds to check for changes...")
        
        time.sleep(5)
        
        changes_detected = False
        for table_name in snapshot:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            new_count = cursor.fetchone()[0]
            if new_count != snapshot[table_name]:
                print(f"   ⚠️ CHANGE DETECTED: {table_name} changed from {snapshot[table_name]} to {new_count}")
                changes_detected = True
        
        if not changes_detected:
            print("   ✅ No changes detected during monitoring period")
        
        # 10. Final analysis and recommendations
        print("\n10. 🎯 ANALYSIS AND RECOMMENDATIONS:")
        
        # Check if data exists but in wrong session
        total_extracted = table_data.get('extracted_data', 0)
        total_comparison = table_data.get('comparison_results', 0)
        total_pre_reporting = table_data.get('pre_reporting_results', 0)
        
        print(f"   Total extracted_data records: {total_extracted}")
        print(f"   Total comparison_results records: {total_comparison}")
        print(f"   Total pre_reporting_results records: {total_pre_reporting}")
        
        if total_extracted > 0:
            print("   ✅ Extracted data exists in database")
            print("   🔍 Issue: Data exists but not for current session")
            print("   💡 Recommendation: Check session ID consistency")
        else:
            print("   ❌ No extracted data in database at all")
            print("   🔍 Issue: Data is being completely cleared")
            print("   💡 Recommendation: Find and stop the clearing mechanism")
        
        if found_operations:
            print(f"\n   🚨 POTENTIAL CULPRITS FOUND:")
            for file_path, pattern in found_operations:
                print(f"     {file_path}: {pattern}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during investigation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    deep_investigation_data_clearing()
