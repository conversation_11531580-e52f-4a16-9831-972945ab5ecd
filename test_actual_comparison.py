#!/usr/bin/env python3
"""
Test the actual comparison method from phased_process_manager
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.phased_process_manager import PhasedProcessManager

def test_actual_comparison():
    """Test the actual comparison method"""
    
    print("🔍 TESTING ACTUAL COMPARISON METHOD")
    print("=" * 60)
    
    # Initialize the manager
    manager = PhasedProcessManager()
    
    # Get latest session
    sessions = manager.db_manager.execute_query("""
        SELECT session_id FROM audit_sessions 
        ORDER BY created_at DESC 
        LIMIT 1
    """)
    
    if not sessions:
        print("❌ No sessions found")
        return
    
    session_id = sessions[0]['session_id']
    manager.session_id = session_id
    print(f"📋 Testing session: {session_id}")
    
    # Test the actual _load_extracted_data method
    print("\n🔍 TESTING _load_extracted_data METHOD:")
    print("-" * 50)
    
    try:
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"Current data loaded: {len(current_data)} employees")
        print(f"Previous data loaded: {len(previous_data)} employees")
        
        if current_data:
            sample_emp = current_data[0]
            print(f"Sample current employee: {sample_emp['employee_id']} - {sample_emp['employee_name']}")
            print(f"  Sections: {list(sample_emp['sections'].keys())}")
            
            # Check data structure
            first_section = list(sample_emp['sections'].keys())[0]
            first_item = list(sample_emp['sections'][first_section].keys())[0]
            item_data = sample_emp['sections'][first_section][first_item]
            print(f"  Sample item structure: {first_section}.{first_item} = {item_data}")
        
        if previous_data:
            sample_emp = previous_data[0]
            print(f"Sample previous employee: {sample_emp['employee_id']} - {sample_emp['employee_name']}")
            print(f"  Sections: {list(sample_emp['sections'].keys())}")
        
        # Test the actual comparison method
        print("\n🔍 TESTING _compare_payroll_data METHOD:")
        print("-" * 50)
        
        if current_data and previous_data:
            # Test with just first 5 employees for speed
            test_current = current_data[:5]
            test_previous = previous_data[:5]
            
            print(f"Testing comparison with {len(test_current)} current and {len(test_previous)} previous employees")
            
            comparison_results = manager._compare_payroll_data(test_current, test_previous)
            
            print(f"Comparison results: {len(comparison_results)} changes found")
            
            if comparison_results:
                print("\n📊 SAMPLE COMPARISON RESULTS:")
                for i, result in enumerate(comparison_results[:5]):
                    print(f"  {i+1}. {result['employee_id']} - {result['section_name']}.{result['item_label']}")
                    print(f"     {result['previous_value']} → {result['current_value']} ({result['change_type']})")
                
                # Test storing results
                print(f"\n🔍 TESTING _store_comparison_results METHOD:")
                print("-" * 50)
                
                try:
                    manager._store_comparison_results(comparison_results)
                    print(f"✅ Successfully stored {len(comparison_results)} comparison results")
                    
                    # Verify storage
                    stored_result = manager.db_manager.execute_query(
                        "SELECT COUNT(*) FROM comparison_results WHERE session_id = ?",
                        (session_id,)
                    )
                    stored_count = stored_result[0]['COUNT(*)'] if isinstance(stored_result[0], dict) else stored_result[0][0]
                    
                    print(f"✅ Verified: {stored_count} results stored in database")
                    
                except Exception as e:
                    print(f"❌ Error storing results: {e}")
                    import traceback
                    print(f"Traceback: {traceback.format_exc()}")
            else:
                print("⚠️ No changes found in test sample")
        else:
            print("❌ No data to compare")
            
    except Exception as e:
        print(f"❌ Error in comparison test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    
    print("\n" + "=" * 60)
    print("🎯 TEST COMPLETE")

if __name__ == "__main__":
    test_actual_comparison()
