#!/usr/bin/env python3
"""
Fix the root cause of comparison phase failure
The issue is that comparison results are not being stored properly
"""

import sys
import os
import sqlite3

def fix_comparison_phase():
    """Fix the comparison phase root cause"""
    print("🔧 FIXING COMPARISON PHASE ROOT CAUSE")
    print("=" * 60)
    
    # Check the main database
    db_path = "data/templar_payroll_auditor.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Main database not found: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current state
        print("\n1. 📋 CURRENT DATABASE STATE:")
        
        # Check audit sessions
        cursor.execute("SELECT COUNT(*) FROM audit_sessions")
        sessions_count = cursor.fetchone()[0]
        print(f"   Audit sessions: {sessions_count}")
        
        # Check extracted data
        cursor.execute("SELECT COUNT(*) FROM extracted_data")
        extracted_count = cursor.fetchone()[0]
        print(f"   Extracted data records: {extracted_count}")
        
        # Check comparison results
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        if extracted_count > 0 and comparison_count == 0:
            print("\n   ⚠️ ROOT CAUSE IDENTIFIED:")
            print("   - Extracted data exists but no comparison results")
            print("   - The comparison phase is failing to store results")
        
        # 2. Check if we have a current session with extracted data
        print("\n2. 🔍 CHECKING LATEST SESSION:")
        
        cursor.execute("""
            SELECT session_id, current_pdf_path, previous_pdf_path, status
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        latest_session = cursor.fetchone()
        
        if latest_session:
            session_id = latest_session[0]
            print(f"   Latest session: {session_id}")
            print(f"   Status: {latest_session[3]}")
            
            # Check extracted data for this session
            cursor.execute("""
                SELECT period_type, COUNT(*) 
                FROM extracted_data 
                WHERE session_id = ? 
                GROUP BY period_type
            """, (session_id,))
            
            period_data = cursor.fetchall()
            print(f"   Extracted data by period:")
            for period, count in period_data:
                print(f"     {period}: {count} records")
            
            # Check if we have both current and previous data
            periods = [row[0] for row in period_data]
            if 'current' in periods and 'previous' in periods:
                print("   ✅ Both current and previous data available")
                print("   🔧 Ready to regenerate comparison results")
                
                # 3. Test the comparison phase directly
                print("\n3. 🧪 TESTING COMPARISON PHASE:")
                
                try:
                    sys.path.append('.')
                    from core.phased_process_manager import PhasedProcessManager
                    
                    # Create manager and set session
                    manager = PhasedProcessManager(debug_mode=True)
                    manager.session_id = session_id
                    
                    # Load extracted data
                    print("   Loading extracted data...")
                    current_data = manager._load_extracted_data('current')
                    previous_data = manager._load_extracted_data('previous')
                    
                    print(f"   Current data: {len(current_data)} employees")
                    print(f"   Previous data: {len(previous_data)} employees")
                    
                    if current_data and previous_data:
                        print("   Running comparison...")
                        comparison_results = manager._compare_payroll_data(current_data, previous_data)
                        print(f"   Comparison generated: {len(comparison_results)} changes")
                        
                        if comparison_results:
                            print("   Storing comparison results...")
                            manager._store_comparison_results(comparison_results)
                            
                            # Verify storage
                            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
                            stored_count = cursor.fetchone()[0]
                            print(f"   ✅ Stored: {stored_count} comparison results")
                            
                            if stored_count > 0:
                                print("\n🎉 COMPARISON PHASE FIXED!")
                                print("   The comparison results are now properly stored")
                                print("   The system should work correctly now")
                            else:
                                print("\n❌ Storage failed - comparison results not saved")
                        else:
                            print("   ⚠️ No changes detected in comparison")
                    else:
                        print("   ❌ Could not load extracted data")
                        
                except Exception as e:
                    print(f"   ❌ Error testing comparison phase: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("   ❌ Missing data - need both current and previous periods")
        else:
            print("   ❌ No audit sessions found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_comparison_phase()
