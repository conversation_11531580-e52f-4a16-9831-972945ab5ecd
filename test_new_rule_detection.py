#!/usr/bin/env python3
"""
Test NEW rule detection in comparison phase
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_new_rule_detection():
    """Test NEW rule detection"""
    print("🔍 TESTING NEW RULE DETECTION")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check extracted data for both periods
        print("\n1. 📊 CHECKING EXTRACTED DATA FOR BOTH PERIODS:")
        
        cursor.execute("""
            SELECT period_type, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (current_session,))
        
        periods = cursor.fetchall()
        for row in periods:
            print(f"   {row[0]}: {row[1]} records")
        
        # 2. Test NEW rule detection manually
        print("\n2. 🔄 TESTING NEW RULE DETECTION MANUALLY:")
        
        # Get a sample employee from current data
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current' AND section_name = 'LOANS'
            ORDER BY employee_id
            LIMIT 5
        """, (current_session,))
        
        current_samples = cursor.fetchall()
        
        if current_samples:
            print(f"   Found {len(current_samples)} current loan samples")
            
            for sample in current_samples:
                employee_id = sample[0]
                item_label = sample[3]
                
                print(f"   Testing employee {employee_id} - {item_label}")
                
                # Check if this item exists in previous data
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM extracted_data 
                    WHERE session_id = ? AND period_type = 'previous' 
                    AND employee_id = ? AND item_label = ?
                """, (current_session, employee_id, item_label))
                
                previous_count = cursor.fetchone()[0]
                
                if previous_count == 0:
                    print(f"     ✅ NEW ITEM FOUND: {employee_id} - {item_label}")
                    print(f"       Current value: {sample[4]}")
                else:
                    print(f"     ❌ Item exists in previous data")
        
        # 3. Test the comparison logic directly
        print("\n3. 🔄 TESTING COMPARISON LOGIC FOR NEW ITEMS:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Load extracted data
            print("   Loading extracted data...")
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                
                # Test with a specific employee
                test_employee = current_data[0]
                employee_id = test_employee.get('employee_id')
                
                print(f"   Testing employee: {employee_id}")
                
                # Find corresponding previous employee
                previous_employee = None
                for emp in previous_data:
                    if emp.get('employee_id') == employee_id:
                        previous_employee = emp
                        break
                
                if previous_employee:
                    print("   ✅ Found corresponding previous employee")
                    
                    # Check LOANS section specifically
                    current_loans = test_employee.get('sections', {}).get('LOANS', {})
                    previous_loans = previous_employee.get('sections', {}).get('LOANS', {})
                    
                    print(f"   Current LOANS items: {len(current_loans)}")
                    print(f"   Previous LOANS items: {len(previous_loans)}")
                    
                    # Find NEW items
                    new_items = []
                    for item_label, item_value in current_loans.items():
                        if item_label not in previous_loans:
                            new_items.append((item_label, item_value))
                    
                    if new_items:
                        print(f"   ✅ Found {len(new_items)} NEW loan items:")
                        for item_label, item_value in new_items[:3]:
                            print(f"     NEW: {item_label} = {item_value}")
                    else:
                        print("   ❌ No NEW loan items found for this employee")
                        
                        # Show some current and previous items for comparison
                        print("   Current loan items (first 3):")
                        for i, (label, value) in enumerate(list(current_loans.items())[:3]):
                            print(f"     {label} = {value}")
                        
                        print("   Previous loan items (first 3):")
                        for i, (label, value) in enumerate(list(previous_loans.items())[:3]):
                            print(f"     {label} = {value}")
                else:
                    print("   ❌ No corresponding previous employee found")
                
                # Test the full comparison for NEW items
                print("\n   Running full comparison to find NEW items...")
                comparison_results = manager._compare_payroll_data(current_data, previous_data)
                
                new_items_count = sum(1 for result in comparison_results if result.get('change_type') == 'NEW')
                print(f"   ✅ Full comparison found {new_items_count} NEW items out of {len(comparison_results)} total changes")
                
                if new_items_count > 0:
                    # Show sample NEW items
                    new_items = [result for result in comparison_results if result.get('change_type') == 'NEW'][:5]
                    print("   Sample NEW items:")
                    for item in new_items:
                        print(f"     {item['employee_id']} - {item['section_name']}.{item['item_label']} = {item['current_value']}")
                
            else:
                print("   ❌ Could not load extracted data")
        
        except Exception as e:
            print(f"   ❌ Comparison logic test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Check if we need to run comparison phase first
        print("\n4. 📊 CHECKING COMPARISON RESULTS STATUS:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results in database: {comparison_count}")
        
        if comparison_count == 0:
            print("   ⚠️ No comparison results found - need to run comparison phase first")
            
            # Run comparison phase
            print("   Running comparison phase...")
            try:
                manager = PhasedProcessManager()
                manager.session_id = current_session
                
                current_data = manager._load_extracted_data('current')
                previous_data = manager._load_extracted_data('previous')
                
                if current_data and previous_data:
                    comparison_results = manager._compare_payroll_data(current_data, previous_data)
                    manager._store_comparison_results(comparison_results)
                    
                    # Check NEW items after storing
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
                    new_count = cursor.fetchone()[0]
                    print(f"   ✅ Stored comparison results with {new_count} NEW items")
                    
                    if new_count > 0:
                        # Test tracker feeding with NEW items
                        print("\n5. 🔄 TESTING TRACKER FEEDING WITH NEW ITEMS:")
                        
                        new_items = manager._load_new_items_for_tracking()
                        print(f"   ✅ Loaded {len(new_items)} NEW items for tracking")
                        
                        if new_items:
                            # Sample NEW items
                            print("   Sample NEW items for tracking:")
                            for item in new_items[:5]:
                                print(f"     {item['employee_id']} - {item['section_name']}.{item['item_label']} = {item['item_value']}")
                        
                        # Run tracker feeding phase
                        options = {'report_name': 'NEW Rule Test', 'report_designation': 'Test'}
                        result = manager._phase_tracker_feeding(options)
                        
                        if result:
                            print("   ✅ TRACKER FEEDING phase completed with NEW rule!")
                            
                            # Check tracker results
                            cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
                            tracker_count = cursor.fetchone()[0]
                            print(f"   ✅ Tracker results: {tracker_count}")
                        else:
                            print("   ❌ TRACKER FEEDING phase failed")
                    else:
                        print("   ❌ No NEW items found in comparison results")
            
            except Exception as e:
                print(f"   ❌ Comparison phase failed: {e}")
                import traceback
                traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_rule_detection()
