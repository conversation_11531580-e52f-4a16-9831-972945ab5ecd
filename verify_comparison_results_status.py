#!/usr/bin/env python3
"""
Verify the current status of comparison results and trace where they were cleared
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_comparison_results_status():
    """Verify current status of comparison results"""
    print("🔍 VERIFYING COMPARISON RESULTS STATUS")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Checking session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current comparison results
        print("\n1. 📊 CURRENT COMPARISON RESULTS STATUS:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        total_comparison = cursor.fetchone()[0]
        print(f"   Total comparison results: {total_comparison}")
        
        if total_comparison > 0:
            # Check breakdown by change type
            cursor.execute("""
                SELECT change_type, COUNT(*) as count
                FROM comparison_results 
                WHERE session_id = ?
                GROUP BY change_type
                ORDER BY count DESC
            """, (current_session,))
            
            change_types = cursor.fetchall()
            print("   Breakdown by change type:")
            for row in change_types:
                print(f"     {row[0]}: {row[1]} items")
            
            # Check NEW items specifically
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
            new_count = cursor.fetchone()[0]
            print(f"   NEW items: {new_count}")
            
            if new_count > 0:
                # Show sample NEW items
                cursor.execute("""
                    SELECT employee_id, section_name, item_label, current_value
                    FROM comparison_results 
                    WHERE session_id = ? AND change_type = 'NEW'
                    AND (
                        (section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F%') OR
                        (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
                    )
                    LIMIT 10
                """, (current_session,))
                
                new_samples = cursor.fetchall()
                print("   Sample NEW items (loans and motor vehicles):")
                for row in new_samples:
                    print(f"     {row[0]} - {row[1]}.{row[2]} = {row[3]}")
        else:
            print("   ❌ NO comparison results found")
            
            # Check if there are any comparison results for any session
            cursor.execute("SELECT COUNT(*) FROM comparison_results")
            total_all = cursor.fetchone()[0]
            print(f"   Total comparison results (all sessions): {total_all}")
            
            if total_all > 0:
                cursor.execute("SELECT DISTINCT session_id FROM comparison_results LIMIT 5")
                other_sessions = cursor.fetchall()
                print("   Other sessions with comparison results:")
                for session in other_sessions:
                    print(f"     {session[0]}")
        
        # 2. Check when comparison results were last generated
        print("\n2. 📊 COMPARISON RESULTS HISTORY:")
        
        # Check if there's a timestamp or creation date
        cursor.execute("PRAGMA table_info(comparison_results)")
        columns = cursor.fetchall()
        
        has_timestamp = any(col[1] in ['created_at', 'timestamp', 'date_created'] for col in columns)
        
        if has_timestamp:
            cursor.execute("""
                SELECT session_id, COUNT(*), MAX(created_at) as last_created
                FROM comparison_results 
                GROUP BY session_id
                ORDER BY last_created DESC
                LIMIT 5
            """, (current_session,))
            
            recent_sessions = cursor.fetchall()
            print("   Recent comparison results by session:")
            for row in recent_sessions:
                print(f"     {row[0]}: {row[1]} results (last: {row[2]})")
        else:
            print("   No timestamp column found in comparison_results table")
        
        # 3. Check extracted data status
        print("\n3. 📊 EXTRACTED DATA STATUS:")
        
        cursor.execute("""
            SELECT period_type, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (current_session,))
        
        extracted_data = cursor.fetchall()
        
        if extracted_data:
            print("   Extracted data available:")
            for row in extracted_data:
                print(f"     {row[0]}: {row[1]} records")
            
            # This means we can regenerate comparison results
            print("   ✅ Extracted data available - comparison results can be regenerated")
        else:
            print("   ❌ No extracted data found")
        
        # 4. Check if comparison results were cleared in our scripts
        print("\n4. 🔍 CHECKING FOR CLEARING OPERATIONS:")
        
        # Look for any evidence of clearing operations
        # This is just informational - we can't actually trace deletions
        print("   Checking recent operations that might have cleared data...")
        
        # Check if pre-reporting results exist (they depend on comparison results)
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting results: {pre_reporting_count}")
        
        if pre_reporting_count > 0 and total_comparison == 0:
            print("   ⚠️ Pre-reporting exists but comparison results don't - suggests clearing occurred")
        elif pre_reporting_count == 0 and total_comparison == 0:
            print("   ⚠️ Both comparison and pre-reporting results missing")
        
        # 5. Determine next steps
        print("\n5. 🎯 NEXT STEPS DETERMINATION:")
        
        if total_comparison > 0:
            print("   ✅ Comparison results exist - can proceed with NEW rule application")
            
            # Check if we have the specific NEW items we need
            cursor.execute("""
                SELECT COUNT(*) 
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW'
                AND (
                    (section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F%') OR
                    (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
                )
            """, (current_session,))
            
            relevant_new_count = cursor.fetchone()[0]
            print(f"   Relevant NEW items for tracking: {relevant_new_count}")
            
            if relevant_new_count > 0:
                print("   ✅ Can proceed with corrected NEW rule application")
            else:
                print("   ⚠️ No relevant NEW items found for loan/motor vehicle tracking")
        
        elif len(extracted_data) >= 2:
            print("   ⚠️ Comparison results missing but extracted data available")
            print("   🔄 Need to regenerate comparison results first")
        else:
            print("   ❌ Both comparison results and extracted data missing")
            print("   🔄 Need to run full extraction and comparison process")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_comparison_results_status()
