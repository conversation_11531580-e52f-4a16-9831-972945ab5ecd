#!/usr/bin/env python3
"""
Test the comparison storage to find the root cause
"""

import sys
import os
import sqlite3

def test_comparison_storage():
    """Test comparison storage step by step"""
    print("🔍 TESTING COMPARISON STORAGE")
    print("=" * 60)
    
    try:
        # Get current session
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No current session found")
            return
        
        session_id = session_result[0]
        print(f"📋 Testing session: {session_id}")
        
        # Step 1: Run comparison and get results
        print("\n1. 🔄 RUNNING COMPARISON:")
        
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        manager.session_id = session_id
        
        # Load data
        current_data = manager._load_extracted_data('current')
        previous_data = manager._load_extracted_data('previous')
        
        print(f"   Loaded {len(current_data)} current + {len(previous_data)} previous employees")
        
        # Run comparison
        comparison_results = manager._compare_payroll_data(current_data, previous_data)
        print(f"   ✅ Generated {len(comparison_results)} comparison results")
        
        if not comparison_results:
            print("   ❌ No comparison results to test storage")
            return
        
        # Step 2: Test storage with small batch
        print("\n2. 💾 TESTING STORAGE:")
        
        # Clear any existing results first
        cursor.execute("DELETE FROM comparison_results WHERE session_id = ?", (session_id,))
        conn.commit()
        
        # Test with first 10 results
        test_results = comparison_results[:10]
        print(f"   Testing storage of {len(test_results)} results...")
        
        try:
            manager._store_comparison_results(test_results)
            print("   ✅ Storage method completed without error")
            
            # Check if data was actually stored
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            stored_count = cursor.fetchone()[0]
            print(f"   📊 Database shows {stored_count} stored results")
            
            if stored_count == 0:
                print("   🚨 CRITICAL ISSUE: Storage method succeeded but no data in database!")
                
                # Debug the database connection
                print("\n   🔍 DEBUGGING DATABASE CONNECTION:")
                
                # Check if table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
                table_exists = cursor.fetchone()
                print(f"      comparison_results table exists: {table_exists is not None}")
                
                if table_exists:
                    # Check table schema
                    cursor.execute("PRAGMA table_info(comparison_results)")
                    schema = cursor.fetchall()
                    print(f"      Table schema: {len(schema)} columns")
                    for col in schema:
                        print(f"         {col[1]} ({col[2]})")
                
                # Test direct insert
                print("\n   🧪 TESTING DIRECT INSERT:")
                try:
                    test_result = test_results[0]
                    cursor.execute(
                        '''INSERT INTO comparison_results
                           (session_id, employee_id, employee_name, section_name, item_label,
                            previous_value, current_value, change_type, priority,
                            numeric_difference, percentage_change)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                        (session_id, test_result['employee_id'], test_result['employee_name'],
                         test_result['section_name'], test_result['item_label'], test_result['previous_value'],
                         test_result['current_value'], test_result['change_type'], test_result['priority'],
                         test_result['numeric_difference'], test_result['percentage_change'])
                    )
                    conn.commit()
                    
                    # Check again
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
                    direct_count = cursor.fetchone()[0]
                    print(f"      Direct insert result: {direct_count} records")
                    
                    if direct_count > 0:
                        print("      ✅ Direct insert works - issue is in PhasedProcessManager storage")
                    else:
                        print("      ❌ Direct insert also failed - database issue")
                        
                except Exception as direct_error:
                    print(f"      ❌ Direct insert failed: {direct_error}")
            
            elif stored_count == len(test_results):
                print("   ✅ Storage working perfectly!")
                
                # Sample the stored data
                cursor.execute("SELECT employee_id, section_name, item_label, change_type FROM comparison_results WHERE session_id = ? LIMIT 3", (session_id,))
                samples = cursor.fetchall()
                print("   📋 Sample stored data:")
                for sample in samples:
                    print(f"      {sample[0]}: {sample[1]}.{sample[2]} ({sample[3]})")
            
            else:
                print(f"   ⚠️ Partial storage: Expected {len(test_results)}, got {stored_count}")
        
        except Exception as storage_error:
            print(f"   ❌ Storage failed: {storage_error}")
            import traceback
            traceback.print_exc()
        
        # Step 3: Test full comparison phase
        print("\n3. 🎯 TESTING FULL COMPARISON PHASE:")
        
        # Clear results again
        cursor.execute("DELETE FROM comparison_results WHERE session_id = ?", (session_id,))
        conn.commit()
        
        try:
            # Run the actual phase method
            options = {
                'currentMonth': 7,
                'currentYear': 2025,
                'previousMonth': 6,
                'previousYear': 2025
            }
            
            print("   Running _phase_comparison...")
            success = manager._phase_comparison(options)
            print(f"   Phase result: {success}")
            
            # Check final results
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            final_count = cursor.fetchone()[0]
            print(f"   Final database count: {final_count}")
            
            if final_count > 0:
                print("   ✅ FULL PHASE WORKS!")
            else:
                print("   ❌ FULL PHASE FAILED TO STORE DATA")
        
        except Exception as phase_error:
            print(f"   ❌ Phase execution failed: {phase_error}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
        print("\n🎯 STORAGE TESTING COMPLETE")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_comparison_storage()
