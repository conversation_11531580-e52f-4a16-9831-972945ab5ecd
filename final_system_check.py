#!/usr/bin/env python3
"""
Final comprehensive system check to verify all fixes are working
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def final_system_check():
    """Comprehensive final system check"""
    print("🔍 FINAL COMPREHENSIVE SYSTEM CHECK")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Checking session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. PRE-REPORTING PHASE CHECK
        print("\n1. 📊 PRE-REPORTING PHASE STATUS:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting results: {pre_reporting_count}")
        
        if pre_reporting_count > 0:
            # Check categorization
            cursor.execute("""
                SELECT bulk_category, COUNT(*) as count
                FROM pre_reporting_results 
                WHERE session_id = ?
                GROUP BY bulk_category
                ORDER BY count DESC
            """, (current_session,))
            
            categories = cursor.fetchall()
            print("   Categorization breakdown:")
            for row in categories:
                print(f"     {row[0]}: {row[1]} items")
            
            # Check auto-selection
            cursor.execute("""
                SELECT selected_for_report, COUNT(*) as count
                FROM pre_reporting_results 
                WHERE session_id = ?
                GROUP BY selected_for_report
            """, (current_session,))
            
            selection = cursor.fetchall()
            print("   Auto-selection breakdown:")
            for row in selection:
                status = "Selected" if row[0] else "Not Selected"
                print(f"     {status}: {row[1]} items")
            
            print("   ✅ PRE-REPORTING phase working correctly")
        else:
            print("   ❌ PRE-REPORTING phase not working")
        
        # 2. TRACKER FEEDING (BANK ADVISER) CHECK
        print("\n2. 📊 TRACKER FEEDING (BANK ADVISER) STATUS:")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
        tracker_count = cursor.fetchone()[0]
        print(f"   Tracker results: {tracker_count}")
        
        if tracker_count > 0:
            # Check tracker breakdown
            cursor.execute("""
                SELECT tracker_type, COUNT(*) as count
                FROM tracker_results 
                WHERE session_id = ?
                GROUP BY tracker_type
                ORDER BY count DESC
            """, (current_session,))
            
            tracker_breakdown = cursor.fetchall()
            print("   Tracker type breakdown:")
            for row in tracker_breakdown:
                print(f"     {row[0]}: {row[1]} items")
        
        # Check Bank Adviser tables
        bank_adviser_tables = {
            'in_house_loans': 'In-house Loans',
            'external_loans': 'External Loans',
            'motor_vehicle_maintenance': 'Motor Vehicle Maintenance'
        }
        
        total_bank_adviser = 0
        print("   Bank Adviser tables:")
        for table, name in bank_adviser_tables.items():
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE source_session = ?", (current_session,))
                count = cursor.fetchone()[0]
                print(f"     {name}: {count} NEW records")
                total_bank_adviser += count
                
                if count > 0:
                    # Show sample
                    if table == 'motor_vehicle_maintenance':
                        cursor.execute(f"SELECT employee_no, maintenance_amount FROM {table} WHERE source_session = ? LIMIT 1", (current_session,))
                    else:
                        cursor.execute(f"SELECT employee_no, loan_amount FROM {table} WHERE source_session = ? LIMIT 1", (current_session,))
                    
                    sample = cursor.fetchone()
                    if sample:
                        print(f"       Sample: {sample[0]} = {sample[1]}")
            except Exception as e:
                print(f"     {name}: Error - {e}")
        
        if total_bank_adviser > 0:
            print(f"   ✅ TRACKER FEEDING working correctly - {total_bank_adviser} total NEW items")
        else:
            print("   ❌ TRACKER FEEDING not working")
        
        # 3. AUTO LEARNING (DICTIONARY MANAGER) CHECK
        print("\n3. 📊 AUTO LEARNING (DICTIONARY MANAGER) STATUS:")
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        auto_learning_count = cursor.fetchone()[0]
        print(f"   Auto learning results: {auto_learning_count}")
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ?", (current_session,))
        dictionary_count = cursor.fetchone()[0]
        print(f"   Dictionary items created: {dictionary_count}")
        
        if dictionary_count > 0:
            # Check status breakdown
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM auto_learning_dictionary 
                WHERE session_id = ?
                GROUP BY status
                ORDER BY count DESC
            """, (current_session,))
            
            status_breakdown = cursor.fetchall()
            print("   Status breakdown:")
            for row in status_breakdown:
                print(f"     {row[0]}: {row[1]} items")
            
            # Show high-confidence samples
            cursor.execute("""
                SELECT section_name, item_label, confidence_score
                FROM auto_learning_dictionary 
                WHERE session_id = ? AND status = 'AUTO_APPROVED'
                LIMIT 3
            """, (current_session,))
            
            approved_samples = cursor.fetchall()
            if approved_samples:
                print("   Sample auto-approved items:")
                for row in approved_samples:
                    print(f"     {row[0]}.{row[1]} (confidence: {row[2]})")
            
            print("   ✅ AUTO LEARNING working correctly")
        else:
            print("   ❌ AUTO LEARNING not working")
        
        # 4. NEW RULE VERIFICATION
        print("\n4. 📊 NEW RULE VERIFICATION:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
        new_items_count = cursor.fetchone()[0]
        print(f"   NEW items in comparison results: {new_items_count}")
        
        if new_items_count > 0:
            # Check NEW items by section
            cursor.execute("""
                SELECT section_name, COUNT(*) as count
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW'
                GROUP BY section_name
                ORDER BY count DESC
                LIMIT 5
            """, (current_session,))
            
            new_by_section = cursor.fetchall()
            print("   NEW items by section:")
            for row in new_by_section:
                print(f"     {row[0]}: {row[1]} NEW items")
            
            print("   ✅ NEW rule working correctly")
        else:
            print("   ❌ NEW rule not detecting items")
        
        # 5. FULL WORKFLOW TEST
        print("\n5. 🔄 FULL WORKFLOW INTEGRATION TEST:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Test get_pre_reporting_data method
            print("   Testing get_pre_reporting_data...")
            pre_reporting_data = manager.get_pre_reporting_data(current_session)
            
            if pre_reporting_data.get('success') and pre_reporting_data.get('data'):
                data_count = len(pre_reporting_data.get('data', []))
                total_changes = pre_reporting_data.get('total_changes', 0)
                print(f"   ✅ get_pre_reporting_data: {data_count} items, {total_changes} total changes")
            else:
                print("   ❌ get_pre_reporting_data not working")
            
            # Test tracker feeding method
            print("   Testing tracker feeding...")
            new_items = manager._load_new_items_for_tracking()
            print(f"   ✅ _load_new_items_for_tracking: {len(new_items)} NEW items")
            
            print("   ✅ FULL WORKFLOW integration working")
        
        except Exception as e:
            print(f"   ❌ FULL WORKFLOW test failed: {e}")
        
        # 6. FINAL SUMMARY
        print("\n6. 📊 FINAL SYSTEM STATUS SUMMARY:")
        
        # Calculate totals
        total_comparison = comparison_count
        total_pre_reporting = pre_reporting_count
        total_tracker = tracker_count
        total_auto_learning = auto_learning_count
        total_dictionary = dictionary_count
        total_new_items = new_items_count
        
        print(f"   📈 Data Processing:")
        print(f"     Comparison results: {total_comparison}")
        print(f"     NEW items detected: {total_new_items}")
        
        print(f"   📋 Phase Results:")
        print(f"     PRE-REPORTING: {total_pre_reporting} items categorized")
        print(f"     TRACKER FEEDING: {total_tracker} items tracked")
        print(f"     AUTO LEARNING: {total_auto_learning} items analyzed")
        
        print(f"   🎯 Module Feeding:")
        print(f"     Bank Adviser tables: {total_bank_adviser} NEW records")
        print(f"     Dictionary Manager: {total_dictionary} items")
        
        # Overall status
        phases_working = 0
        total_phases = 3
        
        if total_pre_reporting > 0:
            phases_working += 1
        if total_bank_adviser > 0:
            phases_working += 1
        if total_dictionary > 0:
            phases_working += 1
        
        success_rate = (phases_working / total_phases) * 100
        
        print(f"\n   🎯 OVERALL SYSTEM STATUS:")
        print(f"     Working phases: {phases_working}/{total_phases}")
        print(f"     Success rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("\n🎉 ALL SYSTEMS WORKING PERFECTLY!")
            print("✅ PRE-REPORTING: Fully functional")
            print("✅ TRACKER FEEDING: Bank Adviser tables populated")
            print("✅ AUTO LEARNING: Dictionary Manager receiving data")
            print("✅ NEW RULE: Correctly applied across all phases")
            print("\n🚀 The payroll audit system is ready for production use!")
        elif success_rate >= 66:
            print(f"\n✅ SYSTEM MOSTLY WORKING ({success_rate:.1f}%)")
            print("Most components are functional with minor issues")
        else:
            print(f"\n⚠️ SYSTEM NEEDS ATTENTION ({success_rate:.1f}%)")
            print("Several components require fixes")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during final check: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_system_check()
