#!/usr/bin/env python3
"""
Fix root cause: Prevent inappropriate clearing of comparison results
Create production-safe data management
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_root_cause_data_clearing():
    """Fix root cause of inappropriate data clearing"""
    print("🔧 FIXING ROOT CAUSE: INAPPROPRIATE DATA CLEARING")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Working with session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current state of comparison results
        print("\n1. 📊 CHECKING CURRENT STATE:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (current_session,))
        extracted_count = cursor.fetchone()[0]
        print(f"   Extracted data: {extracted_count}")
        
        # 2. If comparison results are missing but extracted data exists, regenerate them
        if comparison_count == 0 and extracted_count > 0:
            print("\n2. 🔄 REGENERATING MISSING COMPARISON RESULTS:")
            
            try:
                sys.path.append(os.path.dirname(__file__))
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = current_session
                
                # Load extracted data
                current_data = manager._load_extracted_data('current')
                previous_data = manager._load_extracted_data('previous')
                
                if current_data and previous_data:
                    print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                    
                    # Generate comparison results
                    comparison_results = manager._compare_payroll_data(current_data, previous_data)
                    manager._store_comparison_results(comparison_results)
                    
                    # Verify regeneration
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                    new_comparison_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
                    new_items_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Regenerated {new_comparison_count} comparison results")
                    print(f"   ✅ Found {new_items_count} NEW items")
                else:
                    print("   ❌ Could not load extracted data")
                    return
            
            except Exception as e:
                print(f"   ❌ Regeneration failed: {e}")
                return
        
        elif comparison_count > 0:
            print("\n2. ✅ COMPARISON RESULTS EXIST:")
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
            new_items_count = cursor.fetchone()[0]
            print(f"   NEW items: {new_items_count}")
        
        # 3. Now apply the corrected NEW rule WITHOUT clearing comparison results
        print("\n3. 📊 APPLYING CORRECTED NEW RULE (WITHOUT CLEARING COMPARISON DATA):")
        
        # Only clear tracker tables, NOT comparison results
        cursor.execute("DELETE FROM in_house_loans WHERE source_session = ?", (current_session,))
        cursor.execute("DELETE FROM external_loans WHERE source_session = ?", (current_session,))
        cursor.execute("DELETE FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        conn.commit()
        print("   ✅ Cleared only tracker tables (preserved comparison results)")
        
        # 4. Fix motor vehicle table schema if needed
        print("\n4. 🔧 FIXING MOTOR VEHICLE TABLE SCHEMA:")
        
        cursor.execute("PRAGMA table_info(motor_vehicle_maintenance)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        schema_fixed = False
        if 'allowance_type' not in column_names:
            cursor.execute("ALTER TABLE motor_vehicle_maintenance ADD COLUMN allowance_type TEXT")
            schema_fixed = True
            print("   ✅ Added allowance_type column")
        
        if 'allowance_amount' not in column_names:
            cursor.execute("ALTER TABLE motor_vehicle_maintenance ADD COLUMN allowance_amount REAL")
            schema_fixed = True
            print("   ✅ Added allowance_amount column")
        
        if schema_fixed:
            conn.commit()
        else:
            print("   ✅ Schema already correct")
        
        # 5. Process NEW items with proper data extraction
        print("\n5. 📝 PROCESSING NEW ITEMS WITH PROPER DATA EXTRACTION:")
        
        # Get NEW items from comparison results
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, current_value
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW'
            AND (
                (section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F%') OR
                (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
            )
            ORDER BY employee_id, section_name, item_label
        """, (current_session,))
        
        new_items = cursor.fetchall()
        print(f"   Found {len(new_items)} NEW items to process")
        
        if len(new_items) == 0:
            print("   ⚠️ No NEW items found - checking if comparison results have the right data")
            
            # Check what NEW items exist
            cursor.execute("""
                SELECT section_name, COUNT(*) as count
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW'
                GROUP BY section_name
                ORDER BY count DESC
            """, (current_session,))
            
            new_by_section = cursor.fetchall()
            if new_by_section:
                print("   NEW items by section:")
                for row in new_by_section:
                    print(f"     {row[0]}: {row[1]} items")
            
            return
        
        # Load current data for proper employee info extraction
        try:
            current_data = manager._load_extracted_data('current')
            employee_lookup = {emp['employee_id']: emp for emp in current_data}
            in_house_loan_types = manager._load_in_house_loan_types()
            
            in_house_count = 0
            external_count = 0
            motor_count = 0
            
            for row in new_items:
                employee_id = row[0]
                section_name = row[2]
                item_label = row[3]
                current_value = row[4]
                
                # Get proper employee data
                employee_data = employee_lookup.get(employee_id)
                
                if not employee_data:
                    continue
                
                # Extract proper employee info using corrected functions
                proper_name = get_employee_name(employee_data)
                proper_department = get_employee_department(employee_data)
                
                if section_name == 'LOANS' and 'BALANCE B/F' in item_label:
                    # Process loan
                    loan_type = item_label.replace(' - BALANCE B/F', '').strip()
                    loan_amount = extract_numeric_value(current_value)
                    
                    if loan_amount > 0:
                        # Classify as in-house or external
                        is_in_house = any(in_house_type.lower() in loan_type.lower() 
                                        for in_house_type in in_house_loan_types)
                        
                        try:
                            if is_in_house:
                                cursor.execute("""
                                    INSERT INTO in_house_loans 
                                    (employee_no, employee_name, department, loan_type, loan_amount,
                                     period_month, period_year, period_acquired, source_session, remarks)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    employee_id, proper_name, proper_department, loan_type, loan_amount,
                                    '06', '2025', '2025-06', current_session, 
                                    f"NEW loan - Balance B/F: {current_value}"
                                ))
                                in_house_count += 1
                            else:
                                cursor.execute("""
                                    INSERT INTO external_loans 
                                    (employee_no, employee_name, department, loan_type, loan_amount,
                                     period_month, period_year, period_acquired, source_session)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    employee_id, proper_name, proper_department, loan_type, loan_amount,
                                    '06', '2025', '2025-06', current_session
                                ))
                                external_count += 1
                        
                        except Exception as e:
                            print(f"     ❌ Failed to insert loan for {employee_id}: {e}")
                
                elif 'MOTOR VEH' in item_label or 'VEHICLE MAINT' in item_label:
                    # Process motor vehicle allowance
                    allowance_amount = extract_numeric_value(current_value)
                    
                    if allowance_amount > 0:
                        try:
                            cursor.execute("""
                                INSERT INTO motor_vehicle_maintenance 
                                (employee_no, employee_name, department, allowance_type, allowance_amount,
                                 maintenance_amount, period_month, period_year, period_acquired, 
                                 source_session, remarks)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                employee_id, proper_name, proper_department, item_label, allowance_amount,
                                allowance_amount,  # Also populate maintenance_amount for compatibility
                                '06', '2025', '2025-06', current_session,
                                f"NEW allowance: {item_label} = {current_value}"
                            ))
                            motor_count += 1
                        
                        except Exception as e:
                            print(f"     ❌ Failed to insert motor vehicle for {employee_id}: {e}")
            
            conn.commit()
            
            print(f"   ✅ PROCESSING COMPLETED:")
            print(f"     In-house loans: {in_house_count}")
            print(f"     External loans: {external_count}")
            print(f"     Motor vehicle allowances: {motor_count}")
            print(f"     Total TRUE NEW items: {in_house_count + external_count + motor_count}")
        
        except Exception as e:
            print(f"   ❌ Processing failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Final verification
        print("\n6. ✅ FINAL VERIFICATION:")
        
        # Verify comparison results are preserved
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        final_comparison = cursor.fetchone()[0]
        print(f"   Comparison results preserved: {final_comparison}")
        
        # Verify tracker tables populated
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        final_in_house = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        final_external = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        final_motor = cursor.fetchone()[0]
        
        print(f"   In-house loans: {final_in_house}")
        print(f"   External loans: {final_external}")
        print(f"   Motor vehicle allowances: {final_motor}")
        
        total_items = final_in_house + final_external + final_motor
        
        if total_items > 0 and final_comparison > 0:
            print(f"\n🎉 ROOT CAUSE FIXED SUCCESSFULLY!")
            print("✅ Comparison results preserved (not cleared)")
            print("✅ NEW rule applied correctly")
            print("✅ Motor vehicle schema fixed")
            print("✅ Proper data extraction implemented")
            print("✅ Production-safe data management")
            print(f"\n🎯 Total TRUE NEW items: {total_items}")
        else:
            print(f"\n⚠️ Issues remain - need further investigation")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during root cause fix: {e}")
        import traceback
        traceback.print_exc()

def extract_value_from_dict(data_value):
    """Extract actual value from dictionary structure"""
    if isinstance(data_value, dict):
        if 'numeric_value' in data_value and data_value['numeric_value'] is not None:
            return data_value['numeric_value']
        elif 'value' in data_value:
            return data_value['value']
    return data_value

def extract_numeric_value(value_str):
    """Extract numeric value from string"""
    if not value_str:
        return 0.0
    
    if isinstance(value_str, dict):
        if 'numeric_value' in value_str and value_str['numeric_value'] is not None:
            return float(value_str['numeric_value'])
        elif 'value' in value_str:
            value_str = value_str['value']
        else:
            return 0.0
    
    import re
    clean_str = str(value_str).replace(',', '').strip()
    match = re.search(r'[\d,]+\.?\d*', clean_str)
    if match:
        try:
            return float(match.group().replace(',', ''))
        except:
            return 0.0
    return 0.0

def get_employee_department(employee_data):
    """Extract department from employee data properly"""
    if isinstance(employee_data, dict):
        personal_details = employee_data.get('sections', {}).get('PERSONAL DETAILS', {})
        
        if 'DEPARTMENT' in personal_details:
            dept_data = personal_details['DEPARTMENT']
            dept_value = extract_value_from_dict(dept_data)
            
            if dept_value and dept_value != 'None':
                return str(dept_value).strip()
        
        emp_id = employee_data.get('employee_id', '')
        if emp_id.startswith('COP'):
            return 'POLICE - DEPARTMENT NOT SPECIFIED'
        elif emp_id.startswith('PW'):
            return 'PUBLIC WORKS - DEPARTMENT NOT SPECIFIED'
        else:
            return 'UNKNOWN DEPARTMENT'
    
    return 'UNKNOWN DEPARTMENT'

def get_employee_name(employee_data):
    """Extract proper employee name, handling Ghana Card ID issue"""
    if isinstance(employee_data, dict):
        personal_details = employee_data.get('sections', {}).get('PERSONAL DETAILS', {})
        
        if 'EMPLOYEE NAME' in personal_details:
            name_data = personal_details['EMPLOYEE NAME']
            name_value = extract_value_from_dict(name_data)
            
            if name_value and str(name_value).strip() != 'Ghana Card ID':
                return str(name_value).strip()
        
        emp_name = employee_data.get('employee_name')
        if emp_name and str(emp_name).strip() != 'Ghana Card ID':
            return str(emp_name).strip()
        
        emp_id = employee_data.get('employee_id', '')
        return f"EMPLOYEE_{emp_id}"
    
    return "UNKNOWN EMPLOYEE"

if __name__ == "__main__":
    fix_root_cause_data_clearing()
