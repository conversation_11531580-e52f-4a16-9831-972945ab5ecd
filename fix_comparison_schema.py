#!/usr/bin/env python3
"""
Fix the comparison_results table schema to allow all change types
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_comparison_schema():
    """Fix the comparison_results table schema"""
    print("🔧 FIXING COMPARISON_RESULTS TABLE SCHEMA")
    print("=" * 60)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return
    
    print(f"📁 Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current schema
        print("\n1. 📋 CHECKING CURRENT SCHEMA:")
        cursor.execute("PRAGMA table_info(comparison_results)")
        columns = cursor.fetchall()
        
        print("   Current comparison_results columns:")
        for col in columns:
            print(f"     {col[1]} {col[2]} {col[3] if col[3] else ''}")
        
        # 2. Check if table has data
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        current_count = cursor.fetchone()[0]
        print(f"\n2. 📊 Current records in table: {current_count}")
        
        # 3. Recreate table with correct schema
        print("\n3. 🔄 RECREATING TABLE WITH CORRECT SCHEMA:")
        
        # Backup existing data if any
        backup_data = []
        if current_count > 0:
            print("   Backing up existing data...")
            cursor.execute("SELECT * FROM comparison_results")
            backup_data = cursor.fetchall()
            print(f"   Backed up {len(backup_data)} records")
        
        # Drop and recreate table
        print("   Dropping old table...")
        cursor.execute("DROP TABLE IF EXISTS comparison_results")
        
        print("   Creating new table with correct schema...")
        cursor.execute('''
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT CHECK(change_type IN ('NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED')),
                priority TEXT CHECK(priority IN ('HIGH', 'MODERATE', 'LOW')),
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Restore data if any
        if backup_data:
            print("   Restoring backed up data...")
            cursor.executemany('''
                INSERT INTO comparison_results 
                (id, session_id, employee_id, employee_name, section_name, item_label,
                 previous_value, current_value, change_type, priority, numeric_difference,
                 percentage_change, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', backup_data)
            print(f"   Restored {len(backup_data)} records")
        
        conn.commit()
        
        # 4. Verify new schema
        print("\n4. ✅ VERIFYING NEW SCHEMA:")
        cursor.execute("PRAGMA table_info(comparison_results)")
        new_columns = cursor.fetchall()
        
        print("   New comparison_results columns:")
        for col in new_columns:
            print(f"     {col[1]} {col[2]} {col[3] if col[3] else ''}")
        
        # Check record count
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        final_count = cursor.fetchone()[0]
        print(f"   Final record count: {final_count}")
        
        print("\n✅ SCHEMA FIX COMPLETED SUCCESSFULLY!")
        print("   The comparison_results table now accepts all change types:")
        print("   - NEW")
        print("   - REMOVED") 
        print("   - INCREASED")
        print("   - DECREASED")
        print("   - CHANGED")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error fixing schema: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_comparison_schema()
