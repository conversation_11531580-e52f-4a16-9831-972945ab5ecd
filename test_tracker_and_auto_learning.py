#!/usr/bin/env python3
"""
Test TRACKER FEEDING and AUTO LEARNING phases
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_tracker_and_auto_learning():
    """Test TRACKER FEEDING and AUTO LEARNING phases"""
    print("🔍 TESTING TRACKER FEEDING AND AUTO LEARNING PHASES")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current state
        print("\n1. 📊 CURRENT STATE CHECK:")
        
        # Check comparison results
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        # Check tracker results
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
        tracker_count = cursor.fetchone()[0]
        print(f"   Tracker results: {tracker_count}")
        
        # Check auto learning results
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        auto_learning_count = cursor.fetchone()[0]
        print(f"   Auto learning results: {auto_learning_count}")
        
        # Check Bank Adviser tables
        print("\n2. 📊 BANK ADVISER TABLES CHECK:")
        
        bank_adviser_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in bank_adviser_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
            except Exception as e:
                print(f"   {table}: Table not found or error - {e}")
        
        # Test the phases
        print("\n3. 🔄 TESTING TRACKER FEEDING PHASE:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Test loading NEW items for tracking
            print("   Loading NEW items for tracking...")
            new_items = manager._load_new_items_for_tracking()
            print(f"   ✅ Found {len(new_items)} NEW items for tracking")
            
            if new_items:
                print("   Sample NEW items:")
                for i, item in enumerate(new_items[:5]):
                    print(f"     {i+1}. {item['employee_id']} - {item['section_name']}.{item['item_label']} = {item['item_value']}")
                
                # Test tracker feeding phase
                print("   Running TRACKER FEEDING phase...")
                options = {'report_name': 'Test', 'report_designation': 'Test'}
                
                result = manager._phase_tracker_feeding(options)
                
                if result:
                    print("   ✅ TRACKER FEEDING phase completed successfully!")
                    
                    # Check tracker results
                    cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
                    new_tracker_count = cursor.fetchone()[0]
                    print(f"   ✅ Tracker results stored: {new_tracker_count}")
                    
                    if new_tracker_count > 0:
                        # Sample tracker results
                        cursor.execute("""
                            SELECT employee_id, tracker_type, item_label, item_value
                            FROM tracker_results 
                            WHERE session_id = ?
                            LIMIT 5
                        """, (current_session,))
                        
                        tracker_samples = cursor.fetchall()
                        print("   Sample tracker results:")
                        for row in tracker_samples:
                            print(f"     {row[0]} - {row[1]}: {row[2]} = {row[3]}")
                else:
                    print("   ❌ TRACKER FEEDING phase failed")
            else:
                print("   ⚠️ No NEW items found for tracking")
                
                # Check what NEW items exist in comparison results
                cursor.execute("""
                    SELECT section_name, item_label, COUNT(*) as count
                    FROM comparison_results 
                    WHERE session_id = ? AND change_type = 'NEW'
                    GROUP BY section_name, item_label
                    ORDER BY count DESC
                    LIMIT 10
                """, (current_session,))
                
                new_items_summary = cursor.fetchall()
                if new_items_summary:
                    print("   NEW items in comparison results:")
                    for row in new_items_summary:
                        print(f"     {row[0]}.{row[1]}: {row[2]} employees")
                else:
                    print("   ❌ No NEW items found in comparison results")
        
        except Exception as e:
            print(f"   ❌ TRACKER FEEDING test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test AUTO LEARNING phase
        print("\n4. 🔄 TESTING AUTO LEARNING PHASE:")
        
        try:
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Test loading current data for learning
            print("   Loading current data for learning...")
            current_data = manager._load_extracted_data('current')
            
            if current_data:
                print(f"   ✅ Loaded {len(current_data)} employees for learning")
                
                # Test auto learning phase
                print("   Running AUTO LEARNING phase...")
                options = {'report_name': 'Test', 'report_designation': 'Test'}
                
                result = manager._phase_auto_learning(options)
                
                if result:
                    print("   ✅ AUTO LEARNING phase completed successfully!")
                    
                    # Check auto learning results
                    cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
                    new_auto_learning_count = cursor.fetchone()[0]
                    print(f"   ✅ Auto learning results stored: {new_auto_learning_count}")
                    
                    if new_auto_learning_count > 0:
                        # Sample auto learning results
                        cursor.execute("""
                            SELECT section_name, item_label, confidence_score
                            FROM auto_learning_results 
                            WHERE session_id = ?
                            LIMIT 5
                        """, (current_session,))
                        
                        learning_samples = cursor.fetchall()
                        print("   Sample auto learning results:")
                        for row in learning_samples:
                            print(f"     {row[0]}.{row[1]} (confidence: {row[2]})")
                    
                    # Check pending items
                    cursor.execute("SELECT COUNT(*) FROM pending_items WHERE session_id = ?", (current_session,))
                    pending_count = cursor.fetchone()[0]
                    print(f"   ✅ Pending items for review: {pending_count}")
                    
                else:
                    print("   ❌ AUTO LEARNING phase failed")
            else:
                print("   ❌ No current data found for learning")
        
        except Exception as e:
            print(f"   ❌ AUTO LEARNING test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Final summary
        print("\n5. 📊 FINAL SUMMARY:")
        
        # Check all relevant tables
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
        final_tracker_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        final_auto_learning_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pending_items WHERE session_id = ?", (current_session,))
        final_pending_count = cursor.fetchone()[0]
        
        print(f"   Tracker results: {final_tracker_count}")
        print(f"   Auto learning results: {final_auto_learning_count}")
        print(f"   Pending items: {final_pending_count}")
        
        if final_tracker_count > 0 or final_auto_learning_count > 0:
            print("   ✅ At least one phase is working!")
        else:
            print("   ❌ Both phases may have issues")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tracker_and_auto_learning()
