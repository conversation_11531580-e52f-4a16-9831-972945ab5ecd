#!/usr/bin/env python3
"""
Migrate to Unified Session Management System
This script replaces all old session management with the new unified system
"""

import sys
import os
import sqlite3

def migrate_to_unified_session_management():
    """Migrate to the new unified session management system"""
    print("🔄 MIGRATING TO UNIFIED SESSION MANAGEMENT")
    print("=" * 70)
    
    try:
        # 1. Initialize the unified session manager
        print("\n1. 🏗️ INITIALIZING UNIFIED SESSION MANAGER:")
        
        sys.path.append('.')
        from core.unified_session_manager import get_unified_session_manager
        
        unified_manager = get_unified_session_manager()
        print("   ✅ Unified session manager initialized")
        
        # 2. Find the best session with data
        print("\n2. 🔍 FINDING BEST SESSION WITH DATA:")
        
        db_path = "data/templar_payroll_auditor.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Find sessions with pre-reporting data (most complete)
        cursor.execute("""
            SELECT a.session_id, a.created_at, COUNT(p.id) as pre_reporting_count,
                   COUNT(c.id) as comparison_count, COUNT(e.id) as extracted_count
            FROM audit_sessions a
            LEFT JOIN pre_reporting_results p ON a.session_id = p.session_id
            LEFT JOIN comparison_results c ON a.session_id = c.session_id  
            LEFT JOIN extracted_data e ON a.session_id = e.session_id
            GROUP BY a.session_id, a.created_at
            HAVING pre_reporting_count > 0 OR comparison_count > 0 OR extracted_count > 0
            ORDER BY pre_reporting_count DESC, comparison_count DESC, extracted_count DESC, a.created_at DESC
            LIMIT 5
        """)
        
        sessions_with_data = cursor.fetchall()
        
        if not sessions_with_data:
            print("   ❌ No sessions with data found")
            return
        
        best_session = sessions_with_data[0]
        session_id = best_session[0]
        pre_reporting_count = best_session[2]
        comparison_count = best_session[3]
        extracted_count = best_session[4]
        
        print(f"   ✅ Best session found: {session_id}")
        print(f"   - Pre-reporting data: {pre_reporting_count}")
        print(f"   - Comparison data: {comparison_count}")
        print(f"   - Extracted data: {extracted_count}")
        
        # 3. Set as current session using unified manager
        print(f"\n3. 🎯 SETTING CURRENT SESSION:")
        
        unified_manager.set_current_session(session_id, f"Migrated_Session_{session_id[-8:]}")
        
        # Verify it worked
        current_session = unified_manager.get_current_session_id()
        print(f"   ✅ Current session set to: {current_session}")
        
        if current_session == session_id:
            print("   ✅ Session migration successful!")
        else:
            print(f"   ❌ Session migration failed: {current_session} != {session_id}")
            return
        
        # 4. Update phase statuses based on data
        print(f"\n4. 🔧 UPDATING PHASE STATUSES:")
        
        # Check what phases have data and update their status
        if extracted_count > 0:
            unified_manager.update_phase_status('EXTRACTION', 'COMPLETED', extracted_count)
            print(f"   ✅ EXTRACTION: COMPLETED ({extracted_count} records)")
        
        if comparison_count > 0:
            unified_manager.update_phase_status('COMPARISON', 'COMPLETED', comparison_count)
            print(f"   ✅ COMPARISON: COMPLETED ({comparison_count} records)")
        
        if pre_reporting_count > 0:
            # PRE_REPORTING should be WAITING_FOR_USER if it has data
            unified_manager.update_phase_status('PRE_REPORTING', 'WAITING_FOR_USER', pre_reporting_count)
            print(f"   ✅ PRE_REPORTING: WAITING_FOR_USER ({pre_reporting_count} records)")
            
            # Update session status to pre_reporting_ready
            cursor.execute("""
                UPDATE audit_sessions SET status = 'pre_reporting_ready' WHERE session_id = ?
            """, (session_id,))
            conn.commit()
            print(f"   ✅ Session status: pre_reporting_ready")
        
        # 5. Test the unified system
        print(f"\n5. 🧪 TESTING UNIFIED SYSTEM:")
        
        # Test getting session status
        status = unified_manager.get_session_status()
        print(f"   Session ID: {status['session_id']}")
        print(f"   Session Status: {status['session_status']}")
        print(f"   Phases:")
        
        for phase in status['phases']:
            print(f"     {phase['name']}: {phase['status']} ({phase['data_count']} items)")
        
        # Test getting pre-reporting data
        pre_reporting_data = unified_manager.get_pre_reporting_data()
        print(f"   Pre-reporting data: {pre_reporting_data['success']}")
        if pre_reporting_data['success']:
            print(f"   Total changes: {pre_reporting_data['total_changes']}")
        
        # 6. Update phased_process_manager to use unified system
        print(f"\n6. 🔧 UPDATING PHASED PROCESS MANAGER:")
        
        # The phased_process_manager.py needs to be updated to use unified session manager
        print("   📝 Next step: Update phased_process_manager.py to use unified_session_manager")
        print("   📝 Next step: Update get-latest-pre-reporting-data command")
        
        conn.close()
        
        print(f"\n🎉 UNIFIED SESSION MANAGEMENT MIGRATION COMPLETED!")
        print(f"   ✅ Current session: {session_id}")
        print(f"   ✅ PRE_REPORTING phase: WAITING_FOR_USER")
        print(f"   ✅ System ready for user interaction")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Update phased_process_manager.py to use unified_session_manager")
        print(f"   2. Update all get-latest-pre-reporting-data calls")
        print(f"   3. Test the Pre-Reporting UI")
        print(f"   4. Verify session consistency across all components")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    migrate_to_unified_session_management()
