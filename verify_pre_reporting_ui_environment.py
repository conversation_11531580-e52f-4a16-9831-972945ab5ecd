#!/usr/bin/env python3
"""
Verify that the Payroll Audit tab, process manager, and phase manager 
are properly configured for Pre-Reporting UI to appear with loaded data
"""

import sys
import os
import sqlite3
import json

def verify_pre_reporting_environment():
    """Verify the complete Pre-Reporting UI environment"""
    print("🔍 VERIFYING PRE-REPORTING UI ENVIRONMENT")
    print("=" * 70)
    
    try:
        sys.path.append('.')
        
        # 1. Test Unified Session Manager
        print("\n1. 🏗️ TESTING UNIFIED SESSION MANAGER:")
        
        from core.unified_session_manager import get_unified_session_manager
        unified_manager = get_unified_session_manager()
        
        current_session = unified_manager.get_current_session_id()
        print(f"   ✅ Current session: {current_session}")
        
        session_status = unified_manager.get_session_status()
        print(f"   ✅ Session status: {session_status['session_status']}")
        
        # Check PRE_REPORTING phase
        pre_reporting_phase = next((p for p in session_status['phases'] if p['name'] == 'PRE_REPORTING'), None)
        if pre_reporting_phase:
            print(f"   ✅ PRE_REPORTING phase: {pre_reporting_phase['status']} ({pre_reporting_phase['data_count']} items)")
        else:
            print(f"   ❌ PRE_REPORTING phase not found")
        
        # 2. Test Pre-Reporting Data Availability
        print("\n2. 📊 TESTING PRE-REPORTING DATA:")
        
        pre_reporting_data = unified_manager.get_pre_reporting_data()
        print(f"   Success: {pre_reporting_data['success']}")
        print(f"   Total changes: {pre_reporting_data.get('total_changes', 0)}")
        print(f"   Session ID: {pre_reporting_data.get('session_id', 'None')}")
        
        if pre_reporting_data['success'] and pre_reporting_data['total_changes'] > 0:
            print(f"   ✅ Pre-reporting data is available and ready")
            
            # Show sample data structure
            if 'data' in pre_reporting_data and pre_reporting_data['data']:
                sample = pre_reporting_data['data'][0]
                print(f"   📋 Sample data structure:")
                for key in list(sample.keys())[:5]:  # Show first 5 keys
                    print(f"      {key}: {sample[key]}")
        else:
            print(f"   ❌ Pre-reporting data not available")
        
        # 3. Test Phased Process Manager Integration
        print("\n3. 🔧 TESTING PHASED PROCESS MANAGER INTEGRATION:")
        
        from core.phased_process_manager import PhasedProcessManager
        manager = PhasedProcessManager(debug_mode=True)
        
        # Test get_pre_reporting_data method
        phased_data = manager.get_pre_reporting_data()
        print(f"   Success: {phased_data['success']}")
        print(f"   Total changes: {phased_data.get('total_changes', 0)}")
        print(f"   Session ID: {phased_data.get('session_id', 'None')}")
        
        if phased_data['success'] and phased_data['total_changes'] > 0:
            print(f"   ✅ Phased process manager integration working")
        else:
            print(f"   ❌ Phased process manager integration failed")
        
        # 4. Test IPC Handler (get-latest-pre-reporting-data command)
        print("\n4. 📡 TESTING IPC HANDLER COMMAND:")
        
        # Simulate the IPC command that the UI calls
        import subprocess
        import json
        
        try:
            result = subprocess.run([
                'python', 
                'core/phased_process_manager.py', 
                'get-latest-pre-reporting-data'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                try:
                    ipc_data = json.loads(result.stdout)
                    print(f"   ✅ IPC command successful")
                    print(f"   Success: {ipc_data.get('success', False)}")
                    print(f"   Total changes: {ipc_data.get('total_changes', 0)}")
                    print(f"   Session ID: {ipc_data.get('session_id', 'None')}")
                    
                    if ipc_data.get('success') and ipc_data.get('total_changes', 0) > 0:
                        print(f"   ✅ IPC handler returns correct data")
                    else:
                        print(f"   ❌ IPC handler returns no data")
                        print(f"   Raw output: {result.stdout[:200]}...")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ IPC command returned invalid JSON: {e}")
                    print(f"   Raw output: {result.stdout[:200]}...")
            else:
                print(f"   ❌ IPC command failed with return code: {result.returncode}")
                print(f"   Error: {result.stderr[:200]}...")
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ IPC command timed out")
        except Exception as e:
            print(f"   ❌ IPC command error: {e}")
        
        # 5. Check Database State
        print("\n5. 🗄️ CHECKING DATABASE STATE:")
        
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check current_session table
        cursor.execute("SELECT session_id, status FROM current_session WHERE id = 1")
        current_session_row = cursor.fetchone()
        if current_session_row:
            print(f"   ✅ Current session in DB: {current_session_row[0]}")
            print(f"   ✅ Status: {current_session_row[1]}")
        else:
            print(f"   ❌ No current session in database")
        
        # Check session_phases table
        cursor.execute("""
            SELECT phase_name, status, data_count 
            FROM session_phases 
            WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
        """, (current_session,))
        
        phase_row = cursor.fetchone()
        if phase_row:
            print(f"   ✅ PRE_REPORTING phase in DB: {phase_row[1]} ({phase_row[2]} items)")
        else:
            print(f"   ❌ PRE_REPORTING phase not found in database")
        
        # Check pre_reporting_results table
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   ✅ Pre-reporting records in DB: {pre_reporting_count}")
        
        conn.close()
        
        # 6. Final Environment Assessment
        print("\n6. 🎯 FINAL ENVIRONMENT ASSESSMENT:")
        
        all_checks = [
            unified_manager and current_session,
            pre_reporting_phase and pre_reporting_phase['status'] == 'WAITING_FOR_USER',
            pre_reporting_data['success'] and pre_reporting_data['total_changes'] > 0,
            phased_data['success'] and phased_data['total_changes'] > 0,
            pre_reporting_count > 0
        ]
        
        if all(all_checks):
            print(f"   🎉 ENVIRONMENT IS READY FOR PRE-REPORTING UI!")
            print(f"   ✅ Session Management: Working")
            print(f"   ✅ Phase Status: WAITING_FOR_USER")
            print(f"   ✅ Data Availability: {pre_reporting_count} records")
            print(f"   ✅ IPC Integration: Working")
            print(f"   ✅ Database State: Consistent")
            
            print(f"\n📋 NEXT STEPS FOR UI:")
            print(f"   1. Payroll Audit tab should detect WAITING_FOR_USER status")
            print(f"   2. UI should call get-latest-pre-reporting-data")
            print(f"   3. Pre-Reporting interface should load with {pre_reporting_count} changes")
            print(f"   4. User can review and approve changes")
            print(f"   5. Click 'Generate Report' to complete the phase")
            
        else:
            print(f"   ❌ ENVIRONMENT HAS ISSUES:")
            print(f"   Session Manager: {'✅' if all_checks[0] else '❌'}")
            print(f"   Phase Status: {'✅' if all_checks[1] else '❌'}")
            print(f"   Unified Data: {'✅' if all_checks[2] else '❌'}")
            print(f"   Phased Data: {'✅' if all_checks[3] else '❌'}")
            print(f"   Database Records: {'✅' if all_checks[4] else '❌'}")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_pre_reporting_environment()
