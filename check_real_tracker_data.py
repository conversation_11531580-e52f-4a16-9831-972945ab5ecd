#!/usr/bin/env python3
"""
Check real data in the database for tracker feeding
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_real_tracker_data():
    """Check real data for tracker feeding"""
    print("🔍 CHECKING REAL DATA FOR TRACKER FEEDING")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Checking session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check extracted data for loan-related items
        print("\n1. 📊 LOAN-RELATED ITEMS IN EXTRACTED DATA:")
        cursor.execute("""
            SELECT DISTINCT section_name, item_label
            FROM extracted_data 
            WHERE session_id = ? AND (
                section_name LIKE '%LOAN%' OR
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%ADVANCE%' OR
                item_label LIKE '%BALANCE%' OR
                item_label LIKE '%DEDUCTION%'
            )
            ORDER BY section_name, item_label
            LIMIT 20
        """, (current_session,))
        
        loan_items = cursor.fetchall()
        if loan_items:
            print("   Loan-related items found in extracted data:")
            for row in loan_items:
                print(f"     {row[0]}.{row[1]}")
        else:
            print("   ❌ No loan-related items found in extracted data")
        
        # 2. Check for motor vehicle items
        print("\n2. 📊 MOTOR VEHICLE ITEMS IN EXTRACTED DATA:")
        cursor.execute("""
            SELECT DISTINCT section_name, item_label
            FROM extracted_data 
            WHERE session_id = ? AND (
                item_label LIKE '%MOTOR%' OR
                item_label LIKE '%VEHICLE%' OR
                item_label LIKE '%MAINTENAN%'
            )
            ORDER BY section_name, item_label
        """, (current_session,))
        
        motor_items = cursor.fetchall()
        if motor_items:
            print("   Motor vehicle items found in extracted data:")
            for row in motor_items:
                print(f"     {row[0]}.{row[1]}")
        else:
            print("   ❌ No motor vehicle items found in extracted data")
        
        # 3. Check all sections in extracted data
        print("\n3. 📊 ALL SECTIONS IN EXTRACTED DATA:")
        cursor.execute("""
            SELECT section_name, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY section_name
            ORDER BY count DESC
        """, (current_session,))
        
        sections = cursor.fetchall()
        for row in sections:
            print(f"   {row[0]}: {row[1]} items")
        
        # 4. Check specific loan section items
        print("\n4. 📊 ITEMS IN LOANS SECTION:")
        cursor.execute("""
            SELECT item_label, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ? AND section_name = 'LOANS'
            GROUP BY item_label
            ORDER BY count DESC
            LIMIT 15
        """, (current_session,))
        
        loans_section_items = cursor.fetchall()
        if loans_section_items:
            print("   Items in LOANS section:")
            for row in loans_section_items:
                print(f"     {row[0]}: {row[1]} employees")
        else:
            print("   ❌ No items found in LOANS section")
        
        # 5. Check comparison results for NEW items with real data
        print("\n5. 📊 CHECKING COMPARISON RESULTS FOR NEW ITEMS:")
        cursor.execute("""
            SELECT change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
            GROUP BY change_type
            ORDER BY count DESC
        """, (current_session,))
        
        change_types = cursor.fetchall()
        for row in change_types:
            print(f"   {row[0]}: {row[1]} items")
        
        # 6. Look for specific trackable items in comparison results
        print("\n6. 📊 TRACKABLE ITEMS IN COMPARISON RESULTS:")
        cursor.execute("""
            SELECT section_name, item_label, change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND (
                section_name = 'LOANS' OR
                item_label LIKE '%MOTOR%' OR
                item_label LIKE '%ADVANCE%' OR
                item_label LIKE '%BALANCE%'
            )
            GROUP BY section_name, item_label, change_type
            ORDER BY count DESC
            LIMIT 20
        """, (current_session,))
        
        trackable_items = cursor.fetchall()
        if trackable_items:
            print("   Trackable items in comparison results:")
            for row in trackable_items:
                print(f"     {row[0]}.{row[1]} ({row[2]}): {row[3]} employees")
        else:
            print("   ❌ No trackable items found in comparison results")
        
        # 7. Test direct tracker feeding with real data
        print("\n7. 🔄 TESTING DIRECT TRACKER FEEDING WITH REAL DATA:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Get all comparison results (not just NEW ones)
            print("   Loading all comparison results...")
            cursor.execute("""
                SELECT employee_id, employee_name, section_name, item_label, current_value, change_type
                FROM comparison_results 
                WHERE session_id = ? AND (
                    section_name = 'LOANS' OR
                    item_label LIKE '%MOTOR%' OR
                    item_label LIKE '%ADVANCE%'
                )
                LIMIT 10
            """, (current_session,))
            
            all_trackable = cursor.fetchall()
            
            if all_trackable:
                print(f"   Found {len(all_trackable)} potentially trackable items")
                
                # Test tracker feeding with these items
                tracked_count = 0
                in_house_loan_types = manager._load_in_house_loan_types()
                print(f"   In-house loan types: {in_house_loan_types}")
                
                for row in all_trackable:
                    item_data = {
                        'employee_id': row[0],
                        'employee_name': row[1],
                        'section_name': row[2],
                        'item_label': row[3],
                        'item_value': row[4]
                    }
                    
                    print(f"   Testing item: {item_data['section_name']}.{item_data['item_label']} = {item_data['item_value']}")
                    
                    # Test if trackable
                    if manager._is_trackable_item(item_data):
                        tracker_type = manager._classify_tracker_type(item_data, in_house_loan_types)
                        print(f"     ✅ Trackable as: {tracker_type}")
                        
                        if tracker_type:
                            try:
                                manager._store_tracker_item(item_data, tracker_type)
                                tracked_count += 1
                                print(f"     ✅ Stored in tracker")
                            except Exception as e:
                                print(f"     ❌ Failed to store: {e}")
                    else:
                        print(f"     ❌ Not trackable")
                
                print(f"   ✅ Successfully tracked {tracked_count} items")
                
                # Check tracker results
                cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
                tracker_count = cursor.fetchone()[0]
                print(f"   ✅ Total tracker results: {tracker_count}")
                
                if tracker_count > 0:
                    # Sample tracker results
                    cursor.execute("""
                        SELECT employee_id, tracker_type, item_label, item_value
                        FROM tracker_results 
                        WHERE session_id = ?
                        LIMIT 5
                    """, (current_session,))
                    
                    tracker_samples = cursor.fetchall()
                    print("   Sample tracker results:")
                    for row in tracker_samples:
                        print(f"     {row[0]} - {row[1]}: {row[2]} = {row[3]}")
            else:
                print("   ❌ No trackable items found")
        
        except Exception as e:
            print(f"   ❌ Direct tracker feeding test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 8. Check Bank Adviser tables
        print("\n8. 📊 BANK ADVISER TABLES STATUS:")
        
        bank_adviser_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in bank_adviser_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    samples = cursor.fetchall()
                    print(f"     Sample records: {len(samples)}")
            except Exception as e:
                print(f"   {table}: Error - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during check: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_real_tracker_data()
