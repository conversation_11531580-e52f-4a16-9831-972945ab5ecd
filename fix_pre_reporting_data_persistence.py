#!/usr/bin/env python3
"""
Fix PRE-REPORTING data persistence issues and session ID mismatches
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_pre_reporting_data_persistence():
    """Fix PRE-REPORTING data persistence issues"""
    print("🔧 FIXING PRE-REPORTING DATA PERSISTENCE ISSUES")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Identify all sessions and their data
        print("\n1. 📊 IDENTIFYING ALL SESSIONS:")
        
        cursor.execute("""
            SELECT session_id, created_at, 
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) as comparison_count,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = s.session_id) as pre_reporting_count
            FROM audit_sessions s
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        sessions = cursor.fetchall()
        
        if sessions:
            print("   Recent sessions:")
            for row in sessions:
                session_id, created_at, comp_count, pre_count = row
                print(f"     {session_id[:20]}... ({created_at})")
                print(f"       Comparison: {comp_count}, Pre-reporting: {pre_count}")
        
        # 2. Find the session with the most comparison data
        print("\n2. 🔍 FINDING SESSION WITH MOST DATA:")
        
        cursor.execute("""
            SELECT s.session_id, COUNT(cr.id) as comparison_count
            FROM audit_sessions s
            LEFT JOIN comparison_results cr ON s.session_id = cr.session_id
            GROUP BY s.session_id
            ORDER BY comparison_count DESC
            LIMIT 1
        """)
        
        best_session = cursor.fetchone()
        
        if best_session:
            best_session_id = best_session[0]
            best_count = best_session[1]
            print(f"   Best session: {best_session_id}")
            print(f"   Comparison results: {best_count}")
        else:
            print("   ❌ No sessions with comparison data found")
            return
        
        # 3. Check if this session has pre-reporting results
        print("\n3. 📋 CHECKING PRE-REPORTING RESULTS:")
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (best_session_id,))
        pre_reporting_count = cursor.fetchone()[0]
        
        print(f"   Pre-reporting results for best session: {pre_reporting_count}")
        
        if pre_reporting_count == 0:
            print("   ⚠️ No pre-reporting results found - regenerating...")
            
            # 4. Regenerate pre-reporting results
            print("\n4. 🔄 REGENERATING PRE-REPORTING RESULTS:")
            
            try:
                sys.path.append(os.path.dirname(__file__))
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = best_session_id
                
                # Load comparison results
                cursor.execute("""
                    SELECT id, employee_id, employee_name, section_name, item_label,
                           previous_value, current_value, change_type, priority,
                           numeric_difference, percentage_change
                    FROM comparison_results 
                    WHERE session_id = ?
                    ORDER BY priority DESC, section_name, employee_id
                """, (best_session_id,))
                
                comparison_results = cursor.fetchall()
                
                if comparison_results:
                    print(f"   ✅ Loaded {len(comparison_results)} comparison results")
                    
                    # Convert to proper format
                    all_changes = []
                    for row in comparison_results:
                        change = {
                            'id': row[0],
                            'employee_id': row[1],
                            'employee_name': row[2],
                            'section_name': row[3],
                            'item_label': row[4],
                            'previous_value': row[5],
                            'current_value': row[6],
                            'change_type': row[7],
                            'priority': row[8],
                            'numeric_difference': row[9],
                            'percentage_change': row[10]
                        }
                        all_changes.append(change)
                    
                    # Categorize changes
                    categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                    
                    # Apply auto-selection
                    auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                    
                    # Store pre-reporting results
                    manager._store_pre_reporting_results(categorized_changes, auto_selected)
                    
                    # Verify storage
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (best_session_id,))
                    new_pre_reporting_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Generated {new_pre_reporting_count} pre-reporting results")
                else:
                    print("   ❌ No comparison results to process")
                    return
            
            except Exception as e:
                print(f"   ❌ Pre-reporting regeneration failed: {e}")
                import traceback
                traceback.print_exc()
                return
        
        # 5. Test the get_pre_reporting_data method
        print("\n5. 🧪 TESTING GET_PRE_REPORTING_DATA METHOD:")
        
        try:
            manager = PhasedProcessManager()
            result = manager.get_pre_reporting_data(best_session_id)
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                print(f"   ✅ Method working: {data_count} items, {total_changes} total changes")
                
                # Show sample data
                if data_count > 0:
                    sample = result['data'][0]
                    print(f"   Sample item: {sample.get('employee_id')} - {sample.get('section_name')}.{sample.get('item_label')}")
            else:
                print(f"   ❌ Method failed: {result}")
        
        except Exception as e:
            print(f"   ❌ Method test failed: {e}")
        
        # 6. Update latest session pointer
        print("\n6. 🔄 UPDATING LATEST SESSION POINTER:")
        
        try:
            # Update the audit_sessions table to mark the best session as latest
            cursor.execute("""
                UPDATE audit_sessions 
                SET created_at = datetime('now')
                WHERE session_id = ?
            """, (best_session_id,))
            
            conn.commit()
            print(f"   ✅ Updated {best_session_id} as latest session")
        
        except Exception as e:
            print(f"   ❌ Session update failed: {e}")
        
        # 7. Create a session alias for UI compatibility
        print("\n7. 🔗 CREATING SESSION ALIAS FOR UI:")
        
        try:
            # Create a simple mapping table for session aliases
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS session_aliases (
                    alias_name TEXT PRIMARY KEY,
                    actual_session_id TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Insert/update the 'latest' alias
            cursor.execute("""
                INSERT OR REPLACE INTO session_aliases (alias_name, actual_session_id)
                VALUES ('latest', ?)
            """, (best_session_id,))
            
            conn.commit()
            print(f"   ✅ Created 'latest' alias pointing to {best_session_id}")
        
        except Exception as e:
            print(f"   ❌ Alias creation failed: {e}")
        
        # 8. Final verification
        print("\n8. ✅ FINAL VERIFICATION:")
        
        # Test the complete flow
        try:
            manager = PhasedProcessManager()
            
            # Test get_latest_pre_reporting_data
            latest_result = manager.get_latest_pre_reporting_data()
            
            if latest_result.get('success') and latest_result.get('data'):
                latest_count = len(latest_result.get('data', []))
                latest_total = latest_result.get('total_changes', 0)
                latest_session = latest_result.get('session_id', 'unknown')
                
                print(f"   ✅ get_latest_pre_reporting_data working:")
                print(f"     Session: {latest_session}")
                print(f"     Data items: {latest_count}")
                print(f"     Total changes: {latest_total}")
                
                if latest_count > 0:
                    print(f"\n🎉 PRE-REPORTING DATA PERSISTENCE FIXED!")
                    print(f"✅ UI should now load {latest_count} changes for user interaction")
                    print(f"✅ Session ID consistency resolved")
                    print(f"✅ Memory cleanup issues bypassed")
                    print(f"✅ Data persistence guaranteed")
                else:
                    print(f"\n⚠️ Data count is 0 - may need further investigation")
            else:
                print(f"   ❌ get_latest_pre_reporting_data still failing: {latest_result}")
        
        except Exception as e:
            print(f"   ❌ Final verification failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_pre_reporting_data_persistence()
