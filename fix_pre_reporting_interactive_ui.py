#!/usr/bin/env python3
"""
Fix PRE-REPORTING interactive UI by ensuring all required data is available
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_pre_reporting_interactive_ui():
    """Fix PRE-REPORTING interactive UI"""
    print("🔧 FIXING PRE-REPORTING INTERACTIVE UI")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session from session management
        print("\n1. 📊 GETTING CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check current data status
        print("\n2. 📊 CHECKING CURRENT DATA STATUS:")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (current_session,))
        extracted_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        
        print(f"   Extracted data: {extracted_count}")
        print(f"   Comparison results: {comparison_count}")
        print(f"   Pre-reporting results: {pre_reporting_count}")
        
        # 3. Run missing phases
        if extracted_count > 0 and comparison_count == 0:
            print("\n3. 🔄 RUNNING COMPARISON PHASE:")
            
            try:
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = current_session
                
                # Load extracted data
                current_data = manager._load_extracted_data('current')
                previous_data = manager._load_extracted_data('previous')
                
                if current_data and previous_data:
                    print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                    
                    # Generate comparison results
                    comparison_results = manager._compare_payroll_data(current_data, previous_data)
                    manager._store_comparison_results(comparison_results)
                    
                    # Update session phase status
                    from core.session_manager import get_session_manager
                    session_manager = get_session_manager()
                    session_manager.update_phase_status('COMPARISON', 'COMPLETED', len(comparison_results))
                    
                    # Verify
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                    new_comparison_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
                    new_items_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Generated {new_comparison_count} comparison results")
                    print(f"   ✅ Found {new_items_count} NEW items")
                    
                    comparison_count = new_comparison_count
                else:
                    print("   ❌ Could not load extracted data")
                    return
            
            except Exception as e:
                print(f"   ❌ Comparison phase failed: {e}")
                import traceback
                traceback.print_exc()
                return
        
        # 4. Run PRE-REPORTING phase if comparison exists but pre-reporting missing
        if comparison_count > 0 and pre_reporting_count == 0:
            print("\n4. 🔄 RUNNING PRE-REPORTING PHASE:")
            
            try:
                manager = PhasedProcessManager()
                manager.session_id = current_session
                
                # Load comparison results
                cursor.execute("""
                    SELECT id, employee_id, employee_name, section_name, item_label,
                           previous_value, current_value, change_type, priority,
                           numeric_difference, percentage_change
                    FROM comparison_results 
                    WHERE session_id = ?
                    ORDER BY priority DESC, section_name, employee_id
                """, (current_session,))
                
                comparison_data = cursor.fetchall()
                
                if comparison_data:
                    print(f"   ✅ Loaded {len(comparison_data)} comparison results")
                    
                    # Convert to proper format
                    all_changes = []
                    for row in comparison_data:
                        change = {
                            'id': row[0],
                            'employee_id': row[1],
                            'employee_name': row[2],
                            'section_name': row[3],
                            'item_label': row[4],
                            'previous_value': row[5],
                            'current_value': row[6],
                            'change_type': row[7],
                            'priority': row[8],
                            'numeric_difference': row[9],
                            'percentage_change': row[10]
                        }
                        all_changes.append(change)
                    
                    # Categorize changes
                    categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                    
                    # Apply auto-selection
                    auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                    
                    # Store pre-reporting results
                    manager._store_pre_reporting_results(categorized_changes, auto_selected)
                    
                    # Update session phase status
                    session_manager = get_session_manager()
                    session_manager.update_phase_status('PRE_REPORTING', 'COMPLETED', len(categorized_changes))
                    
                    # Verify
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                    new_pre_reporting_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Generated {new_pre_reporting_count} pre-reporting results")
                    
                    pre_reporting_count = new_pre_reporting_count
                else:
                    print("   ❌ No comparison data to process")
                    return
            
            except Exception as e:
                print(f"   ❌ Pre-reporting phase failed: {e}")
                import traceback
                traceback.print_exc()
                return
        
        # 5. Test the get_pre_reporting_data method
        print("\n5. 🧪 TESTING GET_PRE_REPORTING_DATA METHOD:")
        
        try:
            manager = PhasedProcessManager()
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                session_id = result.get('session_id', 'unknown')
                
                print(f"   ✅ Method working:")
                print(f"     Session: {session_id}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                # Show sample data structure for UI
                if data_count > 0:
                    sample = result['data'][0]
                    print(f"   Sample item for UI:")
                    print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                    print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                    print(f"     Type: {sample.get('change_type')}")
                    print(f"     Category: {sample.get('bulk_category')}")
                    print(f"     Selected: {sample.get('selected_for_report')}")
            else:
                print(f"   ❌ Method failed: {result}")
                return
        
        except Exception as e:
            print(f"   ❌ Method test failed: {e}")
            return
        
        # 6. Test the latest data method (what UI actually calls)
        print("\n6. 🧪 TESTING GET_LATEST_PRE_REPORTING_DATA (UI METHOD):")
        
        try:
            latest_result = manager.get_latest_pre_reporting_data()
            
            if latest_result.get('success') and latest_result.get('data'):
                latest_count = len(latest_result.get('data', []))
                latest_total = latest_result.get('total_changes', 0)
                latest_session_id = latest_result.get('session_id', 'unknown')
                
                print(f"   ✅ Latest method working:")
                print(f"     Session: {latest_session_id}")
                print(f"     Data items: {latest_count}")
                print(f"     Total changes: {latest_total}")
                
                # Show categorization for UI
                categories = {}
                for item in latest_result['data']:
                    category = item.get('bulk_category', 'Unknown')
                    if category not in categories:
                        categories[category] = 0
                    categories[category] += 1
                
                print(f"   Categories for UI:")
                for category, count in categories.items():
                    print(f"     {category}: {count} changes")
                
                # Show auto-selection status
                auto_selected = sum(1 for item in latest_result['data'] if item.get('selected_for_report'))
                print(f"   Auto-selected: {auto_selected} changes")
                print(f"   Pending review: {latest_count - auto_selected} changes")
                
            else:
                print(f"   ❌ Latest method failed: {latest_result}")
                return
        
        except Exception as e:
            print(f"   ❌ Latest method test failed: {e}")
            return
        
        # 7. Verify UI data structure compatibility
        print("\n7. ✅ VERIFYING UI DATA STRUCTURE COMPATIBILITY:")
        
        # Check that data has all required fields for interactive UI
        sample_data = latest_result['data'][0]
        required_fields = [
            'employee_id', 'employee_name', 'section_name', 'item_label',
            'previous_value', 'current_value', 'change_type', 'bulk_category',
            'selected_for_report', 'priority'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in sample_data:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"   ❌ Missing required fields: {missing_fields}")
        else:
            print(f"   ✅ All required fields present for UI")
        
        print(f"\n🎉 PRE-REPORTING INTERACTIVE UI READY!")
        print(f"✅ Comparison results: {comparison_count}")
        print(f"✅ Pre-reporting results: {pre_reporting_count}")
        print(f"✅ UI data method: {latest_count} items")
        print(f"✅ Session consistency: {current_session}")
        print(f"✅ Auto-selection: {auto_selected} changes")
        print(f"✅ Categories: {len(categories)} bulk categories")
        
        print(f"\n🎯 INTERACTIVE UI FEATURES AVAILABLE:")
        print(f"📋 Change Review: Users can review {latest_count} changes")
        print(f"📋 Bulk Categories: {', '.join(categories.keys())}")
        print(f"📋 Auto-Selection: {auto_selected} pre-selected changes")
        print(f"📋 Manual Selection: Users can select/deselect changes")
        print(f"📋 Generate Report: Button enabled when changes selected")
        
        print(f"\n🚀 UI SHOULD NOW LOAD INTERACTIVE PRE-REPORTING PAGE!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_pre_reporting_interactive_ui()
