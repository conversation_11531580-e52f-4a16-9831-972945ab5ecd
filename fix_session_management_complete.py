#!/usr/bin/env python3
"""
Complete session management fix with proper column handling
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_session_management_complete():
    """Complete session management fix"""
    print("🔧 COMPLETE SESSION MANAGEMENT FIX")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check audit_sessions table structure
        print("\n1. 📊 CHECKING AUDIT_SESSIONS TABLE STRUCTURE:")
        
        cursor.execute("PRAGMA table_info(audit_sessions)")
        columns = cursor.fetchall()
        
        print("   Current columns:")
        for col in columns:
            print(f"     {col[1]} {col[2]}")
        
        # 2. Create session management infrastructure
        print("\n2. 🏗️ CREATING SESSION MANAGEMENT INFRASTRUCTURE:")
        
        # Create current_session table (single row table)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS current_session (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                session_id TEXT NOT NULL,
                session_name TEXT,
                status TEXT DEFAULT 'ACTIVE',
                phase TEXT DEFAULT 'NONE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create session_phases table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS session_phases (
                session_id TEXT NOT NULL,
                phase_name TEXT NOT NULL,
                status TEXT DEFAULT 'NOT_STARTED',
                started_at DATETIME,
                completed_at DATETIME,
                data_count INTEGER DEFAULT 0,
                error_message TEXT,
                PRIMARY KEY (session_id, phase_name)
            )
        """)
        
        conn.commit()
        print("   ✅ Created session management tables")
        
        # 3. Find best session with proper column handling
        print("\n3. 🔍 FINDING BEST SESSION:")
        
        cursor.execute("""
            SELECT s.session_id, s.created_at,
                   (SELECT COUNT(*) FROM extracted_data WHERE session_id = s.session_id) as extracted_count,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) as comparison_count
            FROM audit_sessions s
            ORDER BY extracted_count DESC, comparison_count DESC, s.created_at DESC
            LIMIT 5
        """)
        
        sessions = cursor.fetchall()
        
        best_session = None
        if sessions:
            print("   Available sessions:")
            for row in sessions:
                session_id, created, extracted, comparison = row
                print(f"     {session_id}")
                print(f"       Created: {created}")
                print(f"       Data: {extracted} extracted, {comparison} comparison")
                
                if extracted > 0 and not best_session:
                    best_session = session_id
            
            if best_session:
                print(f"   ✅ Selected best session: {best_session}")
            else:
                print("   ❌ No sessions with extracted data found")
                return
        else:
            print("   ❌ No sessions found")
            return
        
        # 4. Set as current session
        print("\n4. 🎯 SETTING CURRENT SESSION:")
        
        cursor.execute("""
            INSERT OR REPLACE INTO current_session 
            (id, session_id, session_name, status, phase, updated_at)
            VALUES (1, ?, ?, 'ACTIVE', 'READY', datetime('now'))
        """, (best_session, f"Current_Session"))
        
        # Initialize phase tracking
        phases = [
            'EXTRACTION',
            'COMPARISON',
            'PRE_REPORTING',
            'TRACKER_FEEDING',
            'AUTO_LEARNING',
            'REPORT_GENERATION'
        ]
        
        for phase in phases:
            cursor.execute("""
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status)
                VALUES (?, ?, 'NOT_STARTED')
            """, (best_session, phase))
        
        conn.commit()
        print(f"   ✅ Set {best_session} as current session")
        
        # 5. Update phase statuses based on existing data
        print("\n5. 📊 UPDATING PHASE STATUSES:")
        
        # Check extraction
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (best_session,))
        extracted_count = cursor.fetchone()[0]
        
        if extracted_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'EXTRACTION'
            """, (extracted_count, best_session))
            print(f"   ✅ EXTRACTION: COMPLETED ({extracted_count} records)")
        
        # Check comparison
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (best_session,))
        comparison_count = cursor.fetchone()[0]
        
        if comparison_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'COMPARISON'
            """, (comparison_count, best_session))
            print(f"   ✅ COMPARISON: COMPLETED ({comparison_count} records)")
        else:
            print(f"   ⚠️ COMPARISON: NOT_STARTED (0 records)")
        
        # Check pre-reporting
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (best_session,))
        pre_reporting_count = cursor.fetchone()[0]
        
        if pre_reporting_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            """, (pre_reporting_count, best_session))
            print(f"   ✅ PRE_REPORTING: COMPLETED ({pre_reporting_count} records)")
        else:
            print(f"   ⚠️ PRE_REPORTING: NOT_STARTED (0 records)")
        
        conn.commit()
        
        # 6. Create session manager utility
        print("\n6. 🛠️ CREATING SESSION MANAGER UTILITY:")
        
        session_manager_code = '''import sqlite3
import os

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\\THE PAYROLL AUDITOR\\data\\templar_payroll_auditor.db",
        r"data\\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

class SessionManager:
    """Centralized session management for payroll audit system"""
    
    def __init__(self):
        self.db_path = get_database_path()
        if not self.db_path:
            raise Exception("Database not found")
    
    def get_current_session_id(self):
        """Get the current active session ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
            result = cursor.fetchone()
            
            if result:
                return result[0]
            else:
                raise Exception("No current session set")
        finally:
            conn.close()
    
    def set_current_session(self, session_id, session_name=None):
        """Set a session as the current active session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO current_session 
                (id, session_id, session_name, status, updated_at)
                VALUES (1, ?, ?, 'ACTIVE', datetime('now'))
            """, (session_id, session_name or f"Session_{session_id[-8:]}"))
            
            conn.commit()
        finally:
            conn.close()
    
    def get_session_status(self, session_id=None):
        """Get status of current or specified session"""
        if not session_id:
            session_id = self.get_current_session_id()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                SELECT phase_name, status, data_count, completed_at
                FROM session_phases 
                WHERE session_id = ?
                ORDER BY 
                    CASE phase_name
                        WHEN 'EXTRACTION' THEN 1
                        WHEN 'COMPARISON' THEN 2
                        WHEN 'PRE_REPORTING' THEN 3
                        WHEN 'TRACKER_FEEDING' THEN 4
                        WHEN 'AUTO_LEARNING' THEN 5
                        WHEN 'REPORT_GENERATION' THEN 6
                        ELSE 7
                    END
            """, (session_id,))
            
            phases = cursor.fetchall()
            
            return {
                'session_id': session_id,
                'phases': [
                    {
                        'name': row[0],
                        'status': row[1],
                        'data_count': row[2],
                        'completed_at': row[3]
                    }
                    for row in phases
                ]
            }
        finally:
            conn.close()
    
    def update_phase_status(self, phase_name, status, data_count=0, error_message=None):
        """Update the status of a phase for current session"""
        session_id = self.get_current_session_id()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            if status == 'COMPLETED':
                cursor.execute("""
                    UPDATE session_phases 
                    SET status = ?, data_count = ?, completed_at = datetime('now'), error_message = ?
                    WHERE session_id = ? AND phase_name = ?
                """, (status, data_count, error_message, session_id, phase_name))
            elif status == 'IN_PROGRESS':
                cursor.execute("""
                    UPDATE session_phases 
                    SET status = ?, started_at = datetime('now'), error_message = ?
                    WHERE session_id = ? AND phase_name = ?
                """, (status, error_message, session_id, phase_name))
            else:
                cursor.execute("""
                    UPDATE session_phases 
                    SET status = ?, error_message = ?
                    WHERE session_id = ? AND phase_name = ?
                """, (status, error_message, session_id, phase_name))
            
            conn.commit()
        finally:
            conn.close()

# Global session manager instance
_session_manager = None

def get_session_manager():
    """Get global session manager instance"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager

def get_current_session_id():
    """Get current session ID - use this in all phases"""
    return get_session_manager().get_current_session_id()
'''
        
        # Ensure core directory exists
        os.makedirs('core', exist_ok=True)
        
        # Save session manager
        with open('core/session_manager.py', 'w') as f:
            f.write(session_manager_code)
        
        print("   ✅ Created SessionManager in core/session_manager.py")
        
        # 7. Test the session management
        print("\n7. 🧪 TESTING SESSION MANAGEMENT:")
        
        # Test getting current session
        cursor.execute("SELECT session_id, session_name, status FROM current_session WHERE id = 1")
        current = cursor.fetchone()
        
        if current:
            print(f"   ✅ Current session: {current[0]}")
            print(f"   ✅ Session name: {current[1]}")
            print(f"   ✅ Status: {current[2]}")
            
            # Test the SessionManager class
            try:
                sys.path.append(os.path.dirname(__file__))
                from core.session_manager import get_current_session_id, get_session_manager
                
                test_session_id = get_current_session_id()
                print(f"   ✅ SessionManager.get_current_session_id(): {test_session_id}")
                
                session_manager = get_session_manager()
                status = session_manager.get_session_status()
                print(f"   ✅ Session status retrieved: {len(status['phases'])} phases")
                
            except Exception as e:
                print(f"   ❌ SessionManager test failed: {e}")
        
        print(f"\n🎉 ROBUST SESSION MANAGEMENT IMPLEMENTED!")
        print(f"✅ Current session: {best_session}")
        print(f"✅ Session management infrastructure created")
        print(f"✅ SessionManager class available")
        print(f"\n🎯 USAGE IN ALL PHASES:")
        print(f"📋 Import: from core.session_manager import get_current_session_id")
        print(f"📋 Use: session_id = get_current_session_id()")
        print(f"📋 This ensures ALL phases use the SAME current session!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_session_management_complete()
