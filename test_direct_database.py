#!/usr/bin/env python3
"""
Test direct database connection to bypass any caching issues
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_direct_database():
    """Test direct database connection and comparison storage"""
    print("🔍 TESTING DIRECT DATABASE CONNECTION")
    print("=" * 60)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return
    
    print(f"📁 Using database: {db_path}")
    
    try:
        # Create fresh connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check table schema
        print("\n1. 📋 CHECKING TABLE SCHEMA:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        
        if result:
            print("   Table definition:")
            print(f"   {result[0]}")
        else:
            print("   ❌ Table does not exist")
            return
        
        # 2. Test inserting all change types directly
        print("\n2. 🧪 TESTING DIRECT INSERT:")
        
        # Get latest session
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("   ❌ No audit sessions found")
            return
        
        session_id = session_result[0]
        print(f"   Using session: {session_id}")
        
        # Test all change types
        change_types = ['NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED']
        
        for i, change_type in enumerate(change_types):
            test_data = (
                session_id, f'TEST{i:03d}', f'Test Employee {i}', 'EARNINGS', 'TEST ITEM',
                '100.00', '200.00', change_type, 'HIGH', 100.0, 100.0
            )
            
            try:
                cursor.execute('''
                    INSERT INTO comparison_results 
                    (session_id, employee_id, employee_name, section_name, item_label,
                     previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', test_data)
                print(f"   ✅ {change_type}: SUCCESS")
            except Exception as e:
                print(f"   ❌ {change_type}: FAILED - {e}")
        
        conn.commit()
        
        # 3. Test the actual comparison data
        print("\n3. 🔄 TESTING ACTUAL COMPARISON DATA:")
        
        # Load some real extracted data
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
            FROM extracted_data
            WHERE session_id = ? AND period_type = 'current'
            LIMIT 5
        """, (session_id,))
        
        current_rows = cursor.fetchall()
        print(f"   Loaded {len(current_rows)} current records")
        
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
            FROM extracted_data
            WHERE session_id = ? AND period_type = 'previous'
            LIMIT 5
        """, (session_id,))
        
        previous_rows = cursor.fetchall()
        print(f"   Loaded {len(previous_rows)} previous records")
        
        # Create a simple comparison result
        if current_rows and previous_rows:
            current_row = current_rows[0]
            previous_row = previous_rows[0]
            
            # Create a test comparison result
            comparison_result = (
                session_id,
                current_row[0],  # employee_id
                current_row[1],  # employee_name
                current_row[2],  # section_name
                current_row[3],  # item_label
                previous_row[4], # previous_value
                current_row[4],  # current_value
                'CHANGED',       # change_type
                'HIGH',          # priority
                None,            # numeric_difference
                None             # percentage_change
            )
            
            try:
                cursor.execute('''
                    INSERT INTO comparison_results 
                    (session_id, employee_id, employee_name, section_name, item_label,
                     previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', comparison_result)
                
                conn.commit()
                print("   ✅ Real comparison result inserted successfully")
                
            except Exception as e:
                print(f"   ❌ Failed to insert real comparison result: {e}")
        
        # 4. Check final count
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
        count = cursor.fetchone()[0]
        print(f"\n4. 📊 Final comparison results count: {count}")
        
        # Clean up test data
        cursor.execute("DELETE FROM comparison_results WHERE employee_id LIKE 'TEST%'")
        conn.commit()
        
        conn.close()
        
        print("\n✅ DIRECT DATABASE TEST COMPLETED!")
        
    except Exception as e:
        print(f"❌ Error during direct database test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_database()
