#!/usr/bin/env python3
"""
Test AUTO LEARNING phase for Dictionary Manager
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_auto_learning_phase():
    """Test AUTO LEARNING phase"""
    print("🔍 TESTING AUTO LEARNING PHASE FOR DICTIONARY MANAGER")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current auto learning results
        print("\n1. 📊 CURRENT AUTO LEARNING RESULTS:")
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        auto_learning_count = cursor.fetchone()[0]
        print(f"   Auto learning results: {auto_learning_count}")
        
        if auto_learning_count > 0:
            # Show breakdown
            cursor.execute("""
                SELECT section_name, COUNT(*) as count
                FROM auto_learning_results 
                WHERE session_id = ?
                GROUP BY section_name
                ORDER BY count DESC
            """, (current_session,))
            
            section_breakdown = cursor.fetchall()
            print("   Breakdown by section:")
            for row in section_breakdown:
                print(f"     {row[0]}: {row[1]} items")
            
            # Show sample results
            cursor.execute("""
                SELECT section_name, item_label, confidence_score, learning_type
                FROM auto_learning_results 
                WHERE session_id = ?
                ORDER BY confidence_score DESC
                LIMIT 10
            """, (current_session,))
            
            samples = cursor.fetchall()
            print("   Sample auto learning results:")
            for row in samples:
                print(f"     {row[0]}.{row[1]} (confidence: {row[2]}, type: {row[3]})")
        
        # 2. Check pending items for Dictionary Manager
        print("\n2. 📊 PENDING ITEMS FOR DICTIONARY MANAGER:")
        cursor.execute("SELECT COUNT(*) FROM pending_items WHERE session_id = ?", (current_session,))
        pending_count = cursor.fetchone()[0]
        print(f"   Pending items: {pending_count}")
        
        if pending_count > 0:
            # Show sample pending items
            cursor.execute("""
                SELECT section_name, item_label, confidence_score, requires_review
                FROM pending_items 
                WHERE session_id = ?
                ORDER BY confidence_score DESC
                LIMIT 10
            """, (current_session,))
            
            pending_samples = cursor.fetchall()
            print("   Sample pending items:")
            for row in pending_samples:
                review_status = "Needs Review" if row[3] else "Auto-Approved"
                print(f"     {row[0]}.{row[1]} (confidence: {row[2]}, {review_status})")
        
        # 3. Check Dictionary Manager tables
        print("\n3. 📊 DICTIONARY MANAGER TABLES:")
        
        dict_tables = ['dictionary_items', 'section_mappings', 'item_classifications']
        for table in dict_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
                
                if count > 0:
                    # Show recent additions
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE source_session = ?", (current_session,))
                    session_count = cursor.fetchone()[0]
                    print(f"     From current session: {session_count}")
            except Exception as e:
                print(f"   {table}: Error - {e}")
        
        # 4. Test AUTO LEARNING phase execution
        print("\n4. 🔄 TESTING AUTO LEARNING PHASE EXECUTION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Clear existing auto learning results for clean test
            cursor.execute("DELETE FROM auto_learning_results WHERE session_id = ?", (current_session,))
            cursor.execute("DELETE FROM pending_items WHERE session_id = ?", (current_session,))
            conn.commit()
            print("   Cleared existing auto learning results")
            
            # Test loading current data for learning
            print("   Loading current data for learning...")
            current_data = manager._load_extracted_data('current')
            
            if current_data:
                print(f"   ✅ Loaded {len(current_data)} employees for learning")
                
                # Sample employee data
                sample_employee = current_data[0]
                print(f"   Sample employee: {sample_employee.get('employee_id')} - {sample_employee.get('employee_name')}")
                print(f"   Sections: {list(sample_employee.get('sections', {}).keys())}")
                
                # Test auto learning phase
                print("   Running AUTO LEARNING phase...")
                options = {
                    'report_name': 'Auto Learning Test',
                    'report_designation': 'Dictionary Manager Test'
                }
                
                result = manager._phase_auto_learning(options)
                
                if result:
                    print("   ✅ AUTO LEARNING phase completed successfully!")
                    
                    # Check results
                    cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
                    new_auto_learning_count = cursor.fetchone()[0]
                    print(f"   ✅ Auto learning results: {new_auto_learning_count}")
                    
                    cursor.execute("SELECT COUNT(*) FROM pending_items WHERE session_id = ?", (current_session,))
                    new_pending_count = cursor.fetchone()[0]
                    print(f"   ✅ Pending items: {new_pending_count}")
                    
                    if new_auto_learning_count > 0:
                        # Show sample results
                        cursor.execute("""
                            SELECT section_name, item_label, confidence_score, learning_type
                            FROM auto_learning_results 
                            WHERE session_id = ?
                            ORDER BY confidence_score DESC
                            LIMIT 5
                        """, (current_session,))
                        
                        new_samples = cursor.fetchall()
                        print("   Sample new auto learning results:")
                        for row in new_samples:
                            print(f"     {row[0]}.{row[1]} (confidence: {row[2]}, type: {row[3]})")
                    
                    if new_pending_count > 0:
                        # Show sample pending items
                        cursor.execute("""
                            SELECT section_name, item_label, confidence_score, requires_review
                            FROM pending_items 
                            WHERE session_id = ?
                            ORDER BY confidence_score DESC
                            LIMIT 5
                        """, (current_session,))
                        
                        new_pending_samples = cursor.fetchall()
                        print("   Sample new pending items:")
                        for row in new_pending_samples:
                            review_status = "Needs Review" if row[3] else "Auto-Approved"
                            print(f"     {row[0]}.{row[1]} (confidence: {row[2]}, {review_status})")
                else:
                    print("   ❌ AUTO LEARNING phase failed")
            else:
                print("   ❌ No current data found for learning")
        
        except Exception as e:
            print(f"   ❌ AUTO LEARNING phase test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. Test feeding to Dictionary Manager tables
        print("\n5. 🔄 FEEDING TO DICTIONARY MANAGER TABLES:")
        
        try:
            # Check if we have auto learning results to feed
            cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
            results_count = cursor.fetchone()[0]
            
            if results_count > 0:
                print(f"   Found {results_count} auto learning results to feed")
                
                # Feed to dictionary_items table
                cursor.execute("""
                    SELECT DISTINCT section_name, item_label, confidence_score
                    FROM auto_learning_results 
                    WHERE session_id = ? AND confidence_score >= 0.8
                """, (current_session,))
                
                high_confidence_items = cursor.fetchall()
                print(f"   Found {len(high_confidence_items)} high-confidence items (>=0.8)")
                
                fed_count = 0
                for row in high_confidence_items:
                    try:
                        # Check if item already exists
                        cursor.execute("""
                            SELECT COUNT(*) FROM dictionary_items 
                            WHERE section_name = ? AND item_label = ?
                        """, (row[0], row[1]))
                        
                        exists = cursor.fetchone()[0]
                        
                        if exists == 0:
                            cursor.execute("""
                                INSERT INTO dictionary_items 
                                (section_name, item_label, item_type, confidence_score, 
                                 auto_learned, source_session, created_at)
                                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
                            """, (
                                row[0],  # section_name
                                row[1],  # item_label
                                'AUTO_LEARNED',  # item_type
                                row[2],  # confidence_score
                                1,  # auto_learned
                                current_session  # source_session
                            ))
                            fed_count += 1
                            
                            if fed_count <= 3:  # Show first 3
                                print(f"     ✅ Fed: {row[0]}.{row[1]} (confidence: {row[2]})")
                    
                    except Exception as e:
                        print(f"     ❌ Failed to feed {row[0]}.{row[1]}: {e}")
                
                conn.commit()
                print(f"   ✅ Fed {fed_count} items to dictionary_items table")
                
                # Feed pending items for review
                cursor.execute("""
                    SELECT section_name, item_label, confidence_score
                    FROM pending_items 
                    WHERE session_id = ? AND requires_review = 1
                """, (current_session,))
                
                review_items = cursor.fetchall()
                print(f"   Found {len(review_items)} items requiring review")
                
            else:
                print("   ❌ No auto learning results to feed")
        
        except Exception as e:
            print(f"   ❌ Dictionary Manager feeding failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Final verification
        print("\n6. ✅ FINAL VERIFICATION:")
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        final_auto_learning = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pending_items WHERE session_id = ?", (current_session,))
        final_pending = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM dictionary_items WHERE source_session = ?", (current_session,))
        final_dictionary = cursor.fetchone()[0]
        
        print(f"   Auto learning results: {final_auto_learning}")
        print(f"   Pending items: {final_pending}")
        print(f"   Dictionary items added: {final_dictionary}")
        
        total_learned = final_auto_learning + final_pending + final_dictionary
        
        if total_learned > 0:
            print(f"\n🎉 AUTO LEARNING PHASE WORKING!")
            print(f"✅ Total items processed: {total_learned}")
            print("✅ Dictionary Manager should now show:")
            print(f"   - {final_dictionary} new auto-learned items")
            print(f"   - {final_pending} items pending review")
        else:
            print("\n❌ AUTO LEARNING phase may have issues")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during AUTO LEARNING test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_auto_learning_phase()
