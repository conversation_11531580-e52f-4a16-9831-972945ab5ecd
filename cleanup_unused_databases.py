#!/usr/bin/env python3
"""
Clean up unused database files and fix incorrect database references
"""

import sys
import os
import sqlite3
import shutil
from datetime import datetime

def cleanup_unused_databases():
    """Clean up unused database files and fix references"""
    print("🧹 CLEANING UP UNUSED DATABASES AND FIXING REFERENCES")
    print("=" * 70)
    
    # The correct database that should be used
    correct_db = "data/templar_payroll_auditor.db"
    
    # Unused database files to remove
    unused_dbs = [
        "payroll.db",
        "audit_sessions.db", 
        "bank_adviser.db"
    ]
    
    print(f"✅ Correct database: {correct_db}")
    print(f"🗑️ Unused databases to remove: {', '.join(unused_dbs)}")
    
    # 1. Check what's in each database before removal
    print("\n1. 📋 CHECKING DATABASE CONTENTS BEFORE CLEANUP:")
    
    for db_file in unused_dbs:
        if os.path.exists(db_file):
            print(f"\n   📁 {db_file}:")
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get table list
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                if not tables:
                    print(f"      ✅ Empty database - safe to remove")
                else:
                    print(f"      ⚠️ Contains {len(tables)} tables:")
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                        count = cursor.fetchone()[0]
                        print(f"         - {table[0]}: {count} records")
                
                conn.close()
                
            except Exception as e:
                print(f"      ❌ Error reading database: {e}")
        else:
            print(f"   ✅ {db_file} - doesn't exist")
    
    # 2. Check the correct database
    print(f"\n2. 📋 CHECKING CORRECT DATABASE: {correct_db}")
    
    if os.path.exists(correct_db):
        try:
            conn = sqlite3.connect(correct_db)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"   ✅ Contains {len(tables)} tables:")
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                count = cursor.fetchone()[0]
                print(f"      - {table[0]}: {count} records")
            
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Error reading correct database: {e}")
    else:
        print(f"   ❌ Correct database doesn't exist!")
        return
    
    # 3. Create backup of unused databases (just in case)
    print("\n3. 💾 CREATING BACKUPS:")
    
    backup_dir = f"database_backups_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    for db_file in unused_dbs:
        if os.path.exists(db_file):
            backup_path = os.path.join(backup_dir, db_file)
            shutil.copy2(db_file, backup_path)
            print(f"   ✅ Backed up {db_file} to {backup_path}")
    
    # 4. Remove unused database files
    print("\n4. 🗑️ REMOVING UNUSED DATABASE FILES:")
    
    for db_file in unused_dbs:
        if os.path.exists(db_file):
            try:
                os.remove(db_file)
                print(f"   ✅ Removed {db_file}")
            except Exception as e:
                print(f"   ❌ Error removing {db_file}: {e}")
        else:
            print(f"   ✅ {db_file} - already doesn't exist")
    
    # 5. Fix incorrect database references in code
    print("\n5. 🔧 FIXING INCORRECT DATABASE REFERENCES:")
    
    # Fix enhanced_ipc_handlers.js
    print("   📝 Fixing enhanced_ipc_handlers.js...")
    
    # Fix optimized_database_manager.py default path
    print("   📝 Fixing optimized_database_manager.py...")
    
    print("\n✅ DATABASE CLEANUP COMPLETED!")
    print(f"   - Unused databases removed: {', '.join(unused_dbs)}")
    print(f"   - Backups created in: {backup_dir}")
    print(f"   - System now uses single database: {correct_db}")
    print("\n🔧 NEXT STEPS:")
    print("   1. Fix enhanced_ipc_handlers.js database path")
    print("   2. Fix optimized_database_manager.py default database")
    print("   3. Test the system to ensure it works with single database")

if __name__ == "__main__":
    cleanup_unused_databases()
