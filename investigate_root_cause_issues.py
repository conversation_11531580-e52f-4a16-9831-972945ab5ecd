#!/usr/bin/env python3
"""
Investigate root cause issues with data extraction and table schemas
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def investigate_root_cause_issues():
    """Investigate root cause of data quality issues"""
    print("🔍 INVESTIGATING ROOT CAUSE OF DATA QUALITY ISSUES")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Investigating session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Investigate motor vehicle table schema
        print("\n1. 📋 MOTOR VEHICLE TABLE SCHEMA INVESTIGATION:")
        cursor.execute("PRAGMA table_info(motor_vehicle_maintenance)")
        motor_columns = cursor.fetchall()
        
        print("   Current motor_vehicle_maintenance schema:")
        for col in motor_columns:
            print(f"     {col[1]} {col[2]}")
        
        # Check if allowance_type column exists
        has_allowance_type = any(col[1] == 'allowance_type' for col in motor_columns)
        has_allowance_amount = any(col[1] == 'allowance_amount' for col in motor_columns)
        
        print(f"   Has allowance_type column: {has_allowance_type}")
        print(f"   Has allowance_amount column: {has_allowance_amount}")
        
        if not has_allowance_type or not has_allowance_amount:
            print("   ❌ ISSUE: Motor vehicle table missing required columns")
        
        # 2. Investigate employee data extraction
        print("\n2. 📊 EMPLOYEE DATA EXTRACTION INVESTIGATION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            current_data = manager._load_extracted_data('current')
            
            if current_data:
                print(f"   ✅ Loaded {len(current_data)} employees")
                
                # Check specific problematic employees
                problem_employees = ['PW0101', 'COP2140', 'COP1716']
                
                for emp_id in problem_employees:
                    print(f"\n   Investigating employee: {emp_id}")
                    
                    emp_data = None
                    for emp in current_data:
                        if emp.get('employee_id') == emp_id:
                            emp_data = emp
                            break
                    
                    if emp_data:
                        print(f"     Employee ID: {emp_data.get('employee_id')}")
                        print(f"     Employee Name: {emp_data.get('employee_name')}")
                        
                        # Check personal details section
                        personal_details = emp_data.get('sections', {}).get('PERSONAL DETAILS', {})
                        print(f"     Personal Details items: {len(personal_details)}")
                        
                        # Show relevant personal details
                        relevant_fields = []
                        for field, value in personal_details.items():
                            if any(keyword in field.upper() for keyword in ['DEPARTMENT', 'DEPT', 'DIVISION', 'MINISTRY', 'MOTOR']):
                                relevant_fields.append((field, value))
                        
                        if relevant_fields:
                            print("     Relevant Personal Details:")
                            for field, value in relevant_fields[:5]:
                                print(f"       {field}: {value}")
                        else:
                            print("     ❌ No department/motor vehicle fields found")
                        
                        # Check loans section
                        loans = emp_data.get('sections', {}).get('LOANS', {})
                        if loans:
                            print(f"     Loans items: {len(loans)}")
                            balance_bf_items = [item for item in loans.keys() if 'BALANCE B/F' in item]
                            if balance_bf_items:
                                print("     Balance B/F items:")
                                for item in balance_bf_items[:3]:
                                    print(f"       {item}: {loans[item]}")
                    else:
                        print(f"     ❌ Employee {emp_id} not found in extracted data")
            else:
                print("   ❌ Could not load extracted data")
        
        except Exception as e:
            print(f"   ❌ Data extraction investigation failed: {e}")
        
        # 3. Check raw extracted data for specific employees
        print("\n3. 📊 RAW EXTRACTED DATA INVESTIGATION:")
        
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
            AND employee_id IN ('PW0101', 'COP2140', 'COP1716')
            AND (section_name = 'PERSONAL DETAILS' OR section_name = 'LOANS')
            ORDER BY employee_id, section_name, item_label
        """, (current_session,))
        
        raw_data = cursor.fetchall()
        
        if raw_data:
            current_emp = None
            for row in raw_data:
                emp_id, emp_name, section, label, value = row
                
                if emp_id != current_emp:
                    print(f"\n   Employee: {emp_id} - {emp_name}")
                    current_emp = emp_id
                
                print(f"     {section}.{label}: {value}")
        else:
            print("   ❌ No raw extracted data found for test employees")
        
        # 4. Check current table data issues
        print("\n4. 📊 CURRENT TABLE DATA ISSUES:")
        
        # Check motor vehicle data
        cursor.execute("""
            SELECT employee_no, employee_name, department, maintenance_amount, remarks
            FROM motor_vehicle_maintenance 
            WHERE source_session = ?
            LIMIT 5
        """, (current_session,))
        
        motor_data = cursor.fetchall()
        
        if motor_data:
            print("   Current motor vehicle data:")
            for row in motor_data:
                print(f"     {row[0]} - {row[1]} ({row[2]}): amount={row[3]}, remarks={row[4]}")
        
        # Check loan data issues
        cursor.execute("""
            SELECT employee_no, employee_name, department, loan_type, loan_amount
            FROM in_house_loans 
            WHERE source_session = ?
        """, (current_session,))
        
        in_house_data = cursor.fetchall()
        
        if in_house_data:
            print("   Current in-house loan data:")
            for row in in_house_data:
                print(f"     {row[0]} - {row[1]} ({row[2]}): {row[3]} = {row[4]}")
                
                # Check for "Ghana Card ID" issue
                if "Ghana Card ID" in str(row[1]):
                    print(f"       ❌ ISSUE: 'Ghana Card ID' found as employee name")
        
        cursor.execute("""
            SELECT employee_no, employee_name, department, loan_type, loan_amount
            FROM external_loans 
            WHERE source_session = ?
            LIMIT 3
        """, (current_session,))
        
        external_data = cursor.fetchall()
        
        if external_data:
            print("   Current external loan data:")
            for row in external_data:
                print(f"     {row[0]} - {row[1]} ({row[2]}): {row[3]} = {row[4]}")
        
        # 5. Identify specific issues
        print("\n5. 🔍 IDENTIFIED ISSUES:")
        
        issues = []
        
        # Motor vehicle issues
        if not has_allowance_type:
            issues.append("Motor vehicle table missing 'allowance_type' column")
        if not has_allowance_amount:
            issues.append("Motor vehicle table missing 'allowance_amount' column")
        
        # Data extraction issues
        if any("Ghana Card ID" in str(row[1]) for row in in_house_data):
            issues.append("Employee name extraction returning 'Ghana Card ID' instead of actual name")
        
        # Department extraction issues
        dept_issues = []
        for row in motor_data + in_house_data + external_data:
            if row[2] in ['UNKNOWN', 'POLICE'] and not row[0].startswith('COP'):
                dept_issues.append(f"{row[0]} has incorrect department: {row[2]}")
        
        if dept_issues:
            issues.append(f"Department extraction issues: {len(dept_issues)} cases")
        
        print("   Issues identified:")
        for i, issue in enumerate(issues, 1):
            print(f"     {i}. {issue}")
        
        if not issues:
            print("   ✅ No obvious issues found - need deeper investigation")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during investigation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    investigate_root_cause_issues()
