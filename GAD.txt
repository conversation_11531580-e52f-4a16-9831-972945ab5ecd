﻿
renderer.js:2232 Updated indicator report-generation with status: Pending
renderer.js:2253 🎯 Phase indicators updated for: PRE_REPORTING (pre-reporting)
renderer.js:981 🔄 PRE_REPORTING phase detected - loading interactive UI
renderer.js:1000 ✅ UI updated for phase: PRE_REPORTING
renderer.js:1900 🔄 PRE_REPORTING phase detected in phase_progress - ensuring UI loads
renderer.js:885 📊 Progress updated: 80% - PRE_REPORTING completed
renderer.js:6551 📊 Received real-time extraction update: 
Object
renderer.js:2631 🔄 Real-time Extraction Progress: 
Object
renderer.js:885 📊 Progress updated: 80% - PRE_REPORTING completed
renderer.js:6563 🚀 Received enhanced progress update: 
Object
renderer.js:1753 🚀 Enhanced Progress Update: 
Object
renderer.js:8175 📝 Processing Log [info]: PRE_REPORTING completed
renderer.js:2176 Updating phase indicators for phase: PRE_REPORTING
renderer.js:2190 Current phase key: pre-reporting, from phase: PRE_REPORTING
renderer.js:2194 Current phase index: 4
renderer.js:2204 Extraction indicator found, ensuring it's visible
renderer.js:2212 Found 6 phase indicators to update
renderer.js:2232 Updated indicator extraction with status: Completed
renderer.js:2232 Updated indicator comparison with status: Completed
renderer.js:2232 Updated indicator auto-learning with status: Completed
renderer.js:2232 Updated indicator tracker-feeding with status: Completed
renderer.js:2232 Updated indicator pre-reporting with status: Processing
renderer.js:2232 Updated indicator report-generation with status: Pending
renderer.js:2253 🎯 Phase indicators updated for: PRE_REPORTING (pre-reporting)
renderer.js:2015 📊 Enhanced progress: PRE_REPORTING - 80%
renderer.js:885 📊 Progress updated: 80% - PRE_REPORTING completed
2
renderer.js:1147 ❌ No pre-reporting data found in database
renderer.js:91 🎉 Enhanced audit result: 
Object
renderer.js:94 ✅ Payroll audit completed successfully
13
renderer.js:1140 📊 Loading pre-reporting UI from database...
13
renderer.js:1147 ❌ No pre-reporting data found in database
index.html:2119 📂 Switched to tab: bank-adviser
index.html:2128 🔧 Initializing content for tab: bank-adviser
renderer.js:5558 🏦 Initializing Bank Adviser tab...
renderer.js:5566 ✅ Bank Adviser tab initialized successfully
index.html:2119 📂 Switched to tab: payroll-audit
index.html:2128 🔧 Initializing content for tab: payroll-audit
renderer.js:2806 🔍 Button state check:
renderer.js:2807   Current PDF: true
renderer.js:2808   Previous PDF: true
renderer.js:2809   Name: true
renderer.js:2810   Designation: true
renderer.js:2811   All met: true
renderer.js:2822 ✅ Button ENABLED
index.html:2195 🔍 Audit button state checked for payroll audit tab
﻿

