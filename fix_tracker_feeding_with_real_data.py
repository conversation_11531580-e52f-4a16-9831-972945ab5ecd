#!/usr/bin/env python3
"""
Fix tracker feeding to work with real extracted data
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_tracker_feeding_with_real_data():
    """Fix tracker feeding to work with real data"""
    print("🔧 FIXING TRACKER FEEDING WITH REAL DATA")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Working with session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Load real loan data from extracted_data
        print("\n1. 📊 LOADING REAL LOAN DATA:")
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, period_type
            FROM extracted_data 
            WHERE session_id = ? AND section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F'
            ORDER BY employee_id, item_label
            LIMIT 50
        """, (current_session,))
        
        loan_data = cursor.fetchall()
        print(f"   Found {len(loan_data)} loan Balance B/F items")
        
        # 2. Load motor vehicle data
        print("\n2. 📊 LOADING MOTOR VEHICLE DATA:")
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, period_type
            FROM extracted_data 
            WHERE session_id = ? AND (
                item_label LIKE '%MOTOR VEH%' OR
                item_label LIKE '%VEHICLE MAINT%'
            )
            ORDER BY employee_id, item_label
            LIMIT 20
        """, (current_session,))
        
        motor_data = cursor.fetchall()
        print(f"   Found {len(motor_data)} motor vehicle items")
        
        # 3. Test tracker feeding with real data
        print("\n3. 🔄 TESTING TRACKER FEEDING WITH REAL DATA:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Clear existing tracker results for clean test
            cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (current_session,))
            conn.commit()
            print("   Cleared existing tracker results")
            
            # Load in-house loan types
            in_house_loan_types = manager._load_in_house_loan_types()
            print(f"   In-house loan types: {in_house_loan_types}")
            
            tracked_items = {
                'in_house_loans': 0,
                'external_loans': 0,
                'motor_vehicles': 0
            }
            
            # Process loan data
            print("   Processing loan data...")
            for row in loan_data:
                item_data = {
                    'employee_id': row[0],
                    'employee_name': row[1],
                    'section_name': row[2],
                    'item_label': row[3],
                    'item_value': row[4]
                }
                
                # Parse loan type from item label
                if ' - ' in item_data['item_label']:
                    loan_type = item_data['item_label'].split(' - ')[0].strip()
                    item_data['loan_type'] = loan_type
                    
                    # Test if trackable
                    if manager._is_trackable_item(item_data):
                        tracker_type = manager._classify_tracker_type(item_data, in_house_loan_types)
                        
                        if tracker_type:
                            try:
                                manager._store_tracker_item(item_data, tracker_type)
                                tracked_items[tracker_type] += 1
                                
                                if tracked_items[tracker_type] <= 3:  # Show first 3 of each type
                                    print(f"     ✅ Tracked {tracker_type}: {item_data['employee_id']} - {loan_type} = {item_data['item_value']}")
                            except Exception as e:
                                print(f"     ❌ Failed to track {loan_type}: {e}")
            
            # Process motor vehicle data
            print("   Processing motor vehicle data...")
            for row in motor_data:
                item_data = {
                    'employee_id': row[0],
                    'employee_name': row[1],
                    'section_name': row[2],
                    'item_label': row[3],
                    'item_value': row[4]
                }
                
                # Test if trackable
                if manager._is_trackable_item(item_data):
                    tracker_type = manager._classify_tracker_type(item_data, in_house_loan_types)
                    
                    if tracker_type == 'motor_vehicles':
                        try:
                            manager._store_tracker_item(item_data, tracker_type)
                            tracked_items[tracker_type] += 1
                            
                            if tracked_items[tracker_type] <= 3:  # Show first 3
                                print(f"     ✅ Tracked motor vehicle: {item_data['employee_id']} - {item_data['item_label']} = {item_data['item_value']}")
                        except Exception as e:
                            print(f"     ❌ Failed to track motor vehicle: {e}")
            
            # Commit all tracker results
            if hasattr(manager.db_manager, 'connection') and manager.db_manager.connection:
                manager.db_manager.connection.commit()
            
            print(f"\n   ✅ TRACKER FEEDING RESULTS:")
            print(f"     In-house loans: {tracked_items['in_house_loans']}")
            print(f"     External loans: {tracked_items['external_loans']}")
            print(f"     Motor vehicles: {tracked_items['motor_vehicles']}")
            print(f"     Total tracked: {sum(tracked_items.values())}")
            
        except Exception as e:
            print(f"   ❌ Tracker feeding test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Verify tracker results in database
        print("\n4. ✅ VERIFYING TRACKER RESULTS:")
        
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
        total_tracker_count = cursor.fetchone()[0]
        print(f"   Total tracker results: {total_tracker_count}")
        
        if total_tracker_count > 0:
            # Check by tracker type
            cursor.execute("""
                SELECT tracker_type, COUNT(*) as count
                FROM tracker_results 
                WHERE session_id = ?
                GROUP BY tracker_type
                ORDER BY count DESC
            """, (current_session,))
            
            tracker_breakdown = cursor.fetchall()
            print("   Breakdown by type:")
            for row in tracker_breakdown:
                print(f"     {row[0]}: {row[1]} items")
            
            # Sample results
            cursor.execute("""
                SELECT employee_id, tracker_type, item_label, item_value
                FROM tracker_results 
                WHERE session_id = ?
                ORDER BY tracker_type, employee_id
                LIMIT 10
            """, (current_session,))
            
            sample_results = cursor.fetchall()
            print("   Sample tracker results:")
            for row in sample_results:
                print(f"     {row[0]} - {row[1]}: {row[2]} = {row[3]}")
        
        # 5. Now feed to Bank Adviser tables
        print("\n5. 🔄 FEEDING TO BANK ADVISER TABLES:")
        
        try:
            # Feed in-house loans
            cursor.execute("""
                SELECT employee_id, employee_name, item_label, item_value, numeric_value
                FROM tracker_results 
                WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
            """, (current_session,))
            
            in_house_loans = cursor.fetchall()
            
            for row in in_house_loans:
                try:
                    cursor.execute("""
                        INSERT INTO in_house_loans 
                        (employee_no, employee_name, department, loan_type, loan_amount,
                         period_month, period_year, period_acquired, source_session)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # employee_id
                        row[1],  # employee_name
                        'Unknown',  # department
                        row[2],  # item_label (loan type)
                        row[4] or 0,  # numeric_value
                        '06',  # current month
                        '2025',  # current year
                        '2025-06',  # period_acquired
                        current_session  # source_session
                    ))
                except Exception as e:
                    print(f"     ❌ Failed to insert in-house loan: {e}")
            
            # Feed external loans
            cursor.execute("""
                SELECT employee_id, employee_name, item_label, item_value, numeric_value
                FROM tracker_results 
                WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
            """, (current_session,))
            
            external_loans = cursor.fetchall()
            
            for row in external_loans:
                try:
                    cursor.execute("""
                        INSERT INTO external_loans 
                        (employee_no, employee_name, department, loan_type, loan_amount,
                         period_month, period_year, period_acquired, source_session)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # employee_id
                        row[1],  # employee_name
                        'Unknown',  # department
                        row[2],  # item_label (loan type)
                        row[4] or 0,  # numeric_value
                        '06',  # current month
                        '2025',  # current year
                        '2025-06',  # period_acquired
                        current_session  # source_session
                    ))
                except Exception as e:
                    print(f"     ❌ Failed to insert external loan: {e}")
            
            # Feed motor vehicle maintenance
            cursor.execute("""
                SELECT employee_id, employee_name, item_label, item_value, numeric_value
                FROM tracker_results 
                WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
            """, (current_session,))
            
            motor_vehicles = cursor.fetchall()
            
            for row in motor_vehicles:
                try:
                    cursor.execute("""
                        INSERT INTO motor_vehicle_maintenance 
                        (employee_no, employee_name, department, allowance_type, allowance_amount,
                         period_month, period_year, period_acquired, source_session)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # employee_id
                        row[1],  # employee_name
                        'Unknown',  # department
                        row[2],  # item_label (allowance type)
                        row[4] or 0,  # numeric_value
                        '06',  # current month
                        '2025',  # current year
                        '2025-06',  # period_acquired
                        current_session  # source_session
                    ))
                except Exception as e:
                    print(f"     ❌ Failed to insert motor vehicle: {e}")
            
            conn.commit()
            print("   ✅ Data fed to Bank Adviser tables")
            
        except Exception as e:
            print(f"   ❌ Failed to feed Bank Adviser tables: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Final verification
        print("\n6. ✅ FINAL VERIFICATION:")
        
        bank_adviser_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in bank_adviser_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE source_session = ?", (current_session,))
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
                
                if count > 0:
                    cursor.execute(f"SELECT employee_no, loan_type, loan_amount FROM {table} WHERE source_session = ? LIMIT 3", (current_session,))
                    samples = cursor.fetchall()
                    for sample in samples:
                        print(f"     Sample: {sample[0]} - {sample[1]} = {sample[2]}")
            except Exception as e:
                print(f"   {table}: Error - {e}")
        
        conn.close()
        
        print("\n🎉 TRACKER FEEDING FIXED AND WORKING!")
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_tracker_feeding_with_real_data()
