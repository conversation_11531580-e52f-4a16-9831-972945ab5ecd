#!/usr/bin/env python3
"""
Fix PRE_REPORTING phase to properly wait for user interaction
The issue is that sessions are being marked as completed when they should be waiting for user approval
"""

import sys
import os
import sqlite3

def fix_pre_reporting_waiting_status():
    """Fix PRE_REPORTING phase to wait for user interaction"""
    print("🔧 FIXING PRE_REPORTING WAITING STATUS")
    print("=" * 60)
    
    db_path = "data/templar_payroll_auditor.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Find sessions that should be waiting for user interaction
        print("\n1. 🔍 FINDING SESSIONS THAT SHOULD BE WAITING:")
        
        cursor.execute("""
            SELECT a.session_id, a.status, COUNT(p.id) as pre_reporting_count
            FROM audit_sessions a
            LEFT JOIN pre_reporting_results p ON a.session_id = p.session_id
            WHERE a.status = 'completed'
            GROUP BY a.session_id, a.status
            HAVING pre_reporting_count > 0
            ORDER BY a.created_at DESC
            LIMIT 3
        """)
        
        sessions_to_fix = cursor.fetchall()
        
        for session_id, status, count in sessions_to_fix:
            print(f"   Session: {session_id}")
            print(f"   Current status: {status}")
            print(f"   Pre-reporting records: {count}")
            print(f"   ❌ Should be: WAITING_FOR_USER")
            print()
        
        if not sessions_to_fix:
            print("   ✅ No sessions need fixing")
            return
        
        # 2. Fix the most recent session
        latest_session = sessions_to_fix[0]
        session_id = latest_session[0]
        pre_reporting_count = latest_session[2]
        
        print(f"\n2. 🔧 FIXING LATEST SESSION: {session_id}")
        
        # Update audit session status
        cursor.execute("""
            UPDATE audit_sessions 
            SET status = 'pre_reporting_ready' 
            WHERE session_id = ?
        """, (session_id,))
        
        print(f"   ✅ Updated audit_sessions status to 'pre_reporting_ready'")
        
        # Update session phase status
        cursor.execute("""
            UPDATE session_phases 
            SET status = 'WAITING_FOR_USER' 
            WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
        """, (session_id,))
        
        print(f"   ✅ Updated session_phases status to 'WAITING_FOR_USER'")
        
        # 3. Verify the fix
        print("\n3. ✅ VERIFYING FIX:")
        
        cursor.execute("""
            SELECT status FROM audit_sessions WHERE session_id = ?
        """, (session_id,))
        session_status = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT status FROM session_phases 
            WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
        """, (session_id,))
        phase_status = cursor.fetchone()
        
        print(f"   Session status: {session_status}")
        print(f"   Phase status: {phase_status[0] if phase_status else 'Not found'}")
        print(f"   Pre-reporting records: {pre_reporting_count}")
        
        if session_status == 'pre_reporting_ready' and phase_status and phase_status[0] == 'WAITING_FOR_USER':
            print(f"   🎉 SUCCESS! Session is now properly waiting for user interaction")
        else:
            print(f"   ❌ Fix failed - status not updated correctly")
        
        # 4. Test if UI can detect the waiting status
        print("\n4. 🧪 TESTING UI DETECTION:")
        
        try:
            sys.path.append('.')
            from core.session_manager import get_session_manager
            
            session_manager = get_session_manager()
            status = session_manager.get_session_status()
            
            print(f"   Session manager status: {status}")
            
            # Check if PRE_REPORTING phase is waiting
            pre_reporting_phase = next((p for p in status['phases'] if p['name'] == 'PRE_REPORTING'), None)
            
            if pre_reporting_phase:
                print(f"   PRE_REPORTING phase status: {pre_reporting_phase['status']}")
                
                if pre_reporting_phase['status'] == 'WAITING_FOR_USER':
                    print(f"   ✅ UI should now detect waiting status and show Pre-Reporting interface")
                else:
                    print(f"   ❌ Phase status is not WAITING_FOR_USER")
            else:
                print(f"   ❌ PRE_REPORTING phase not found")
                
        except Exception as e:
            print(f"   ❌ Error testing UI detection: {e}")
        
        # 5. Show what the UI should do next
        print("\n5. 📋 NEXT STEPS FOR UI:")
        print("   1. Refresh the page or restart the application")
        print("   2. The UI should detect WAITING_FOR_USER status")
        print("   3. The Pre-Reporting interface should appear")
        print("   4. User can review and approve changes")
        print("   5. Click 'Generate Report' to complete the phase")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_pre_reporting_waiting_status()
