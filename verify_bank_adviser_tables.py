#!/usr/bin/env python3
"""
Verify that Bank Adviser tables are populated with NEW data
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_bank_adviser_tables():
    """Verify Bank Adviser tables are populated"""
    print("🔍 VERIFYING BANK ADVISER TABLES POPULATION")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Checking session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check tracker results first
        print("\n1. 📊 TRACKER RESULTS SUMMARY:")
        cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE session_id = ?", (current_session,))
        total_tracker_count = cursor.fetchone()[0]
        print(f"   Total tracker results: {total_tracker_count}")
        
        if total_tracker_count > 0:
            # Breakdown by tracker type
            cursor.execute("""
                SELECT tracker_type, COUNT(*) as count
                FROM tracker_results 
                WHERE session_id = ?
                GROUP BY tracker_type
                ORDER BY count DESC
            """, (current_session,))
            
            tracker_breakdown = cursor.fetchall()
            print("   Breakdown by type:")
            for row in tracker_breakdown:
                print(f"     {row[0]}: {row[1]} items")
        
        # 2. Check Bank Adviser tables
        print("\n2. 📊 BANK ADVISER TABLES STATUS:")
        
        bank_adviser_tables = {
            'in_house_loans': ['employee_no', 'employee_name', 'loan_type', 'loan_amount', 'period_acquired'],
            'external_loans': ['employee_no', 'employee_name', 'loan_type', 'loan_amount', 'period_acquired'],
            'motor_vehicle_maintenance': ['employee_no', 'employee_name', 'allowance_type', 'allowance_amount', 'period_acquired']
        }
        
        for table_name, columns in bank_adviser_tables.items():
            print(f"\n   📋 {table_name.upper()}:")
            
            try:
                # Check total count
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                total_count = cursor.fetchone()[0]
                print(f"     Total records: {total_count}")
                
                # Check records from current session
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE source_session = ?", (current_session,))
                session_count = cursor.fetchone()[0]
                print(f"     From current session: {session_count}")
                
                if session_count > 0:
                    # Show sample records
                    column_list = ', '.join(columns)
                    cursor.execute(f"SELECT {column_list} FROM {table_name} WHERE source_session = ? LIMIT 5", (current_session,))
                    samples = cursor.fetchall()
                    
                    print(f"     Sample records:")
                    for i, sample in enumerate(samples):
                        print(f"       {i+1}. Employee: {sample[0]} - {sample[1]}")
                        print(f"          Type: {sample[2]}")
                        print(f"          Amount: {sample[3]}")
                        print(f"          Period: {sample[4]}")
                else:
                    print(f"     ❌ No records from current session")
                    
                    # Check if table has any structure issues
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    table_info = cursor.fetchall()
                    print(f"     Table structure: {len(table_info)} columns")
                    for col in table_info:
                        print(f"       {col[1]} {col[2]}")
            
            except Exception as e:
                print(f"     ❌ Error checking {table_name}: {e}")
        
        # 3. If tables are empty, try to populate them from tracker results
        print("\n3. 🔄 POPULATING BANK ADVISER TABLES FROM TRACKER RESULTS:")
        
        try:
            # Check if we need to populate the tables
            cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
            in_house_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
            external_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
            motor_count = cursor.fetchone()[0]
            
            if in_house_count == 0 or external_count == 0 or motor_count == 0:
                print("   Some tables are empty, populating from tracker results...")
                
                # Populate in-house loans
                if in_house_count == 0:
                    cursor.execute("""
                        SELECT employee_id, employee_name, item_label, item_value, numeric_value
                        FROM tracker_results 
                        WHERE session_id = ? AND tracker_type = 'IN_HOUSE_LOAN'
                    """, (current_session,))
                    
                    in_house_data = cursor.fetchall()
                    print(f"   Found {len(in_house_data)} in-house loans to populate")
                    
                    for row in in_house_data:
                        try:
                            cursor.execute("""
                                INSERT INTO in_house_loans 
                                (employee_no, employee_name, department, loan_type, loan_amount,
                                 period_month, period_year, period_acquired, source_session)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                row[0],  # employee_id
                                row[1],  # employee_name
                                'Unknown',  # department
                                row[2],  # item_label (loan type)
                                row[4] or 0,  # numeric_value
                                '06',  # current month
                                '2025',  # current year
                                '2025-06',  # period_acquired
                                current_session  # source_session
                            ))
                        except Exception as e:
                            print(f"     ❌ Failed to insert in-house loan: {e}")
                
                # Populate external loans
                if external_count == 0:
                    cursor.execute("""
                        SELECT employee_id, employee_name, item_label, item_value, numeric_value
                        FROM tracker_results 
                        WHERE session_id = ? AND tracker_type = 'EXTERNAL_LOAN'
                    """, (current_session,))
                    
                    external_data = cursor.fetchall()
                    print(f"   Found {len(external_data)} external loans to populate")
                    
                    for row in external_data:
                        try:
                            cursor.execute("""
                                INSERT INTO external_loans 
                                (employee_no, employee_name, department, loan_type, loan_amount,
                                 period_month, period_year, period_acquired, source_session)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                row[0],  # employee_id
                                row[1],  # employee_name
                                'Unknown',  # department
                                row[2],  # item_label (loan type)
                                row[4] or 0,  # numeric_value
                                '06',  # current month
                                '2025',  # current year
                                '2025-06',  # period_acquired
                                current_session  # source_session
                            ))
                        except Exception as e:
                            print(f"     ❌ Failed to insert external loan: {e}")
                
                # Populate motor vehicle maintenance
                if motor_count == 0:
                    cursor.execute("""
                        SELECT employee_id, employee_name, item_label, item_value, numeric_value
                        FROM tracker_results 
                        WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
                    """, (current_session,))
                    
                    motor_data = cursor.fetchall()
                    print(f"   Found {len(motor_data)} motor vehicle items to populate")
                    
                    for row in motor_data:
                        try:
                            cursor.execute("""
                                INSERT INTO motor_vehicle_maintenance 
                                (employee_no, employee_name, department, allowance_type, allowance_amount,
                                 period_month, period_year, period_acquired, source_session)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                row[0],  # employee_id
                                row[1],  # employee_name
                                'Unknown',  # department
                                row[2],  # item_label (allowance type)
                                row[4] or 0,  # numeric_value
                                '06',  # current month
                                '2025',  # current year
                                '2025-06',  # period_acquired
                                current_session  # source_session
                            ))
                        except Exception as e:
                            print(f"     ❌ Failed to insert motor vehicle: {e}")
                
                conn.commit()
                print("   ✅ Population completed")
            else:
                print("   ✅ Tables already populated")
        
        except Exception as e:
            print(f"   ❌ Population failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Final verification
        print("\n4. ✅ FINAL VERIFICATION:")
        
        for table_name in ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE source_session = ?", (current_session,))
                count = cursor.fetchone()[0]
                print(f"   {table_name}: {count} NEW records")
                
                if count > 0:
                    # Show sample with more details
                    if table_name == 'motor_vehicle_maintenance':
                        cursor.execute(f"""
                            SELECT employee_no, employee_name, allowance_type, allowance_amount 
                            FROM {table_name} 
                            WHERE source_session = ? 
                            LIMIT 3
                        """, (current_session,))
                    else:
                        cursor.execute(f"""
                            SELECT employee_no, employee_name, loan_type, loan_amount 
                            FROM {table_name} 
                            WHERE source_session = ? 
                            LIMIT 3
                        """, (current_session,))
                    
                    samples = cursor.fetchall()
                    print(f"     Sample records:")
                    for sample in samples:
                        print(f"       {sample[0]} - {sample[1]}: {sample[2]} = {sample[3]}")
            
            except Exception as e:
                print(f"   {table_name}: Error - {e}")
        
        # 5. Summary
        print("\n5. 📊 SUMMARY:")
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        final_in_house = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        final_external = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        final_motor = cursor.fetchone()[0]
        
        total_populated = final_in_house + final_external + final_motor
        
        print(f"   In-house loans: {final_in_house}")
        print(f"   External loans: {final_external}")
        print(f"   Motor vehicle maintenance: {final_motor}")
        print(f"   Total NEW items in Bank Adviser: {total_populated}")
        
        if total_populated > 0:
            print("\n🎉 BANK ADVISER TABLES SUCCESSFULLY POPULATED WITH NEW DATA!")
            print("✅ The Loan & Allowance Tracker should now show the NEW items")
        else:
            print("\n❌ Bank Adviser tables are still empty")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_bank_adviser_tables()
