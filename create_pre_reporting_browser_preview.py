#!/usr/bin/env python3
"""
Create PRE-REPORTING browser preview
"""

import sys
import os
import webbrowser

def create_pre_reporting_browser_preview():
    """Create and open PRE-REPORTING browser preview"""
    print("🌐 CREATING PRE-REPORTING BROWSER PREVIEW")
    print("=" * 60)
    
    try:
        # Get sample data
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            manager = PhasedProcessManager()
            result = manager.get_pre_reporting_data()
            
            if result.get('success') and result.get('data'):
                data = result['data'][:50]  # First 50 items for preview
                total_count = len(result.get('data', []))
                print(f"   ✅ Loaded {total_count} changes from session {current_session}")
            else:
                data = []
                total_count = 0
                current_session = "demo_session"
                print("   ⚠️ No real data - creating demo preview")
        except Exception as e:
            print(f"   ⚠️ Could not load real data: {e}")
            data = []
            total_count = 0
            current_session = "demo_session"
        
        # Create sample data if none available
        if not data:
            data = [
                {
                    'id': 1,
                    'employee_id': 'COP0209',
                    'employee_name': 'APPIAH-AIDOO A.',
                    'section_name': 'LOANS',
                    'item_label': 'SALARY ADVANCE-MINS - BALANCE B/F',
                    'previous_value': '11,111.12',
                    'current_value': '10,000.01',
                    'change_type': 'DECREASED',
                    'priority': 'MODERATE',
                    'bulk_category': 'Large_Bulk',
                    'selected_for_report': 0
                },
                {
                    'id': 2,
                    'employee_id': 'COP0361',
                    'employee_name': 'MENSAH K.',
                    'section_name': 'EARNINGS',
                    'item_label': 'GROSS SALARY',
                    'previous_value': '5,089.79',
                    'current_value': '4,025.18',
                    'change_type': 'DECREASED',
                    'priority': 'HIGH',
                    'bulk_category': 'Individual',
                    'selected_for_report': 1
                },
                {
                    'id': 3,
                    'employee_id': 'COP0540',
                    'employee_name': 'ASANTE J.',
                    'section_name': 'PERSONAL DETAILS',
                    'item_label': 'EMPLOYEE NAME',
                    'previous_value': '',
                    'current_value': 'ASANTE JAMES',
                    'change_type': 'NEW',
                    'priority': 'HIGH',
                    'bulk_category': 'Individual',
                    'selected_for_report': 1
                }
            ]
            total_count = len(data)
        
        # Create HTML content
        html_content = f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PRE-REPORTING UI Preview - {current_session}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }}
        
        .header h1 {{
            font-size: 3em;
            font-weight: 300;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }}
        
        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            padding: 40px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        
        .stat-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }}
        
        .stat-number {{
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }}
        
        .stat-label {{
            color: #7f8c8d;
            font-size: 1em;
            text-transform: uppercase;
            letter-spacing: 2px;
            font-weight: 600;
        }}
        
        .controls {{
            padding: 40px;
            background: white;
            border-bottom: 2px solid #ecf0f1;
        }}
        
        .filter-section {{
            margin-bottom: 30px;
        }}
        
        .filter-title {{
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }}
        
        .filter-buttons {{
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 25px;
        }}
        
        .filter-btn {{
            padding: 12px 24px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 0.95em;
        }}
        
        .filter-btn:hover {{
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }}
        
        .filter-btn.active {{
            background: #3498db;
            color: white;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }}
        
        .action-buttons {{
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }}
        
        .btn {{
            padding: 15px 35px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-weight: 700;
            font-size: 1.1em;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .btn-primary {{
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }}
        
        .btn-secondary {{
            background: linear-gradient(135deg, #95a5a6 0%, #bdc3c7 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(149, 165, 166, 0.3);
        }}
        
        .btn:hover {{
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.2);
        }}
        
        .changes-list {{
            padding: 40px;
            max-height: 700px;
            overflow-y: auto;
        }}
        
        .changes-list::-webkit-scrollbar {{
            width: 8px;
        }}
        
        .changes-list::-webkit-scrollbar-track {{
            background: #f1f1f1;
            border-radius: 10px;
        }}
        
        .changes-list::-webkit-scrollbar-thumb {{
            background: #3498db;
            border-radius: 10px;
        }}
        
        .change-item {{
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }}
        
        .change-item::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.5s ease;
        }}
        
        .change-item:hover::before {{
            left: 100%;
        }}
        
        .change-item:hover {{
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            transform: translateY(-3px);
            border-color: #3498db;
        }}
        
        .change-item.selected {{
            border-color: #27ae60;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.2);
        }}
        
        .change-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }}
        
        .employee-info {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.3em;
        }}
        
        .badges {{
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }}
        
        .badge {{
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .change-type.new {{ background: #e8f5e8; color: #27ae60; }}
        .change-type.changed {{ background: #fff3cd; color: #856404; }}
        .change-type.increased {{ background: #d4edda; color: #155724; }}
        .change-type.decreased {{ background: #f8d7da; color: #721c24; }}
        .change-type.removed {{ background: #f5c6cb; color: #721c24; }}
        
        .priority.high {{ background: #ffebee; color: #c62828; }}
        .priority.moderate {{ background: #fff3e0; color: #ef6c00; }}
        .priority.low {{ background: #e8f5e8; color: #2e7d32; }}
        
        .change-details {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}
        
        .detail-group {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3498db;
        }}
        
        .detail-label {{
            font-size: 0.9em;
            color: #7f8c8d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }}
        
        .detail-value {{
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
            word-break: break-word;
        }}
        
        .value-change {{
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        
        .arrow {{
            color: #3498db;
            font-size: 1.2em;
            font-weight: bold;
        }}
        
        .no-data {{
            text-align: center;
            padding: 80px 20px;
            color: #7f8c8d;
        }}
        
        .no-data h3 {{
            margin-bottom: 15px;
            color: #95a5a6;
            font-size: 2em;
        }}
        
        .no-data p {{
            font-size: 1.2em;
            line-height: 1.6;
        }}
        
        @media (max-width: 768px) {{
            .stats {{
                grid-template-columns: 1fr;
                padding: 20px;
            }}
            
            .controls {{
                padding: 20px;
            }}
            
            .changes-list {{
                padding: 20px;
            }}
            
            .change-details {{
                grid-template-columns: 1fr;
            }}
            
            .filter-buttons {{
                justify-content: center;
            }}
            
            .action-buttons {{
                flex-direction: column;
                align-items: center;
            }}
            
            .change-header {{
                flex-direction: column;
                align-items: flex-start;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 PRE-REPORTING INTERFACE</h1>
            <p>Interactive Change Review & Selection • Session: {current_session}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_count}</div>
                <div class="stat-label">Total Changes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len([d for d in data if d.get('selected_for_report')])}</div>
                <div class="stat-label">Auto-Selected</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len([d for d in data if d.get('change_type') == 'NEW'])}</div>
                <div class="stat-label">New Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(d.get('bulk_category', 'Unknown') for d in data))}</div>
                <div class="stat-label">Categories</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="filter-section">
                <div class="filter-title">Filter by Category</div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Changes</button>
                    <button class="filter-btn" data-filter="individual">Individual</button>
                    <button class="filter-btn" data-filter="small_bulk">Small Bulk</button>
                    <button class="filter-btn" data-filter="medium_bulk">Medium Bulk</button>
                    <button class="filter-btn" data-filter="large_bulk">Large Bulk</button>
                </div>
            </div>
            
            <div class="filter-section">
                <div class="filter-title">Filter by Priority</div>
                <div class="filter-buttons">
                    <button class="filter-btn" data-filter="high">High Priority</button>
                    <button class="filter-btn" data-filter="moderate">Moderate Priority</button>
                    <button class="filter-btn" data-filter="low">Low Priority</button>
                    <button class="filter-btn" data-filter="auto_selected">Auto-Selected</button>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-secondary" onclick="selectAllHighPriority()">Select All High Priority</button>
                <button class="btn btn-secondary" onclick="clearAllSelections()">Clear All Selections</button>
                <button class="btn btn-primary" id="generateBtn">Generate Report ({len([d for d in data if d.get('selected_for_report')])} selected)</button>
            </div>
        </div>
        
        <div class="changes-list" id="changesList">
'''
        
        if data:
            for i, change in enumerate(data):
                selected_class = "selected" if change.get('selected_for_report') else ""
                change_type = change.get('change_type', 'UNKNOWN').lower()
                priority = change.get('priority', 'LOW').lower()
                
                html_content += f'''
            <div class="change-item {selected_class}" data-id="{change.get('id', i)}" data-category="{change.get('bulk_category', 'Unknown').lower()}" data-priority="{priority}" data-selected="{change.get('selected_for_report', 0)}">
                <div class="change-header">
                    <div class="employee-info">
                        {change.get('employee_id', 'N/A')} - {change.get('employee_name', 'Unknown')}
                    </div>
                    <div class="badges">
                        <span class="badge change-type {change_type}">{change.get('change_type', 'UNKNOWN')}</span>
                        <span class="badge priority {priority}">{change.get('priority', 'LOW')}</span>
                    </div>
                </div>
                
                <div class="change-details">
                    <div class="detail-group">
                        <div class="detail-label">Section & Item</div>
                        <div class="detail-value">{change.get('section_name', 'N/A')}<br>{change.get('item_label', 'N/A')}</div>
                    </div>
                    
                    <div class="detail-group">
                        <div class="detail-label">Value Change</div>
                        <div class="detail-value">
                            <div class="value-change">
                                <span>{change.get('previous_value', 'N/A')}</span>
                                <span class="arrow">→</span>
                                <span>{change.get('current_value', 'N/A')}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-group">
                        <div class="detail-label">Category</div>
                        <div class="detail-value">{change.get('bulk_category', 'Unknown')}</div>
                    </div>
                    
                    <div class="detail-group">
                        <div class="detail-label">Selection Status</div>
                        <div class="detail-value">{'✅ Auto-Selected' if change.get('selected_for_report') else '⭕ Manual Review'}</div>
                    </div>
                </div>
            </div>
                '''
        else:
            html_content += '''
            <div class="no-data">
                <h3>🔍 No Pre-reporting Data Available</h3>
                <p>The PRE-REPORTING phase needs to be executed to generate data for review.<br>
                This preview shows how the interface will look when data is available.</p>
            </div>
            '''
        
        html_content += '''
        </div>
    </div>
    
    <script>
        // Interactive functionality
        let selectedCount = 0;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateSelectedCount();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Change item selection
            document.querySelectorAll('.change-item').forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.toggle('selected');
                    const isSelected = this.classList.contains('selected');
                    this.setAttribute('data-selected', isSelected ? '1' : '0');
                    updateSelectedCount();
                });
            });
            
            // Filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active from siblings
                    this.parentElement.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    const filter = this.getAttribute('data-filter');
                    filterChanges(filter);
                });
            });
        }
        
        function filterChanges(filter) {
            const items = document.querySelectorAll('.change-item');
            
            items.forEach(item => {
                let show = true;
                
                if (filter === 'all') {
                    show = true;
                } else if (filter === 'individual') {
                    show = item.getAttribute('data-category').includes('individual');
                } else if (filter === 'small_bulk') {
                    show = item.getAttribute('data-category').includes('small');
                } else if (filter === 'medium_bulk') {
                    show = item.getAttribute('data-category').includes('medium');
                } else if (filter === 'large_bulk') {
                    show = item.getAttribute('data-category').includes('large');
                } else if (filter === 'high') {
                    show = item.getAttribute('data-priority') === 'high';
                } else if (filter === 'moderate') {
                    show = item.getAttribute('data-priority') === 'moderate';
                } else if (filter === 'low') {
                    show = item.getAttribute('data-priority') === 'low';
                } else if (filter === 'auto_selected') {
                    show = item.getAttribute('data-selected') === '1';
                }
                
                item.style.display = show ? 'block' : 'none';
            });
        }
        
        function updateSelectedCount() {
            selectedCount = document.querySelectorAll('.change-item.selected').length;
            const generateBtn = document.getElementById('generateBtn');
            if (generateBtn) {
                generateBtn.textContent = `Generate Report (${selectedCount} selected)`;
            }
        }
        
        function selectAllHighPriority() {
            document.querySelectorAll('.change-item[data-priority="high"]').forEach(item => {
                item.classList.add('selected');
                item.setAttribute('data-selected', '1');
            });
            updateSelectedCount();
        }
        
        function clearAllSelections() {
            document.querySelectorAll('.change-item').forEach(item => {
                item.classList.remove('selected');
                item.setAttribute('data-selected', '0');
            });
            updateSelectedCount();
        }
        
        // Generate report button
        document.getElementById('generateBtn').addEventListener('click', function() {
            if (selectedCount === 0) {
                alert('Please select at least one change for the report.');
                return;
            }
            
            alert(`This would generate a report with ${selectedCount} selected changes.\\n\\nIn the actual application, this would:\\n1. Update selections in database\\n2. Complete PRE_REPORTING phase\\n3. Proceed to report generation`);
        });
        
        console.log('📋 PRE-REPORTING UI Preview Loaded Successfully');
        console.log('🎯 This demonstrates the interactive interface for change review');
        console.log('💡 Users can filter, select, and approve changes before report generation');
    </script>
</body>
</html>
        '''
        
        # Save and open
        preview_path = "pre_reporting_preview.html"
        with open(preview_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"   ✅ Created HTML preview: {preview_path}")
        
        # Open in browser
        full_path = os.path.abspath(preview_path)
        webbrowser.open(f"file://{full_path}")
        
        print(f"   🌐 Opened PRE-REPORTING UI in browser")
        print(f"   📊 Preview shows {total_count} changes")
        print(f"   🎯 This is how the interactive interface should look")
        
    except Exception as e:
        print(f"❌ Error creating preview: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_pre_reporting_browser_preview()
