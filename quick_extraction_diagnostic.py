#!/usr/bin/env python3
"""
Quick diagnostic for extraction freeze
"""

import sqlite3
import os

def quick_extraction_diagnostic():
    """Quick check of extraction settings and potential issues"""
    print("🔍 QUICK EXTRACTION DIAGNOSTIC")
    print("=" * 40)
    
    try:
        # Check current session and PDF info
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"Current session: {current_session}")
        
        cursor.execute('SELECT current_pdf_path, previous_pdf_path FROM audit_sessions WHERE session_id = ?', (current_session,))
        session_data = cursor.fetchone()
        
        if session_data:
            current_pdf, previous_pdf = session_data
            print(f"Current PDF: {current_pdf}")
            print(f"Previous PDF: {previous_pdf}")
            
            # Check file sizes
            if os.path.exists(current_pdf):
                current_size = os.path.getsize(current_pdf) / (1024 * 1024)
                print(f"Current PDF size: {current_size:.1f} MB")
                
                # Estimate pages (rough estimate: 1 page ≈ 50-100KB for payslips)
                estimated_pages = int(current_size * 1024 / 75)  # 75KB per page average
                print(f"Estimated pages in current PDF: ~{estimated_pages}")
                
                # Calculate processing time estimate
                # Assuming 2-5 pages per second with current settings
                estimated_time_min = estimated_pages / (3 * 60)  # 3 pages/sec average
                print(f"Estimated processing time: ~{estimated_time_min:.1f} minutes")
                
                if estimated_time_min > 10:
                    print("⚠️ Large file detected - processing may take significant time")
                
            if os.path.exists(previous_pdf):
                previous_size = os.path.getsize(previous_pdf) / (1024 * 1024)
                print(f"Previous PDF size: {previous_size:.1f} MB")
        
        # Check current extraction settings
        print(f"\n📊 CURRENT EXTRACTION SETTINGS:")
        print(f"   Batch size: 50 pages (good for performance)")
        
        cpu_count = os.cpu_count() or 4
        print(f"   CPU cores: {cpu_count}")
        
        # Estimate workers based on current logic
        if estimated_pages > 1000:
            max_workers = min(12, cpu_count * 2)
        elif estimated_pages > 500:
            max_workers = min(8, cpu_count)
        else:
            max_workers = min(6, cpu_count)
        
        print(f"   Estimated workers: {max_workers}")
        print(f"   Parallel processing: {max_workers} threads")
        
        # Check for potential issues
        print(f"\n🚨 POTENTIAL FREEZE CAUSES:")
        
        issues_found = []
        
        if current_size > 200:
            issues_found.append("Very large PDF file (>200MB)")
        
        if estimated_pages > 2000:
            issues_found.append("Very high page count (>2000 pages)")
        
        if cpu_count < 4:
            issues_found.append("Low CPU core count")
        
        if max_workers > cpu_count * 2:
            issues_found.append("Too many workers for CPU cores")
        
        if not issues_found:
            print("   ✅ No obvious configuration issues detected")
            print("   The freeze is likely caused by:")
            print("     • PDF corruption or complex formatting")
            print("     • ThreadPoolExecutor deadlock")
            print("     • Memory exhaustion during processing")
            print("     • Antivirus interference")
        else:
            for issue in issues_found:
                print(f"   ❌ {issue}")
        
        # Recommendations
        print(f"\n💡 IMMEDIATE SOLUTIONS:")
        print("   1. Use smaller test PDFs first (e.g., JUNE001.pdf, MAY001.pdf)")
        print("   2. Check if antivirus is scanning files during processing")
        print("   3. Close other applications to free memory")
        print("   4. Try processing one PDF at a time instead of both")
        
        if current_size > 100:
            print("   5. Consider splitting large PDFs into smaller chunks")
        
        print(f"\n🔧 QUICK FIXES TO TRY:")
        print("   • Reduce max_workers to 2-4 for testing")
        print("   • Add more timeout handling")
        print("   • Implement extraction progress monitoring")
        print("   • Use smaller batch size (25) temporarily for debugging")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Diagnostic failed: {e}")
        return False

def suggest_extraction_optimization():
    """Suggest extraction optimizations"""
    print(f"\n⚡ EXTRACTION OPTIMIZATION SUGGESTIONS:")
    print("=" * 50)
    
    print("1. 🎯 IMMEDIATE PERFORMANCE FIXES:")
    print("   • Reduce ThreadPoolExecutor timeout from 300s to 120s")
    print("   • Add progress callbacks every 10 pages")
    print("   • Implement extraction cancellation mechanism")
    print("   • Add memory usage monitoring")
    
    print("\n2. 🔧 CODE OPTIMIZATIONS:")
    print("   • Cache PDF reader objects")
    print("   • Use memory-mapped file reading")
    print("   • Implement lazy loading for large PDFs")
    print("   • Add extraction checkpoints for resume capability")
    
    print("\n3. 📊 MONITORING IMPROVEMENTS:")
    print("   • Real-time progress updates every 5 seconds")
    print("   • Memory usage alerts")
    print("   • Processing speed metrics")
    print("   • Worker thread health monitoring")
    
    print("\n4. 🛡️ RELIABILITY ENHANCEMENTS:")
    print("   • Automatic retry for failed pages")
    print("   • Graceful degradation for corrupted pages")
    print("   • Emergency extraction termination")
    print("   • Resource cleanup on failure")

if __name__ == "__main__":
    success = quick_extraction_diagnostic()
    
    if success:
        suggest_extraction_optimization()
        
        print(f"\n🎯 RECOMMENDED NEXT STEPS:")
        print("1. Try with smaller test PDFs first")
        print("2. Monitor system resources during extraction")
        print("3. Check for antivirus interference")
        print("4. Consider implementing extraction timeout/cancellation")
    else:
        print("\n⚠️ Could not complete diagnostic")
