#!/usr/bin/env python3
"""
Fix motor vehicle table population with correct schema
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_motor_vehicle_table():
    """Fix motor vehicle table population"""
    print("🔧 FIXING MOTOR VEHICLE TABLE POPULATION")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Working with session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check motor vehicle tracker results
        print("\n1. 📊 MOTOR VEHICLE TRACKER RESULTS:")
        cursor.execute("""
            SELECT employee_id, employee_name, item_label, item_value, numeric_value
            FROM tracker_results 
            WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
            LIMIT 10
        """, (current_session,))
        
        motor_data = cursor.fetchall()
        print(f"   Found {len(motor_data)} motor vehicle items")
        
        if motor_data:
            print("   Sample motor vehicle data:")
            for i, row in enumerate(motor_data[:3]):
                print(f"     {i+1}. {row[0]} - {row[1]}: {row[2]} = {row[3]}")
        
        # 2. Check motor vehicle table schema
        print("\n2. 📋 MOTOR VEHICLE TABLE SCHEMA:")
        cursor.execute("PRAGMA table_info(motor_vehicle_maintenance)")
        table_info = cursor.fetchall()
        
        print("   Current schema:")
        for col in table_info:
            print(f"     {col[1]} {col[2]}")
        
        # 3. Populate motor vehicle table with correct schema
        print("\n3. 🔄 POPULATING MOTOR VEHICLE TABLE:")
        
        try:
            # Clear existing records from current session
            cursor.execute("DELETE FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
            
            # Get all motor vehicle tracker results
            cursor.execute("""
                SELECT employee_id, employee_name, item_label, item_value, numeric_value
                FROM tracker_results 
                WHERE session_id = ? AND tracker_type = 'MOTOR_VEHICLE'
            """, (current_session,))
            
            all_motor_data = cursor.fetchall()
            print(f"   Processing {len(all_motor_data)} motor vehicle items...")
            
            inserted_count = 0
            for row in all_motor_data:
                try:
                    # Use correct column names based on actual schema
                    cursor.execute("""
                        INSERT INTO motor_vehicle_maintenance 
                        (employee_no, employee_name, department, maintenance_amount,
                         period_month, period_year, period_acquired, source_session, remarks)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        row[0],  # employee_id
                        row[1],  # employee_name
                        'Unknown',  # department
                        row[4] or 0,  # numeric_value as maintenance_amount
                        '06',  # current month
                        '2025',  # current year
                        '2025-06',  # period_acquired
                        current_session,  # source_session
                        f"NEW: {row[2]}"  # remarks with item_label
                    ))
                    inserted_count += 1
                    
                    if inserted_count <= 3:  # Show first 3 insertions
                        print(f"     ✅ Inserted: {row[0]} - {row[2]} = {row[4] or 0}")
                
                except Exception as e:
                    print(f"     ❌ Failed to insert {row[0]}: {e}")
            
            conn.commit()
            print(f"   ✅ Successfully inserted {inserted_count} motor vehicle records")
        
        except Exception as e:
            print(f"   ❌ Population failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Verify motor vehicle table population
        print("\n4. ✅ VERIFYING MOTOR VEHICLE TABLE:")
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        motor_count = cursor.fetchone()[0]
        print(f"   Motor vehicle records: {motor_count}")
        
        if motor_count > 0:
            # Show sample records
            cursor.execute("""
                SELECT employee_no, employee_name, maintenance_amount, remarks
                FROM motor_vehicle_maintenance 
                WHERE source_session = ?
                LIMIT 5
            """, (current_session,))
            
            samples = cursor.fetchall()
            print("   Sample records:")
            for sample in samples:
                print(f"     {sample[0]} - {sample[1]}: {sample[2]} ({sample[3]})")
        
        # 5. Final summary of all Bank Adviser tables
        print("\n5. 📊 FINAL BANK ADVISER TABLES SUMMARY:")
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        in_house_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        external_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        motor_final_count = cursor.fetchone()[0]
        
        total_bank_adviser = in_house_count + external_count + motor_final_count
        
        print(f"   In-house loans: {in_house_count} NEW records")
        print(f"   External loans: {external_count} NEW records")
        print(f"   Motor vehicle maintenance: {motor_final_count} NEW records")
        print(f"   Total NEW items in Bank Adviser: {total_bank_adviser}")
        
        if total_bank_adviser > 0:
            print("\n🎉 ALL BANK ADVISER TABLES SUCCESSFULLY POPULATED!")
            print("✅ The Loan & Allowance Tracker should now show all NEW items:")
            print(f"   - {in_house_count} in-house loans")
            print(f"   - {external_count} external loans") 
            print(f"   - {motor_final_count} motor vehicle allowances")
            print("\n✅ NEW rule is working correctly - only items present in current")
            print("   month but absent in previous month are tracked!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_motor_vehicle_table()
