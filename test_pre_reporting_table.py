#!/usr/bin/env python3
"""
Test the pre_reporting_results table creation and basic operations
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_pre_reporting_table():
    """Test the pre_reporting_results table"""
    print("🔍 TESTING PRE_REPORTING_RESULTS TABLE")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check if table exists
        print("\n1. 📋 CHECKING TABLE EXISTENCE:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pre_reporting_results'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   ✅ pre_reporting_results table exists")
        else:
            print("   ❌ pre_reporting_results table does not exist")
            print("   Creating table...")
            
            # Create the table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pre_reporting_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    change_id INTEGER NOT NULL,
                    selected_for_report BOOLEAN DEFAULT 1,
                    bulk_category TEXT,
                    bulk_size INTEGER DEFAULT 1,
                    user_notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
            print("   ✅ Table created")
        
        # 2. Check table schema
        print("\n2. 📋 TABLE SCHEMA:")
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        columns = cursor.fetchall()
        for col in columns:
            print(f"   {col[1]} {col[2]} {col[3] if col[3] else ''}")
        
        # 3. Get latest session
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if not session_result:
            print("   ❌ No audit sessions found")
            return
        
        session_id = session_result[0]
        print(f"\n3. 🎯 Using session: {session_id}")
        
        # 4. Check comparison results
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
        comparison_count = cursor.fetchone()[0]
        print(f"   📊 Comparison results available: {comparison_count}")
        
        if comparison_count == 0:
            print("   ❌ No comparison results - cannot test pre-reporting")
            return
        
        # 5. Test inserting a pre-reporting result
        print("\n4. 🧪 TESTING INSERT:")
        
        # Get a sample comparison result
        cursor.execute("SELECT id FROM comparison_results WHERE session_id = ? LIMIT 1", (session_id,))
        sample_change = cursor.fetchone()
        
        if sample_change:
            change_id = sample_change[0]
            
            # Test insert
            try:
                cursor.execute('''
                    INSERT INTO pre_reporting_results 
                    (session_id, change_id, selected_for_report, bulk_category, bulk_size)
                    VALUES (?, ?, ?, ?, ?)
                ''', (session_id, change_id, True, 'INDIVIDUAL', 1))
                
                conn.commit()
                print("   ✅ Insert successful")
                
                # Verify insert
                cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
                count = cursor.fetchone()[0]
                print(f"   ✅ Records in table: {count}")
                
                # Clean up test record
                cursor.execute("DELETE FROM pre_reporting_results WHERE session_id = ? AND change_id = ?", 
                             (session_id, change_id))
                conn.commit()
                print("   ✅ Test record cleaned up")
                
            except Exception as e:
                print(f"   ❌ Insert failed: {e}")
        
        # 6. Test the Python database manager
        print("\n5. 🔄 TESTING PYTHON DATABASE MANAGER:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.python_database_manager import PythonDatabaseManager
            
            db_manager = PythonDatabaseManager()
            
            # Test query
            results = db_manager.execute_query("SELECT COUNT(*) as count FROM comparison_results WHERE session_id = ?", (session_id,))
            print(f"   ✅ Python DB Manager query successful: {results[0]['count']} comparison results")
            
            # Test insert
            if sample_change:
                db_manager.execute_update('''
                    INSERT INTO pre_reporting_results 
                    (session_id, change_id, selected_for_report, bulk_category, bulk_size)
                    VALUES (?, ?, ?, ?, ?)
                ''', (session_id, change_id, True, 'INDIVIDUAL', 1))
                
                print("   ✅ Python DB Manager insert successful")
                
                # Clean up
                db_manager.execute_update("DELETE FROM pre_reporting_results WHERE session_id = ? AND change_id = ?", 
                                        (session_id, change_id))
                print("   ✅ Test record cleaned up")
            
            db_manager.close()
            
        except Exception as e:
            print(f"   ❌ Python DB Manager test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
        print("\n✅ PRE_REPORTING_RESULTS TABLE TEST COMPLETED!")
        
    except Exception as e:
        print(f"❌ Error during table test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pre_reporting_table()
