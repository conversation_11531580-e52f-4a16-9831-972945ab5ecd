#!/usr/bin/env python3
"""
Test the schema fix to ensure data persistence
"""

import sqlite3
import sys
import os
sys.path.append('.')

def test_schema_fix():
    """Test if the schema fix resolves the data clearing issue"""
    print("🔧 TESTING SCHEMA FIX")
    print("=" * 40)
    
    try:
        # Step 1: Insert test data
        print("1. Inserting test data...")
        
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        
        # Clear and insert test data
        cursor.execute('DELETE FROM comparison_results WHERE session_id = ?', (current_session,))
        cursor.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority,
             numeric_difference, percentage_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (current_session, 'TEST_SCHEMA', 'Test Schema Fix', 'EARNINGS', 'BASIC SALARY',
              '1000.00', '1100.00', 'INCREASED', 'HIGH', 100.00, 10.0))
        
        conn.commit()
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        before_count = cursor.fetchone()[0]
        print(f"   Test data inserted: {before_count} records")
        
        conn.close()
        
        # Step 2: Test PhasedProcessManager initialization (the problematic part)
        print("\n2. Testing PhasedProcessManager initialization...")
        
        from core.phased_process_manager import PhasedProcessManager
        
        # This should NOT clear the data anymore
        manager = PhasedProcessManager(debug_mode=False)
        
        # Step 3: Check if data survived
        print("\n3. Checking data survival...")
        
        conn2 = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor2 = conn2.cursor()
        
        cursor2.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        after_count = cursor2.fetchone()[0]
        print(f"   Data after manager init: {after_count} records")
        
        conn2.close()
        
        if after_count == before_count and after_count > 0:
            print("   ✅ DATA SURVIVED! Schema fix working!")
            
            # Step 4: Test the pre-reporting API
            print("\n4. Testing pre-reporting API...")
            
            result = manager.get_pre_reporting_data(current_session)
            
            print(f"   API success: {result.get('success')}")
            print(f"   API data count: {len(result.get('data', []))}")
            
            if result.get('success') and len(result.get('data', [])) > 0:
                print("   ✅ PRE-REPORTING API WORKING!")
                
                # Sample the data
                data = result.get('data', [])
                sample = data[0]
                print(f"   Sample data: {sample.get('employee_id')} - {sample.get('section_name')}.{sample.get('item_label')}")
                print(f"   Change: {sample.get('previous_value')} → {sample.get('current_value')} ({sample.get('change_type')})")
                
                return True
            else:
                print("   ❌ PRE-REPORTING API STILL FAILING")
                print(f"   Error: {result.get('error', 'Unknown')}")
                return False
        else:
            print("   ❌ DATA STILL BEING CLEARED")
            print(f"   Before: {before_count}, After: {after_count}")
            
            # Debug what's clearing the data
            print("\n   🔍 DEBUGGING DATA CLEARING...")
            
            # Check if it's a different table being created
            conn3 = sqlite3.connect('./data/templar_payroll_auditor.db')
            cursor3 = conn3.cursor()
            
            cursor3.execute('SELECT sql FROM sqlite_master WHERE type="table" AND name="comparison_results"')
            current_schema = cursor3.fetchone()[0]
            print(f"   Current table schema: {current_schema}")
            
            conn3.close()
            
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_schema_fix()
    
    if success:
        print("\n🎉 SCHEMA FIX SUCCESSFUL!")
        print("   The Pre-Reporting UI should now work correctly.")
        print("   The root cause has been resolved.")
    else:
        print("\n⚠️ SCHEMA FIX INCOMPLETE")
        print("   Additional investigation needed.")
