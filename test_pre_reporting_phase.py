#!/usr/bin/env python3
"""
Test the PRE-REPORTING phase specifically
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_pre_reporting_phase():
    """Test the PRE-REPORTING phase"""
    print("🔍 TESTING PRE-REPORTING PHASE")
    print("=" * 60)
    
    try:
        # Import the manager
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        # Get the latest session from database
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return
        
        session_id = session_result[0]
        print(f"🎯 Testing session: {session_id}")
        
        # Check if comparison results exist
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
        comparison_count = cursor.fetchone()[0]
        print(f"📊 Comparison results available: {comparison_count}")
        
        if comparison_count == 0:
            print("❌ No comparison results found - run comparison phase first")
            return
        
        # Check if pre_reporting_results table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pre_reporting_results'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ pre_reporting_results table does not exist")
            return
        else:
            print("✅ pre_reporting_results table exists")
        
        # Check table schema
        cursor.execute("PRAGMA table_info(pre_reporting_results)")
        columns = cursor.fetchall()
        print("📋 Table schema:")
        for col in columns:
            print(f"   {col[1]} {col[2]}")
        
        conn.close()
        
        # Create manager and set session
        print("\n🔄 Testing PRE-REPORTING phase execution:")
        manager = PhasedProcessManager()
        manager.session_id = session_id
        
        # Test the pre-reporting phase
        options = {
            'report_name': 'Test Report',
            'report_designation': 'Test Designation'
        }
        
        result = manager._phase_pre_reporting(options)
        
        if result:
            print("✅ PRE-REPORTING phase completed successfully!")
            
            # Check if pre-reporting results were stored
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
            pre_reporting_count = cursor.fetchone()[0]
            print(f"📊 Pre-reporting results stored: {pre_reporting_count}")
            
            # Sample some results
            cursor.execute("""
                SELECT pr.id, pr.change_id, pr.selected_for_report, pr.bulk_category, pr.bulk_size,
                       cr.employee_id, cr.section_name, cr.item_label, cr.change_type
                FROM pre_reporting_results pr
                JOIN comparison_results cr ON pr.change_id = cr.id
                WHERE pr.session_id = ?
                LIMIT 10
            """, (session_id,))
            
            sample_results = cursor.fetchall()
            print("\n📋 Sample pre-reporting results:")
            for row in sample_results:
                print(f"   Change {row[1]}: {row[5]} - {row[6]}.{row[7]} ({row[8]})")
                print(f"     Category: {row[3]}, Size: {row[4]}, Selected: {bool(row[2])}")
            
            conn.close()
            
        else:
            print("❌ PRE-REPORTING phase failed")
        
    except Exception as e:
        print(f"❌ Error during PRE-REPORTING test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pre_reporting_phase()
