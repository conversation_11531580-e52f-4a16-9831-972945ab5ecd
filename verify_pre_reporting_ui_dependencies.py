#!/usr/bin/env python3
"""
Comprehensive verification of ALL dependencies for Pre-Reporting UI to appear
"""

import sys
import os
import sqlite3

def verify_all_dependencies():
    """Verify every single dependency for Pre-Reporting UI"""
    print("🔍 COMPREHENSIVE PRE-REPORTING UI DEPENDENCY CHECK")
    print("=" * 80)
    
    dependencies_status = {}
    
    try:
        # 1. Check Database Dependencies
        print("\n1. 🗄️ DATABASE DEPENDENCIES:")
        
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check current session
        cursor.execute("SELECT session_id, status FROM current_session WHERE id = 1")
        current_session_row = cursor.fetchone()
        
        if current_session_row:
            session_id, session_status = current_session_row
            print(f"   ✅ Current session: {session_id}")
            print(f"   ✅ Session status: {session_status}")
            dependencies_status['current_session'] = True
        else:
            print(f"   ❌ No current session found")
            dependencies_status['current_session'] = False
            session_id = None
        
        # Check PRE_REPORTING phase status
        if session_id:
            cursor.execute("""
                SELECT status, data_count FROM session_phases 
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            """, (session_id,))
            phase_row = cursor.fetchone()
            
            if phase_row:
                phase_status, data_count = phase_row
                print(f"   ✅ PRE_REPORTING phase: {phase_status} ({data_count} items)")
                dependencies_status['phase_status'] = phase_status == 'WAITING_FOR_USER'
            else:
                print(f"   ❌ PRE_REPORTING phase not found")
                dependencies_status['phase_status'] = False
        
        # Check pre-reporting data
        if session_id:
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
            pre_reporting_count = cursor.fetchone()[0]
            print(f"   ✅ Pre-reporting data: {pre_reporting_count} records")
            dependencies_status['pre_reporting_data'] = pre_reporting_count > 0
        
        conn.close()
        
        # 2. Check File Dependencies
        print("\n2. 📁 FILE DEPENDENCIES:")
        
        required_files = [
            'ui/interactive_pre_reporting.js',
            'ui/content_switching_manager.js', 
            'ui/phase_manager.js',
            'ui/payroll_audit_core.js',
            'renderer.js',
            'preload.js',
            'main.js',
            'index.html'
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"   ✅ {file_path}")
                dependencies_status[f'file_{file_path.replace("/", "_").replace(".", "_")}'] = True
            else:
                print(f"   ❌ {file_path} - MISSING")
                dependencies_status[f'file_{file_path.replace("/", "_").replace(".", "_")}'] = False
        
        # 3. Check IPC Handler Dependencies
        print("\n3. 📡 IPC HANDLER DEPENDENCIES:")
        
        # Check if get-latest-pre-reporting-data handler exists in main.js
        with open('main.js', 'r', encoding='utf-8') as f:
            main_content = f.read()
            
        ipc_handlers = [
            'get-latest-pre-reporting-data',
            'complete-pre-reporting-phase',
            'enhanced-payroll-audit',
            'process-waiting-for-user'
        ]
        
        for handler in ipc_handlers:
            if handler in main_content:
                print(f"   ✅ {handler} IPC handler")
                dependencies_status[f'ipc_{handler.replace("-", "_")}'] = True
            else:
                print(f"   ❌ {handler} IPC handler - MISSING")
                dependencies_status[f'ipc_{handler.replace("-", "_")}'] = False
        
        # 4. Check Preload API Dependencies
        print("\n4. 🔌 PRELOAD API DEPENDENCIES:")
        
        with open('preload.js', 'r', encoding='utf-8') as f:
            preload_content = f.read()
        
        api_methods = [
            'getLatestPreReportingData',
            'completePREReportingPhase',
            'enhancedPayrollAudit',
            'onProcessWaitingForUser'
        ]
        
        for method in api_methods:
            if method in preload_content:
                print(f"   ✅ window.api.{method}")
                dependencies_status[f'api_{method}'] = True
            else:
                print(f"   ❌ window.api.{method} - MISSING")
                dependencies_status[f'api_{method}'] = False
        
        # 5. Check UI Detection Dependencies
        print("\n5. 🖥️ UI DETECTION DEPENDENCIES:")
        
        with open('renderer.js', 'r', encoding='utf-8') as f:
            renderer_content = f.read()
        
        ui_functions = [
            'loadPreReportingUIFromDatabase',
            'onProcessWaitingForUser',
            'updateUIPhase',
            'loadInteractivePreReportingScript'
        ]
        
        for function in ui_functions:
            if function in renderer_content:
                print(f"   ✅ {function}")
                dependencies_status[f'ui_{function}'] = True
            else:
                print(f"   ❌ {function} - MISSING")
                dependencies_status[f'ui_{function}'] = False
        
        # 6. Check HTML Container Dependencies
        print("\n6. 🏗️ HTML CONTAINER DEPENDENCIES:")
        
        with open('index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        html_elements = [
            'pre-reporting-container',
            'pre-reporting-loading-spinner',
            'pre-reporting-interactive-ui',
            'payroll-processing-view'
        ]
        
        for element in html_elements:
            if f'id="{element}"' in html_content:
                print(f"   ✅ #{element}")
                dependencies_status[f'html_{element.replace("-", "_")}'] = True
            else:
                print(f"   ❌ #{element} - MISSING")
                dependencies_status[f'html_{element.replace("-", "_")}'] = False
        
        # 7. Check Unified Session Manager Integration
        print("\n7. 🔧 UNIFIED SESSION MANAGER INTEGRATION:")
        
        sys.path.append('.')
        try:
            from core.unified_session_manager import get_unified_session_manager
            unified_manager = get_unified_session_manager()
            
            current_session = unified_manager.get_current_session_id()
            print(f"   ✅ Unified session manager: {current_session}")
            dependencies_status['unified_session_manager'] = True
            
            pre_reporting_data = unified_manager.get_pre_reporting_data()
            print(f"   ✅ Pre-reporting data access: {pre_reporting_data['success']}")
            dependencies_status['unified_data_access'] = pre_reporting_data['success']
            
        except Exception as e:
            print(f"   ❌ Unified session manager error: {e}")
            dependencies_status['unified_session_manager'] = False
            dependencies_status['unified_data_access'] = False
        
        # 8. Final Assessment
        print("\n8. 📊 FINAL DEPENDENCY ASSESSMENT:")
        
        total_deps = len(dependencies_status)
        passed_deps = sum(1 for status in dependencies_status.values() if status)
        
        print(f"   Total dependencies: {total_deps}")
        print(f"   Passed: {passed_deps}")
        print(f"   Failed: {total_deps - passed_deps}")
        print(f"   Success rate: {(passed_deps/total_deps)*100:.1f}%")
        
        # Critical dependencies for Pre-Reporting UI
        critical_deps = [
            'current_session',
            'phase_status', 
            'pre_reporting_data',
            'file_ui_interactive_pre_reporting_js',
            'ipc_get_latest_pre_reporting_data',
            'api_getLatestPreReportingData',
            'ui_loadPreReportingUIFromDatabase',
            'html_pre_reporting_container',
            'unified_session_manager'
        ]
        
        critical_passed = sum(1 for dep in critical_deps if dependencies_status.get(dep, False))
        
        print(f"\n   CRITICAL DEPENDENCIES: {critical_passed}/{len(critical_deps)} passed")
        
        if critical_passed == len(critical_deps):
            print(f"   🎉 ALL CRITICAL DEPENDENCIES ARE IN PLACE!")
            print(f"   ✅ Pre-Reporting UI SHOULD appear")
            print(f"   ✅ System is ready for user interaction")
        else:
            print(f"   ❌ MISSING CRITICAL DEPENDENCIES:")
            for dep in critical_deps:
                if not dependencies_status.get(dep, False):
                    print(f"      - {dep}")
        
        # 9. Next Steps
        print(f"\n9. 📋 WHAT SHOULD HAPPEN NEXT:")
        
        if critical_passed == len(critical_deps):
            print(f"   1. ✅ UI should detect WAITING_FOR_USER status")
            print(f"   2. ✅ loadPreReportingUIFromDatabase() should be called")
            print(f"   3. ✅ interactive_pre_reporting.js should load")
            print(f"   4. ✅ Pre-Reporting interface should appear")
            print(f"   5. ✅ User can review {pre_reporting_count if 'pre_reporting_count' in locals() else 'N/A'} changes")
        else:
            print(f"   ❌ Fix missing dependencies first")
            print(f"   ❌ Pre-Reporting UI will not appear until all critical deps are met")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_all_dependencies()
