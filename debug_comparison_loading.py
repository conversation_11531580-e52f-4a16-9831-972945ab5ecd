#!/usr/bin/env python3
"""
Debug the comparison results loading issue
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_comparison_loading():
    """Debug the comparison results loading"""
    print("🔍 DEBUGGING COMPARISON RESULTS LOADING")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check comparison results directly
        print("\n1. 📊 DIRECT DATABASE CHECK:")
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        total_count = cursor.fetchone()[0]
        print(f"   Total comparison results: {total_count}")
        
        # Check by session
        cursor.execute("""
            SELECT session_id, COUNT(*) as count
            FROM comparison_results 
            GROUP BY session_id
        """)
        by_session = cursor.fetchall()
        
        for row in by_session:
            print(f"   Session {row[0]}: {row[1]} results")
        
        # 2. Test the Python database manager query
        print("\n2. 🔄 TESTING PYTHON DATABASE MANAGER:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.python_database_manager import PythonDatabaseManager
            
            db_manager = PythonDatabaseManager()
            
            # Test the exact query used in _load_all_comparison_results
            session_id = by_session[0][0] if by_session else None
            
            if session_id:
                print(f"   Testing with session: {session_id}")
                
                query = '''SELECT id, employee_id, employee_name, section_name, item_label,
                                  previous_value, current_value, change_type, priority,
                                  numeric_difference, percentage_change
                           FROM comparison_results
                           WHERE session_id = ?
                           ORDER BY priority DESC, employee_id, section_name, item_label'''
                
                rows = db_manager.execute_query(query, (session_id,))
                print(f"   ✅ Python DB Manager returned {len(rows)} rows")
                
                if rows:
                    print("   Sample row:")
                    sample = rows[0]
                    print(f"     ID: {sample.get('id')}")
                    print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                    print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                    print(f"     Type: {sample.get('change_type')}")
                    print(f"     Priority: {sample.get('priority')}")
                
                # Test the phased process manager method
                print("\n3. 🔄 TESTING PHASED PROCESS MANAGER:")
                
                from core.phased_process_manager import PhasedProcessManager
                
                manager = PhasedProcessManager()
                manager.session_id = session_id
                
                # Test the method directly
                all_changes = manager._load_all_comparison_results()
                print(f"   ✅ Phased Process Manager returned {len(all_changes)} changes")
                
                if all_changes:
                    print("   Sample change:")
                    sample = all_changes[0]
                    print(f"     ID: {sample.get('id')}")
                    print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                    print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                    print(f"     Type: {sample.get('change_type')}")
                    print(f"     Priority: {sample.get('priority')}")
                
                # Test the full pre-reporting phase
                print("\n4. 🔄 TESTING FULL PRE-REPORTING PHASE:")
                
                options = {
                    'report_name': 'Debug Test Report',
                    'report_designation': 'Debug Test'
                }
                
                result = manager._phase_pre_reporting(options)
                
                if result:
                    print("   ✅ PRE-REPORTING phase completed successfully!")
                    
                    # Check stored results
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_id,))
                    stored_count = cursor.fetchone()[0]
                    print(f"   ✅ Pre-reporting results stored: {stored_count}")
                    
                else:
                    print("   ❌ PRE-REPORTING phase failed")
            
            db_manager.close()
            
        except Exception as e:
            print(f"   ❌ Python DB Manager test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_comparison_loading()
