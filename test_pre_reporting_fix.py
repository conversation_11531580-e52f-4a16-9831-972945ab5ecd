#!/usr/bin/env python3
"""
Test the pre-reporting data fix
"""

import sys
import os
sys.path.append('.')

from core.phased_process_manager import PhasedProcessManager

def test_pre_reporting_fix():
    """Test the pre-reporting data fix"""
    print("🔧 TESTING PRE-REPORTING DATA FIX")
    print("=" * 50)
    
    try:
        manager = PhasedProcessManager(debug_mode=True)
        
        # Test get_latest_pre_reporting_data
        print("1. Testing get_latest_pre_reporting_data...")
        result = manager.get_pre_reporting_data()
        
        print(f"   Result success: {result.get('success')}")
        print(f"   Data count: {len(result.get('data', []))}")
        print(f"   Total changes: {result.get('total_changes', 0)}")
        print(f"   Session ID: {result.get('session_id', 'unknown')}")
        
        if result.get('success') and result.get('data'):
            print("   ✅ PRE-REPORTING DATA FIX SUCCESSFUL!")
            
            # Sample first few items
            data = result.get('data', [])
            print(f"\n2. Sample data (first 3 items):")
            for i, item in enumerate(data[:3]):
                print(f"   {i+1}. {item.get('employee_id')}: {item.get('section_name')}.{item.get('item_label')}")
                print(f"      {item.get('previous_value')} → {item.get('current_value')} ({item.get('change_type')})")
                print(f"      Category: {item.get('bulk_category')}, Selected: {item.get('selected_for_report')}")
            
            print(f"\n3. ✅ READY FOR UI:")
            print(f"   The Pre-Reporting UI should now receive {len(data)} items")
            print(f"   All items have required fields for display")
            
            return True
        else:
            print("   ❌ PRE-REPORTING DATA FIX FAILED")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pre_reporting_fix()
    
    if success:
        print("\n🎉 PRE-REPORTING FIX COMPLETE!")
        print("   The UI should now load the pre-reporting data correctly.")
    else:
        print("\n⚠️ PRE-REPORTING FIX INCOMPLETE")
        print("   Additional debugging required.")
