#!/usr/bin/env python3
"""
Implement robust session management to ensure all phases use the current session
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def implement_robust_session_management():
    """Implement robust session management system"""
    print("🔧 IMPLEMENTING ROBUST SESSION MANAGEMENT")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Create session management tables
        print("\n1. 🏗️ CREATING SESSION MANAGEMENT INFRASTRUCTURE:")
        
        # Create current_session table (single row table)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS current_session (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                session_id TEXT NOT NULL,
                session_name TEXT,
                status TEXT DEFAULT 'ACTIVE',
                phase TEXT DEFAULT 'NONE',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create session_phases table to track phase completion
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS session_phases (
                session_id TEXT NOT NULL,
                phase_name TEXT NOT NULL,
                status TEXT DEFAULT 'NOT_STARTED',
                started_at DATETIME,
                completed_at DATETIME,
                data_count INTEGER DEFAULT 0,
                error_message TEXT,
                PRIMARY KEY (session_id, phase_name)
            )
        """)
        
        # Create session_metadata table for additional info
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS session_metadata (
                session_id TEXT PRIMARY KEY,
                report_name TEXT,
                report_designation TEXT,
                pdf_files TEXT,
                total_employees INTEGER DEFAULT 0,
                total_changes INTEGER DEFAULT 0,
                created_by TEXT DEFAULT 'SYSTEM',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        print("   ✅ Created session management tables")
        
        # 2. Find the best existing session with data
        print("\n2. 🔍 FINDING BEST EXISTING SESSION:")
        
        cursor.execute("""
            SELECT s.session_id, s.session_name, s.created_at,
                   (SELECT COUNT(*) FROM extracted_data WHERE session_id = s.session_id) as extracted_count,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = s.session_id) as comparison_count,
                   (SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = s.session_id) as pre_reporting_count
            FROM audit_sessions s
            ORDER BY extracted_count DESC, comparison_count DESC, s.created_at DESC
            LIMIT 5
        """)
        
        sessions = cursor.fetchall()
        
        best_session = None
        if sessions:
            print("   Available sessions:")
            for row in sessions:
                session_id, name, created, extracted, comparison, pre_reporting = row
                print(f"     {session_id[:30]}...")
                print(f"       Name: {name}")
                print(f"       Created: {created}")
                print(f"       Data: {extracted} extracted, {comparison} comparison, {pre_reporting} pre-reporting")
                
                if extracted > 0 and not best_session:
                    best_session = session_id
            
            if best_session:
                print(f"   ✅ Selected best session: {best_session}")
            else:
                print("   ❌ No sessions with extracted data found")
                return
        else:
            print("   ❌ No sessions found")
            return
        
        # 3. Set this as the current session
        print("\n3. 🎯 SETTING CURRENT SESSION:")
        
        cursor.execute("""
            INSERT OR REPLACE INTO current_session 
            (id, session_id, session_name, status, phase, updated_at)
            VALUES (1, ?, ?, 'ACTIVE', 'READY', datetime('now'))
        """, (best_session, f"Session_{best_session[-8:]}"))
        
        # Initialize session metadata
        cursor.execute("""
            INSERT OR REPLACE INTO session_metadata 
            (session_id, report_name, report_designation, created_at)
            VALUES (?, 'Current Payroll Audit', 'System Administrator', datetime('now'))
        """, (best_session,))
        
        # Initialize phase tracking
        phases = [
            'EXTRACTION',
            'PRE_AUDITING', 
            'COMPARISON',
            'PRE_REPORTING',
            'TRACKER_FEEDING',
            'AUTO_LEARNING',
            'REPORT_GENERATION'
        ]
        
        for phase in phases:
            cursor.execute("""
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status)
                VALUES (?, ?, 'NOT_STARTED')
            """, (best_session, phase))
        
        conn.commit()
        print(f"   ✅ Set {best_session} as current session")
        
        # 4. Update phase statuses based on existing data
        print("\n4. 📊 UPDATING PHASE STATUSES:")
        
        # Check extraction
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (best_session,))
        extracted_count = cursor.fetchone()[0]
        
        if extracted_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'EXTRACTION'
            """, (extracted_count, best_session))
            print(f"   ✅ EXTRACTION: COMPLETED ({extracted_count} records)")
        
        # Check comparison
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (best_session,))
        comparison_count = cursor.fetchone()[0]
        
        if comparison_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'COMPARISON'
            """, (comparison_count, best_session))
            print(f"   ✅ COMPARISON: COMPLETED ({comparison_count} records)")
        else:
            print(f"   ⚠️ COMPARISON: NOT_STARTED (0 records)")
        
        # Check pre-reporting
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (best_session,))
        pre_reporting_count = cursor.fetchone()[0]
        
        if pre_reporting_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'PRE_REPORTING'
            """, (pre_reporting_count, best_session))
            print(f"   ✅ PRE_REPORTING: COMPLETED ({pre_reporting_count} records)")
        else:
            print(f"   ⚠️ PRE_REPORTING: NOT_STARTED (0 records)")
        
        # Check tracker feeding
        cursor.execute("""
            SELECT 
                (SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?) +
                (SELECT COUNT(*) FROM external_loans WHERE source_session = ?) +
                (SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?)
        """, (best_session, best_session, best_session))
        
        tracker_count = cursor.fetchone()[0]
        
        if tracker_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'TRACKER_FEEDING'
            """, (tracker_count, best_session))
            print(f"   ✅ TRACKER_FEEDING: COMPLETED ({tracker_count} records)")
        else:
            print(f"   ⚠️ TRACKER_FEEDING: NOT_STARTED (0 records)")
        
        # Check auto learning
        cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ?", (best_session,))
        auto_learning_count = cursor.fetchone()[0]
        
        if auto_learning_count > 0:
            cursor.execute("""
                UPDATE session_phases 
                SET status = 'COMPLETED', completed_at = datetime('now'), data_count = ?
                WHERE session_id = ? AND phase_name = 'AUTO_LEARNING'
            """, (auto_learning_count, best_session))
            print(f"   ✅ AUTO_LEARNING: COMPLETED ({auto_learning_count} records)")
        else:
            print(f"   ⚠️ AUTO_LEARNING: NOT_STARTED (0 records)")
        
        conn.commit()
        
        # 5. Create session management functions
        print("\n5. 🛠️ CREATING SESSION MANAGEMENT FUNCTIONS:")
        
        session_manager_code = '''
class SessionManager:
    """Centralized session management for payroll audit system"""
    
    def __init__(self, db_path):
        self.db_path = db_path
    
    def get_current_session_id(self):
        """Get the current active session ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            return result[0]
        else:
            raise Exception("No current session set")
    
    def set_current_session(self, session_id, session_name=None):
        """Set a session as the current active session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO current_session 
            (id, session_id, session_name, status, updated_at)
            VALUES (1, ?, ?, 'ACTIVE', datetime('now'))
        """, (session_id, session_name or f"Session_{session_id[-8:]}"))
        
        conn.commit()
        conn.close()
    
    def get_session_status(self, session_id=None):
        """Get status of current or specified session"""
        if not session_id:
            session_id = self.get_current_session_id()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT phase_name, status, data_count, completed_at
            FROM session_phases 
            WHERE session_id = ?
            ORDER BY 
                CASE phase_name
                    WHEN 'EXTRACTION' THEN 1
                    WHEN 'COMPARISON' THEN 2
                    WHEN 'PRE_REPORTING' THEN 3
                    WHEN 'TRACKER_FEEDING' THEN 4
                    WHEN 'AUTO_LEARNING' THEN 5
                    WHEN 'REPORT_GENERATION' THEN 6
                    ELSE 7
                END
        """, (session_id,))
        
        phases = cursor.fetchall()
        conn.close()
        
        return {
            'session_id': session_id,
            'phases': [
                {
                    'name': row[0],
                    'status': row[1],
                    'data_count': row[2],
                    'completed_at': row[3]
                }
                for row in phases
            ]
        }
    
    def update_phase_status(self, phase_name, status, data_count=0, error_message=None):
        """Update the status of a phase for current session"""
        session_id = self.get_current_session_id()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if status == 'COMPLETED':
            cursor.execute("""
                UPDATE session_phases 
                SET status = ?, data_count = ?, completed_at = datetime('now'), error_message = ?
                WHERE session_id = ? AND phase_name = ?
            """, (status, data_count, error_message, session_id, phase_name))
        elif status == 'IN_PROGRESS':
            cursor.execute("""
                UPDATE session_phases 
                SET status = ?, started_at = datetime('now'), error_message = ?
                WHERE session_id = ? AND phase_name = ?
            """, (status, error_message, session_id, phase_name))
        else:
            cursor.execute("""
                UPDATE session_phases 
                SET status = ?, error_message = ?
                WHERE session_id = ? AND phase_name = ?
            """, (status, error_message, session_id, phase_name))
        
        conn.commit()
        conn.close()
'''
        
        # Save session manager to file
        with open('core/session_manager.py', 'w') as f:
            f.write('import sqlite3\n\n')
            f.write(session_manager_code)
        
        print("   ✅ Created SessionManager class in core/session_manager.py")
        
        # 6. Test the session management
        print("\n6. 🧪 TESTING SESSION MANAGEMENT:")
        
        # Test getting current session
        cursor.execute("SELECT session_id, session_name, status FROM current_session WHERE id = 1")
        current = cursor.fetchone()
        
        if current:
            print(f"   ✅ Current session: {current[0]}")
            print(f"   ✅ Session name: {current[1]}")
            print(f"   ✅ Status: {current[2]}")
        
        # Test phase status
        cursor.execute("""
            SELECT phase_name, status, data_count
            FROM session_phases 
            WHERE session_id = ?
            ORDER BY 
                CASE phase_name
                    WHEN 'EXTRACTION' THEN 1
                    WHEN 'COMPARISON' THEN 2
                    WHEN 'PRE_REPORTING' THEN 3
                    WHEN 'TRACKER_FEEDING' THEN 4
                    WHEN 'AUTO_LEARNING' THEN 5
                    WHEN 'REPORT_GENERATION' THEN 6
                    ELSE 7
                END
        """, (best_session,))
        
        phase_status = cursor.fetchall()
        
        print("   Phase status:")
        for row in phase_status:
            phase, status, count = row
            print(f"     {phase}: {status} ({count} records)")
        
        # 7. Create helper functions for integration
        print("\n7. 🔗 CREATING INTEGRATION HELPERS:")
        
        integration_code = '''
def get_current_session_data():
    """Get current session data for any phase"""
    from core.session_manager import SessionManager
    
    db_path = get_database_path()
    session_manager = SessionManager(db_path)
    
    try:
        session_id = session_manager.get_current_session_id()
        return {
            'success': True,
            'session_id': session_id,
            'status': session_manager.get_session_status()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def ensure_current_session_consistency():
    """Ensure all phases use the same current session"""
    result = get_current_session_data()
    
    if result['success']:
        return result['session_id']
    else:
        raise Exception(f"No current session available: {result['error']}")
'''
        
        with open('core/session_helpers.py', 'w') as f:
            f.write(integration_code)
        
        print("   ✅ Created session helper functions")
        
        print(f"\n🎉 ROBUST SESSION MANAGEMENT IMPLEMENTED!")
        print(f"✅ Current session: {best_session}")
        print(f"✅ Session management tables created")
        print(f"✅ Phase tracking implemented")
        print(f"✅ SessionManager class available")
        print(f"✅ Integration helpers created")
        print(f"\n🎯 All phases will now use the same current session!")
        print(f"📋 Use SessionManager.get_current_session_id() in all phases")
        print(f"📋 Use SessionManager.update_phase_status() to track progress")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during implementation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    implement_robust_session_management()
