#!/usr/bin/env python3
"""
Evaluate the impact of using "Clear Extracted Data" feature in settings
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def evaluate_clear_extracted_data_impact():
    """Evaluate the impact of Clear Extracted Data feature"""
    print("🔍 EVALUATING 'CLEAR EXTRACTED DATA' FEATURE IMPACT")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 CURRENT SESSION STATUS:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            current_session = None
        
        # 2. Check all tables in database
        print("\n2. 📊 ALL TABLES IN DATABASE:")
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        all_tables = cursor.fetchall()
        
        print("   All tables:")
        for table in all_tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"     {table_name}: {count} records")
        
        # 3. Identify what Clear Extracted Data will delete
        print("\n3. 🗑️ WHAT 'CLEAR EXTRACTED DATA' WILL DELETE:")
        
        # These are the tables the feature targets
        target_tables = [
            'extracted_data',
            'audit_sessions', 
            'comparison_results',
            'pre_reporting_data'
        ]
        
        tables_to_delete = []
        tables_not_found = []
        
        for table_name in target_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                tables_to_delete.append((table_name, count))
                print(f"   ❌ WILL DELETE: {table_name} ({count} records)")
            except sqlite3.OperationalError:
                tables_not_found.append(table_name)
                print(f"   ⚠️ NOT FOUND: {table_name} (table doesn't exist)")
        
        # 4. Identify what will be preserved
        print("\n4. ✅ WHAT WILL BE PRESERVED:")
        
        # These tables should NOT be affected
        preserved_tables = [
            'in_house_loans',
            'external_loans', 
            'motor_vehicle_maintenance',
            'leave_claims',
            'educational_subsidy',
            'long_service_awards',
            'auto_learning_dictionary',
            'current_session',
            'session_phases',
            'session_metadata'
        ]
        
        preserved_data = []
        preserved_not_found = []
        
        for table_name in preserved_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                preserved_data.append((table_name, count))
                print(f"   ✅ PRESERVED: {table_name} ({count} records)")
            except sqlite3.OperationalError:
                preserved_not_found.append(table_name)
                print(f"   ⚠️ NOT FOUND: {table_name} (table doesn't exist)")
        
        # 5. Check current session data specifically
        if current_session:
            print(f"\n5. 📊 CURRENT SESSION DATA IMPACT:")
            
            session_data_impact = []
            
            for table_name, count in tables_to_delete:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE session_id = ?", (current_session,))
                    session_count = cursor.fetchone()[0]
                    session_data_impact.append((table_name, session_count, count))
                    print(f"   ❌ {table_name}: {session_count} current session records (of {count} total)")
                except Exception as e:
                    print(f"   ⚠️ {table_name}: Could not check session data - {e}")
            
            # Check preserved tracker data for current session
            print(f"\n   📊 PRESERVED TRACKER DATA FOR CURRENT SESSION:")
            
            tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
            
            for table_name in tracker_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE source_session = ?", (current_session,))
                    tracker_count = cursor.fetchone()[0]
                    print(f"   ✅ {table_name}: {tracker_count} records (WILL BE PRESERVED)")
                except Exception as e:
                    print(f"   ⚠️ {table_name}: Could not check - {e}")
        
        # 6. Impact assessment
        print(f"\n6. 🎯 IMPACT ASSESSMENT:")
        
        total_records_to_delete = sum(count for _, count in tables_to_delete)
        total_preserved_records = sum(count for _, count in preserved_data)
        
        print(f"   📊 Summary:")
        print(f"     Records to DELETE: {total_records_to_delete}")
        print(f"     Records to PRESERVE: {total_preserved_records}")
        print(f"     Tables to DELETE: {len(tables_to_delete)}")
        print(f"     Tables to PRESERVE: {len(preserved_data)}")
        
        # 7. Critical data analysis
        print(f"\n7. ⚠️ CRITICAL DATA ANALYSIS:")
        
        critical_losses = []
        
        # Check if we'll lose our PRE-REPORTING data
        for table_name, count in tables_to_delete:
            if table_name == 'comparison_results' and count > 0:
                critical_losses.append(f"COMPARISON RESULTS: {count} changes (needed for PRE-REPORTING)")
            elif table_name == 'pre_reporting_data' and count > 0:
                critical_losses.append(f"PRE-REPORTING DATA: {count} categorized changes")
            elif table_name == 'extracted_data' and count > 0:
                critical_losses.append(f"EXTRACTED DATA: {count} payslip records")
            elif table_name == 'audit_sessions' and count > 0:
                critical_losses.append(f"AUDIT SESSIONS: {count} session records")
        
        if critical_losses:
            print("   ❌ CRITICAL LOSSES:")
            for loss in critical_losses:
                print(f"     • {loss}")
        else:
            print("   ✅ No critical data to lose")
        
        # 8. Recovery requirements
        print(f"\n8. 🔄 RECOVERY REQUIREMENTS AFTER CLEARING:")
        
        if total_records_to_delete > 0:
            print("   To restore functionality after clearing, you would need to:")
            print("   1. ❌ Re-run EXTRACTION phase (process PDF files again)")
            print("   2. ❌ Re-run COMPARISON phase (compare current vs previous)")
            print("   3. ❌ Re-run PRE-REPORTING phase (categorize changes)")
            print("   4. ❌ Re-run TRACKER FEEDING phase (populate Bank Adviser)")
            print("   5. ❌ Lose all current PRE-REPORTING selections")
            print("   6. ❌ Lose all audit history and session data")
            print("   7. ✅ Bank Adviser tracker data WILL be preserved")
            print("   8. ✅ Auto-learning dictionary WILL be preserved")
        else:
            print("   ✅ No recovery needed - no data to clear")
        
        # 9. Recommendation
        print(f"\n9. 💡 RECOMMENDATION:")
        
        if current_session and total_records_to_delete > 0:
            print("   ⚠️ WARNING: DO NOT USE 'Clear Extracted Data' right now!")
            print("   Reasons:")
            print("   • You have a working PRE-REPORTING UI with 6,657 changes")
            print("   • You have comparison results and pre-reporting data")
            print("   • You would lose all current audit progress")
            print("   • You would need to re-process everything from scratch")
            print("   • Bank Adviser tracker data would be preserved, but audit data lost")
            
            print(f"\n   ✅ SAFE TO USE 'Clear Extracted Data' when:")
            print("   • You want to start a completely fresh audit")
            print("   • You have completed and exported all needed reports")
            print("   • You want to clear old audit sessions")
            print("   • You're troubleshooting and need a clean slate")
        else:
            print("   ✅ Safe to use - no critical data to lose")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    evaluate_clear_extracted_data_impact()
