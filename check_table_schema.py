#!/usr/bin/env python3
"""
Check table schema to find the data clearing issue
"""

import sqlite3

def check_table_schema():
    """Check table schema for issues"""
    print("🔍 CHECKING TABLE SCHEMA")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check comparison_results table schema
        cursor.execute('PRAGMA table_info(comparison_results)')
        schema = cursor.fetchall()
        
        print('comparison_results table schema:')
        for col in schema:
            print(f'   {col[1]} {col[2]} (nullable: {not col[3]}, default: {col[4]}, pk: {col[5]})')
        
        # Check table creation SQL
        cursor.execute('SELECT sql FROM sqlite_master WHERE type="table" AND name="comparison_results"')
        create_sql = cursor.fetchone()
        
        if create_sql:
            print(f'\nTable creation SQL:')
            print(create_sql[0])
        
        # Check if there are any triggers that might clear data
        cursor.execute('SELECT name, sql FROM sqlite_master WHERE type="trigger" AND tbl_name="comparison_results"')
        triggers = cursor.fetchall()
        
        if triggers:
            print(f'\nTriggers on comparison_results:')
            for trigger_name, trigger_sql in triggers:
                print(f'   {trigger_name}: {trigger_sql}')
        else:
            print('\nNo triggers on comparison_results table')
        
        # Check for any views that might interfere
        cursor.execute('SELECT name, sql FROM sqlite_master WHERE type="view" AND sql LIKE "%comparison_results%"')
        views = cursor.fetchall()
        
        if views:
            print(f'\nViews referencing comparison_results:')
            for view_name, view_sql in views:
                print(f'   {view_name}: {view_sql}')
        
        conn.close()
        
        # Now test if the schema creation is the issue
        print(f'\n🧪 TESTING SCHEMA CREATION IMPACT')
        
        # Insert test data
        conn2 = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor2 = conn2.cursor()
        
        cursor2.execute('DELETE FROM comparison_results')
        cursor2.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label,
             previous_value, current_value, change_type, priority,
             numeric_difference, percentage_change)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('test_session', 'TEST001', 'Test Employee', 'EARNINGS', 'BASIC SALARY',
              '1000.00', '1100.00', 'INCREASED', 'HIGH', 100.00, 10.0))
        conn2.commit()
        
        cursor2.execute('SELECT COUNT(*) FROM comparison_results')
        before_count = cursor2.fetchone()[0]
        print(f'Records before schema creation: {before_count}')
        
        conn2.close()
        
        # Now run just the schema creation part
        import sys
        import os
        sys.path.append('.')
        
        from core.python_database_manager import PythonDatabaseManager
        
        db_manager = PythonDatabaseManager()
        
        # Run the exact schema creation from PhasedProcessManager
        db_manager.execute_update('''
            CREATE TABLE IF NOT EXISTS comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT CHECK(change_type IN ('NEW', 'REMOVED', 'INCREASED', 'DECREASED')),
                priority TEXT CHECK(priority IN ('HIGH', 'MODERATE', 'LOW')),
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Check after schema creation
        conn3 = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor3 = conn3.cursor()
        
        cursor3.execute('SELECT COUNT(*) FROM comparison_results')
        after_count = cursor3.fetchone()[0]
        print(f'Records after schema creation: {after_count}')
        
        conn3.close()
        
        if before_count > after_count:
            print('🚨 SCHEMA CREATION IS CLEARING DATA!')
            print('   The CHECK constraints might be incompatible with existing data')
            return False
        else:
            print('✅ Schema creation is not clearing data')
            print('   The issue must be elsewhere in the initialization')
            return True
        
    except Exception as e:
        print(f"❌ Schema check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_table_schema()
    
    if success:
        print("\n✅ SCHEMA IS NOT THE ISSUE")
        print("   Need to look elsewhere for data clearing cause")
    else:
        print("\n🚨 SCHEMA ISSUE FOUND")
        print("   This is causing the data to be cleared!")
