#!/usr/bin/env python3
"""
Verify the system can regenerate all data correctly with fixes
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_system_regeneration():
    """Verify system can regenerate data correctly"""
    print("🔄 VERIFYING SYSTEM REGENERATION WITH ALL FIXES")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Verify extracted data is still available
        print("\n1. 📊 EXTRACTED DATA VERIFICATION:")
        cursor.execute("""
            SELECT period_type, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (current_session,))
        
        extracted_data = cursor.fetchall()
        for row in extracted_data:
            print(f"   {row[0]}: {row[1]} records")
        
        if len(extracted_data) >= 2:
            print("   ✅ Extracted data available for regeneration")
        else:
            print("   ❌ Insufficient extracted data")
            return
        
        # 2. Run full workflow regeneration
        print("\n2. 🔄 RUNNING FULL WORKFLOW REGENERATION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Step 1: Regenerate comparison results
            print("   Step 1: Regenerating comparison results...")
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                comparison_results = manager._compare_payroll_data(current_data, previous_data)
                manager._store_comparison_results(comparison_results)
                
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                comparison_count = cursor.fetchone()[0]
                print(f"   ✅ Comparison results regenerated: {comparison_count}")
                
                # Check NEW items
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
                new_count = cursor.fetchone()[0]
                print(f"   ✅ NEW items detected: {new_count}")
                
                # Step 2: Regenerate PRE-REPORTING
                print("   Step 2: Regenerating PRE-REPORTING...")
                options = {'report_name': 'Regeneration Test', 'report_designation': 'System Test'}
                
                pre_reporting_result = manager._phase_pre_reporting(options)
                
                if pre_reporting_result:
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                    pre_reporting_count = cursor.fetchone()[0]
                    print(f"   ✅ PRE-REPORTING regenerated: {pre_reporting_count}")
                    
                    # Test get_pre_reporting_data
                    data_result = manager.get_pre_reporting_data(current_session)
                    if data_result.get('success'):
                        print(f"   ✅ get_pre_reporting_data working: {len(data_result.get('data', []))} items")
                    else:
                        print("   ❌ get_pre_reporting_data failed")
                else:
                    print("   ❌ PRE-REPORTING regeneration failed")
                
                # Step 3: Verify TRACKER FEEDING still works
                print("   Step 3: Verifying TRACKER FEEDING...")
                new_items = manager._load_new_items_for_tracking()
                print(f"   ✅ NEW items for tracking: {len(new_items)}")
                
                if len(new_items) > 0:
                    # Check if Bank Adviser tables still have data
                    cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
                    in_house_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
                    external_count = cursor.fetchone()[0]
                    
                    cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
                    motor_count = cursor.fetchone()[0]
                    
                    total_bank_adviser = in_house_count + external_count + motor_count
                    print(f"   ✅ Bank Adviser tables: {total_bank_adviser} records preserved")
                
                # Step 4: Verify AUTO LEARNING still works
                print("   Step 4: Verifying AUTO LEARNING...")
                cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ?", (current_session,))
                dictionary_count = cursor.fetchone()[0]
                print(f"   ✅ Dictionary Manager: {dictionary_count} items preserved")
                
            else:
                print("   ❌ Could not load extracted data")
                return
        
        except Exception as e:
            print(f"   ❌ Workflow regeneration failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 3. Final verification of all components
        print("\n3. ✅ FINAL VERIFICATION AFTER REGENERATION:")
        
        # Check all key tables
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        final_comparison = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        final_pre_reporting = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        final_in_house = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        final_external = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        final_motor = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ?", (current_session,))
        final_dictionary = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = 'NEW'", (current_session,))
        final_new_items = cursor.fetchone()[0]
        
        print(f"   Comparison results: {final_comparison}")
        print(f"   PRE-REPORTING results: {final_pre_reporting}")
        print(f"   NEW items detected: {final_new_items}")
        print(f"   Bank Adviser - In-house loans: {final_in_house}")
        print(f"   Bank Adviser - External loans: {final_external}")
        print(f"   Bank Adviser - Motor vehicles: {final_motor}")
        print(f"   Dictionary Manager items: {final_dictionary}")
        
        # Calculate success metrics
        total_bank_adviser = final_in_house + final_external + final_motor
        
        components_working = 0
        total_components = 4
        
        if final_pre_reporting > 0:
            components_working += 1
            print("   ✅ PRE-REPORTING: Working")
        else:
            print("   ❌ PRE-REPORTING: Not working")
        
        if total_bank_adviser > 0:
            components_working += 1
            print("   ✅ TRACKER FEEDING (Bank Adviser): Working")
        else:
            print("   ❌ TRACKER FEEDING (Bank Adviser): Not working")
        
        if final_dictionary > 0:
            components_working += 1
            print("   ✅ AUTO LEARNING (Dictionary Manager): Working")
        else:
            print("   ❌ AUTO LEARNING (Dictionary Manager): Not working")
        
        if final_new_items > 0:
            components_working += 1
            print("   ✅ NEW RULE: Working")
        else:
            print("   ❌ NEW RULE: Not working")
        
        success_rate = (components_working / total_components) * 100
        
        print(f"\n🎯 FINAL SYSTEM STATUS:")
        print(f"   Working components: {components_working}/{total_components}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("\n🎉 ALL FIXES SUCCESSFULLY IMPLEMENTED!")
            print("✅ PRE-REPORTING: Fully functional with comparison data")
            print("✅ TRACKER FEEDING: Bank Adviser tables populated with NEW items")
            print("✅ AUTO LEARNING: Dictionary Manager receiving data")
            print("✅ NEW RULE: Correctly detecting and applying NEW items")
            print("\n🚀 The payroll audit system is fully operational!")
        elif success_rate >= 75:
            print(f"\n✅ SYSTEM WORKING WELL ({success_rate:.1f}%)")
            print("Most fixes are successful with minor issues")
        else:
            print(f"\n⚠️ SYSTEM NEEDS MORE WORK ({success_rate:.1f}%)")
            print("Some fixes may not have taken full effect")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_system_regeneration()
