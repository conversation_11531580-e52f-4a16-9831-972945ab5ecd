#!/usr/bin/env python3
"""
Test the comprehensive session protection system
"""

import sys
import os
import sqlite3

def test_session_protection():
    """Test all aspects of the session protection system"""
    print("🛡️ TESTING SESSION PROTECTION SYSTEM")
    print("=" * 70)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # TEST 1: Session Guidance System
        print("\n1. 🎯 TESTING SESSION GUIDANCE SYSTEM")
        
        # Check if system can find the correct session
        cursor.execute('''
            SELECT session_id, is_primary, data_quality_score,
                   (SELECT COUNT(*) FROM comparison_results WHERE session_id = session_metadata.session_id) as comparison_count
            FROM session_metadata 
            WHERE is_primary = TRUE
        ''')
        
        primary_session = cursor.fetchone()
        
        if primary_session:
            session_id, is_primary, quality_score, comparison_count = primary_session
            print(f"   ✅ Primary session found: {session_id}")
            print(f"   ✅ Quality score: {quality_score}")
            print(f"   ✅ Comparison results: {comparison_count}")
            
            # Check if current session matches primary
            cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
            current_session = cursor.fetchone()[0]
            
            if current_session == session_id:
                print("   ✅ Current session matches primary session")
            else:
                print(f"   ⚠️ Current session ({current_session}) differs from primary ({session_id})")
        else:
            print("   ❌ No primary session found")
        
        # TEST 2: Duplication Prevention
        print("\n2. 🔒 TESTING DUPLICATION PREVENTION")
        
        # Check for duplicate sessions
        cursor.execute('''
            SELECT COUNT(*) as session_count, current_pdf_path, previous_pdf_path
            FROM audit_sessions 
            WHERE created_at > datetime('now', '-24 hours')
            GROUP BY current_pdf_path, previous_pdf_path
            HAVING session_count > 1
        ''')
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"   ⚠️ Found {len(duplicates)} sets of duplicate sessions:")
            for count, current_pdf, previous_pdf in duplicates:
                print(f"      {count} sessions with same PDFs")
        else:
            print("   ✅ No duplicate sessions found")
        
        # TEST 3: Session Health Monitoring
        print("\n3. 📊 TESTING SESSION HEALTH MONITORING")
        
        cursor.execute("SELECT * FROM session_health_view LIMIT 5")
        health_data = cursor.fetchall()
        
        print(f"   Session health data ({len(health_data)} sessions):")
        for session_data in health_data:
            session_id, status, created_at, is_primary, quality_score, extracted_count, comparison_count = session_data
            print(f"      {session_id}: {status} (Primary: {is_primary}, Score: {quality_score})")
            print(f"         Data: {extracted_count} extracted, {comparison_count} comparison")
        
        # TEST 4: Consolidation Rules
        print("\n4. 📋 TESTING CONSOLIDATION RULES")
        
        cursor.execute("SELECT rule_name, rule_description, priority, is_active FROM session_consolidation_rules")
        rules = cursor.fetchall()
        
        print(f"   Consolidation rules ({len(rules)} active):")
        for rule_name, description, priority, is_active in rules:
            status = "✅ Active" if is_active else "❌ Inactive"
            print(f"      {rule_name}: {description} (Priority: {priority}) {status}")
        
        # TEST 5: Session Guidance Log
        print("\n5. 📝 TESTING SESSION GUIDANCE LOG")
        
        cursor.execute('''
            SELECT timestamp, action_type, new_session_id, reason 
            FROM session_guidance_log 
            ORDER BY timestamp DESC 
            LIMIT 5
        ''')
        
        log_entries = cursor.fetchall()
        
        print(f"   Recent guidance actions ({len(log_entries)} entries):")
        for timestamp, action_type, new_session_id, reason in log_entries:
            print(f"      {timestamp}: {action_type}")
            print(f"         Session: {new_session_id}")
            print(f"         Reason: {reason}")
        
        # TEST 6: Protection Integration Test
        print("\n6. 🧪 TESTING PROTECTION INTEGRATION")
        
        # Test the PhasedProcessManager protection
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        
        # Test session uniqueness check
        test_pdf_current = "test_current.pdf"
        test_pdf_previous = "test_previous.pdf"
        
        existing_session = manager._ensure_session_uniqueness(test_pdf_current, test_pdf_previous)
        
        if existing_session:
            print(f"   ✅ Session uniqueness check returned: {existing_session}")
        else:
            print("   ✅ Session uniqueness check returned None (would create new)")
        
        # TEST 7: Data Integrity Verification
        print("\n7. ✅ TESTING DATA INTEGRITY")
        
        # Verify current session has complete data
        cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
        current_session = cursor.fetchone()[0]
        
        # Check data completeness
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (current_session,))
        extracted_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT status FROM audit_sessions WHERE session_id = ?", (current_session,))
        session_status = cursor.fetchone()[0]
        
        print(f"   Current session data integrity:")
        print(f"      Session: {current_session}")
        print(f"      Status: {session_status}")
        print(f"      Extracted: {extracted_count} records")
        print(f"      Comparison: {comparison_count} results")
        
        # Overall assessment
        data_complete = extracted_count > 0 and comparison_count > 0
        status_correct = session_status in ['pre_reporting_ready', 'completed']
        
        if data_complete and status_correct:
            print("   ✅ Data integrity verified")
        else:
            print("   ⚠️ Data integrity issues detected")
        
        # FINAL ASSESSMENT
        print("\n🎯 FINAL PROTECTION SYSTEM ASSESSMENT")
        
        protection_score = 0
        max_score = 7
        
        # Score each test
        if primary_session and comparison_count > 0:
            protection_score += 1
            print("   ✅ Session guidance working")
        
        if not duplicates:
            protection_score += 1
            print("   ✅ Duplication prevention working")
        
        if len(health_data) > 0:
            protection_score += 1
            print("   ✅ Health monitoring working")
        
        if len(rules) >= 4:
            protection_score += 1
            print("   ✅ Consolidation rules active")
        
        if len(log_entries) > 0:
            protection_score += 1
            print("   ✅ Guidance logging working")
        
        if hasattr(manager, '_ensure_session_uniqueness'):
            protection_score += 1
            print("   ✅ Protection integration working")
        
        if data_complete and status_correct:
            protection_score += 1
            print("   ✅ Data integrity maintained")
        
        protection_percentage = (protection_score / max_score) * 100
        
        print(f"\n   📊 PROTECTION SYSTEM SCORE: {protection_score}/{max_score} ({protection_percentage:.1f}%)")
        
        if protection_percentage >= 85:
            print("   🎉 PROTECTION SYSTEM EXCELLENT!")
            print("   ✅ Root cause fully resolved")
            print("   ✅ System protected against session duplication")
            print("   ✅ Automatic session guidance working")
            print("   ✅ Data integrity maintained")
        elif protection_percentage >= 70:
            print("   ✅ PROTECTION SYSTEM GOOD")
            print("   ⚠️ Minor improvements needed")
        else:
            print("   ⚠️ PROTECTION SYSTEM NEEDS IMPROVEMENT")
            print("   ❌ Additional work required")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Protection test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_session_protection()
