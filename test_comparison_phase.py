#!/usr/bin/env python3
"""
Test the comparison phase directly to see what's failing
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_comparison_phase():
    """Test the comparison phase directly"""
    print("🔍 TESTING COMPARISON PHASE DIRECTLY")
    print("=" * 60)
    
    try:
        # Import the manager
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager
        
        # Get the latest session from database
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get latest session
        cursor.execute("""
            SELECT session_id FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        session_result = cursor.fetchone()
        
        if not session_result:
            print("❌ No audit sessions found")
            return
        
        session_id = session_result[0]
        print(f"🎯 Testing session: {session_id}")
        
        # Create manager and set session
        manager = PhasedProcessManager()
        manager.session_id = session_id
        
        # Test _load_extracted_data method
        print("\n1. 📊 TESTING _load_extracted_data METHOD:")
        
        print("   Loading current data...")
        current_data = manager._load_extracted_data('current')
        print(f"   Current data loaded: {len(current_data)} employees")
        
        print("   Loading previous data...")
        previous_data = manager._load_extracted_data('previous')
        print(f"   Previous data loaded: {len(previous_data)} employees")
        
        if not current_data:
            print("   ❌ FAILED TO LOAD CURRENT DATA")
            return
        
        if not previous_data:
            print("   ❌ FAILED TO LOAD PREVIOUS DATA")
            return
        
        # Sample the data structure
        print("\n2. 📋 DATA STRUCTURE SAMPLE:")
        if current_data:
            sample_emp = current_data[0]
            print(f"   Sample current employee:")
            print(f"     Employee ID: {sample_emp.get('employee_id')}")
            print(f"     Employee Name: {sample_emp.get('employee_name')}")
            print(f"     Sections: {list(sample_emp.get('sections', {}).keys())}")
            
            # Sample a section
            sections = sample_emp.get('sections', {})
            if sections:
                section_name = list(sections.keys())[0]
                section_data = sections[section_name]
                print(f"     Sample section '{section_name}': {len(section_data)} items")
                
                # Sample an item
                if section_data:
                    item_name = list(section_data.keys())[0]
                    item_data = section_data[item_name]
                    print(f"     Sample item '{item_name}': {item_data}")
        
        # Test comparison logic
        print("\n3. 🔄 TESTING COMPARISON LOGIC:")
        
        print("   Running _compare_payroll_data...")
        try:
            comparison_results = manager._compare_payroll_data(current_data, previous_data)
            print(f"   Comparison results: {len(comparison_results)} changes found")
            
            if comparison_results:
                print("   ✅ COMPARISON LOGIC WORKING!")
                
                # Sample some results
                print("   Sample comparison results:")
                for i, result in enumerate(comparison_results[:5]):
                    print(f"     {i+1}. Employee {result['employee_id']}: {result['section_name']}.{result['item_label']}")
                    print(f"        {result['previous_value']} → {result['current_value']} ({result['change_type']})")
                
                # Test storing results
                print("\n4. 💾 TESTING RESULT STORAGE:")
                try:
                    manager._store_comparison_results(comparison_results)
                    print("   ✅ Results stored successfully!")
                    
                    # Verify storage
                    cursor.execute("""
                        SELECT COUNT(*) FROM comparison_results 
                        WHERE session_id = ?
                    """, (session_id,))
                    stored_count = cursor.fetchone()[0]
                    print(f"   Verified: {stored_count} results stored in database")
                    
                except Exception as e:
                    print(f"   ❌ FAILED TO STORE RESULTS: {e}")
                    import traceback
                    traceback.print_exc()
                
            else:
                print("   ⚠️ NO CHANGES DETECTED")
                print("   This could be normal if the data is identical")
                
                # Let's check if the data is actually identical
                print("\n   🔍 CHECKING IF DATA IS IDENTICAL:")
                
                # Compare first few employees manually
                current_lookup = {emp['employee_id']: emp for emp in current_data[:5]}
                previous_lookup = {emp['employee_id']: emp for emp in previous_data[:5]}
                
                for emp_id in list(current_lookup.keys())[:3]:
                    current_emp = current_lookup.get(emp_id)
                    previous_emp = previous_lookup.get(emp_id)
                    
                    if current_emp and previous_emp:
                        current_sections = current_emp.get('sections', {})
                        previous_sections = previous_emp.get('sections', {})
                        
                        print(f"     Employee {emp_id}:")
                        print(f"       Current sections: {len(current_sections)}")
                        print(f"       Previous sections: {len(previous_sections)}")
                        
                        # Compare a section
                        if current_sections and previous_sections:
                            section_name = list(current_sections.keys())[0]
                            if section_name in previous_sections:
                                current_section = current_sections[section_name]
                                previous_section = previous_sections[section_name]
                                
                                print(f"       Section '{section_name}':")
                                print(f"         Current items: {len(current_section)}")
                                print(f"         Previous items: {len(previous_section)}")
                                
                                # Compare an item
                                if current_section and previous_section:
                                    item_name = list(current_section.keys())[0]
                                    if item_name in previous_section:
                                        current_item = current_section[item_name]
                                        previous_item = previous_section[item_name]
                                        
                                        print(f"         Item '{item_name}':")
                                        print(f"           Current: {current_item}")
                                        print(f"           Previous: {previous_item}")
                                        print(f"           Same: {current_item == previous_item}")
                
        except Exception as e:
            print(f"   ❌ COMPARISON FAILED: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_comparison_phase()
