#!/usr/bin/env python3
"""
Simple fix for UI freeze issue
"""

import sqlite3
import sys
import os
sys.path.append('.')

def simple_ui_freeze_fix():
    """Simple fix for UI freeze by creating manageable test data"""
    print("🔧 SIMPLE UI FREEZE FIX")
    print("=" * 40)
    
    try:
        # Step 1: Create manageable test data
        print("1. 📊 CREATING MANAGEABLE TEST DATA:")
        
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"   Current session: {current_session}")
        
        # Clear existing data
        cursor.execute('DELETE FROM comparison_results WHERE session_id = ?', (current_session,))
        
        # Create a small, manageable dataset for testing (10 records)
        test_data = [
            ('E001', '<PERSON>', 'EARNINGS', 'BASIC SALARY', '5000.00', '5500.00', 'INCREASED', 'HIGH', 500.00, 10.0),
            ('E002', '<PERSON>', 'DEDUCTIONS', 'INCOME TAX', '800.00', '850.00', 'INCREASED', 'MEDIUM', 50.00, 6.25),
            ('E003', 'Bob Johnson', 'PERSONAL DETAILS', 'DEPARTMENT', 'IT', 'HR', 'CHANGED', 'HIGH', 0, 0),
            ('E004', 'Alice Brown', 'LOANS', 'CAR LOAN', '2000.00', '', 'REMOVED', 'MEDIUM', -2000.00, -100.0),
            ('E005', 'Charlie Wilson', 'EARNINGS', 'OVERTIME', '', '300.00', 'NEW', 'LOW', 300.00, 0),
            ('E006', 'Diana Prince', 'DEDUCTIONS', 'PENSION', '400.00', '420.00', 'INCREASED', 'MEDIUM', 20.00, 5.0),
            ('E007', 'Frank Miller', 'EARNINGS', 'ALLOWANCE', '200.00', '180.00', 'DECREASED', 'LOW', -20.00, -10.0),
            ('E008', 'Grace Lee', 'PERSONAL DETAILS', 'PHONE', '123-456', '123-789', 'CHANGED', 'LOW', 0, 0),
            ('E009', 'Henry Ford', 'LOANS', 'HOUSE LOAN', '', '5000.00', 'NEW', 'HIGH', 5000.00, 0),
            ('E010', 'Ivy Chen', 'DEDUCTIONS', 'HEALTH INSURANCE', '150.00', '160.00', 'INCREASED', 'MEDIUM', 10.00, 6.67)
        ]
        
        for data in test_data:
            cursor.execute('''
                INSERT INTO comparison_results 
                (session_id, employee_id, employee_name, section_name, item_label,
                 previous_value, current_value, change_type, priority,
                 numeric_difference, percentage_change)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (current_session,) + data)
        
        conn.commit()
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
        count = cursor.fetchone()[0]
        print(f"   ✅ Created {count} manageable test records")
        
        # Step 2: Test the pre-reporting API
        print("\n2. 🧪 TESTING PRE-REPORTING API:")
        
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=False)
        result = manager.get_pre_reporting_data(current_session)
        
        print(f"   API success: {result.get('success')}")
        print(f"   API data count: {len(result.get('data', []))}")
        
        if result.get('success') and result.get('data'):
            print("   ✅ API working with small dataset")
            
            # Sample the data
            data = result.get('data', [])
            print(f"   Sample items:")
            for i, item in enumerate(data[:3]):
                print(f"      {i+1}. {item.get('employee_id')}: {item.get('section_name')}.{item.get('item_label')}")
                print(f"         {item.get('previous_value')} → {item.get('current_value')} ({item.get('change_type')})")
        
        # Step 3: Update session status
        print("\n3. 📋 UPDATING SESSION STATUS:")
        
        cursor.execute('UPDATE audit_sessions SET status = ? WHERE session_id = ?', ('pre_reporting_ready', current_session))
        
        # Update phase status
        cursor.execute('''
            INSERT OR REPLACE INTO session_phases 
            (session_id, phase_name, status, data_count)
            VALUES (?, ?, ?, ?)
        ''', (current_session, 'PRE_REPORTING', 'WAITING_FOR_USER', count))
        
        conn.commit()
        
        print(f"   ✅ Session {current_session} ready for pre-reporting")
        
        # Step 4: Verify final state
        print("\n4. ✅ VERIFYING FINAL STATE:")
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        final_session = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (final_session,))
        final_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT status FROM audit_sessions WHERE session_id = ?', (final_session,))
        final_status = cursor.fetchone()[0]
        
        print(f"   Final session: {final_session}")
        print(f"   Final data count: {final_count}")
        print(f"   Final status: {final_status}")
        
        if final_count > 0 and final_status == 'pre_reporting_ready':
            print("   ✅ SYSTEM READY FOR UI TESTING")
            print("   ✅ Small dataset should not cause UI freeze")
            print("   ✅ Pre-reporting API working correctly")
            
            # Step 5: UI Performance Tips
            print("\n5. 💡 UI PERFORMANCE TIPS:")
            print("   • Small dataset (10 items) should load instantly")
            print("   • If UI still freezes, check for infinite loops in renderer.js")
            print("   • Monitor browser console for JavaScript errors")
            print("   • Check if UI is waiting for user interaction")
            print("   • Verify that progress indicators are not stuck")
            
            conn.close()
            return True
        else:
            print("   ❌ System not ready")
            conn.close()
            return False
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_ui_freeze_fix()
    
    if success:
        print("\n🎉 SIMPLE UI FREEZE FIX COMPLETE!")
        print("   ✅ Small manageable dataset created (10 items)")
        print("   ✅ Pre-reporting API working")
        print("   ✅ Session ready for UI testing")
        print("\n   🚀 NEXT STEPS:")
        print("   1. Test the Pre-Reporting UI now")
        print("   2. If it still freezes, check browser console for errors")
        print("   3. Look for infinite loops or stuck progress indicators")
        print("   4. The issue may be in the UI logic, not the data")
    else:
        print("\n⚠️ UI FREEZE FIX INCOMPLETE")
        print("   Additional work needed.")
