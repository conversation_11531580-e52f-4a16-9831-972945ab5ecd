#!/usr/bin/env python3
"""
Final fix for the comparison_results table schema issue
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def final_comparison_fix():
    """Final fix for the comparison schema"""
    print("🔧 FINAL COMPARISON SCHEMA FIX")
    print("=" * 60)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return
    
    print(f"📁 Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Drop the table completely
        print("\n1. 🗑️ DROPPING COMPARISON_RESULTS TABLE:")
        cursor.execute("DROP TABLE IF EXISTS comparison_results")
        conn.commit()
        print("   Table dropped")
        
        # 2. Create new table without any constraints
        print("\n2. 🆕 CREATING NEW TABLE WITHOUT CONSTRAINTS:")
        create_sql = '''
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT,
                priority TEXT,
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''
        
        cursor.execute(create_sql)
        conn.commit()
        print("   New table created without constraints")
        
        # 3. Verify table creation
        print("\n3. ✅ VERIFYING TABLE:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        
        if result:
            print("   Table definition:")
            print(f"   {result[0]}")
        
        # 4. Test all change types
        print("\n4. 🧪 TESTING ALL CHANGE TYPES:")
        
        # Get latest session
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if session_result:
            session_id = session_result[0]
            
            change_types = ['NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED']
            
            for i, change_type in enumerate(change_types):
                test_data = (
                    session_id, f'TEST{i:03d}', f'Test Employee {i}', 'EARNINGS', 'TEST ITEM',
                    '100.00', '200.00', change_type, 'HIGH', 100.0, 100.0
                )
                
                try:
                    cursor.execute('''
                        INSERT INTO comparison_results 
                        (session_id, employee_id, employee_name, section_name, item_label,
                         previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', test_data)
                    print(f"   ✅ {change_type}: SUCCESS")
                except Exception as e:
                    print(f"   ❌ {change_type}: FAILED - {e}")
            
            conn.commit()
            
            # Clean up test data
            cursor.execute("DELETE FROM comparison_results WHERE employee_id LIKE 'TEST%'")
            conn.commit()
        
        # 5. Test with the actual phased process manager
        print("\n5. 🔄 TESTING WITH PHASED PROCESS MANAGER:")
        
        try:
            # Import the manager
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            # Create manager and set session
            manager = PhasedProcessManager()
            manager.session_id = session_id
            
            # Test storing a single comparison result
            test_results = [{
                'employee_id': 'FINAL_TEST',
                'employee_name': 'Final Test Employee',
                'section_name': 'EARNINGS',
                'item_label': 'BASIC SALARY',
                'previous_value': '1000.00',
                'current_value': '1100.00',
                'change_type': 'CHANGED',
                'priority': 'HIGH',
                'numeric_difference': 100.0,
                'percentage_change': 10.0
            }]
            
            manager._store_comparison_results(test_results)
            print("   ✅ Phased process manager storage working!")
            
            # Verify storage
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE employee_id = 'FINAL_TEST'")
            count = cursor.fetchone()[0]
            print(f"   ✅ Verified: {count} record stored")
            
            # Clean up
            cursor.execute("DELETE FROM comparison_results WHERE employee_id = 'FINAL_TEST'")
            conn.commit()
            
        except Exception as e:
            print(f"   ❌ Phased process manager test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
        print("\n✅ FINAL COMPARISON SCHEMA FIX COMPLETED!")
        print("   The comparison_results table now accepts all change types")
        print("   The comparison phase should now work correctly")
        
    except Exception as e:
        print(f"❌ Error during final fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_comparison_fix()
