#!/usr/bin/env python3
"""
Complete PRE-REPORTING setup with proper data loading
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def complete_pre_reporting_setup():
    """Complete PRE-REPORTING setup"""
    print("🔧 COMPLETING PRE-REPORTING SETUP")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 GETTING CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Verify comparison results exist
        print("\n2. 📊 VERIFYING COMPARISON RESULTS:")
        
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        if comparison_count == 0:
            print("   ❌ No comparison results found")
            return
        
        # 3. Load comparison results and run PRE-REPORTING
        print("\n3. 🔄 RUNNING PRE-REPORTING PHASE:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Load comparison results directly from database
            cursor.execute("""
                SELECT id, employee_id, employee_name, section_name, item_label,
                       previous_value, current_value, change_type, priority,
                       numeric_difference, percentage_change
                FROM comparison_results 
                WHERE session_id = ?
                ORDER BY priority DESC, section_name, employee_id
            """, (current_session,))
            
            comparison_rows = cursor.fetchall()
            
            if comparison_rows:
                print(f"   ✅ Loaded {len(comparison_rows)} comparison results from database")
                
                # Convert to proper format
                all_changes = []
                for row in comparison_rows:
                    change = {
                        'id': row[0],
                        'employee_id': row[1],
                        'employee_name': row[2],
                        'section_name': row[3],
                        'item_label': row[4],
                        'previous_value': row[5],
                        'current_value': row[6],
                        'change_type': row[7],
                        'priority': row[8],
                        'numeric_difference': row[9],
                        'percentage_change': row[10]
                    }
                    all_changes.append(change)
                
                print(f"   ✅ Converted {len(all_changes)} changes to proper format")
                
                # Categorize changes for reporting
                categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                print(f"   ✅ Categorized changes for reporting")
                
                # Apply auto-selection rules
                auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                print(f"   ✅ Applied auto-selection rules")
                
                # Store pre-reporting results
                manager._store_pre_reporting_results(categorized_changes, auto_selected)
                print(f"   ✅ Stored pre-reporting results")
                
                # Update session phase status
                from core.session_manager import get_session_manager
                session_manager = get_session_manager()
                session_manager.update_phase_status('PRE_REPORTING', 'COMPLETED', len(categorized_changes))
                
                # Verify storage
                cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                pre_reporting_count = cursor.fetchone()[0]
                
                print(f"   ✅ Generated {pre_reporting_count} pre-reporting results")
            else:
                print("   ❌ No comparison data loaded from database")
                return
        
        except Exception as e:
            print(f"   ❌ Pre-reporting phase failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 4. Test the UI data methods
        print("\n4. 🧪 TESTING UI DATA METHODS:")
        
        try:
            # Test get_pre_reporting_data
            result = manager.get_pre_reporting_data(current_session)
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                
                print(f"   ✅ get_pre_reporting_data:")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                # Test get_latest_pre_reporting_data (what UI calls)
                latest_result = manager.get_latest_pre_reporting_data()
                
                if latest_result.get('success') and latest_result.get('data'):
                    latest_count = len(latest_result.get('data', []))
                    latest_total = latest_result.get('total_changes', 0)
                    latest_session_id = latest_result.get('session_id', 'unknown')
                    
                    print(f"   ✅ get_latest_pre_reporting_data:")
                    print(f"     Session: {latest_session_id}")
                    print(f"     Data items: {latest_count}")
                    print(f"     Total changes: {latest_total}")
                    
                    # Analyze data for UI
                    categories = {}
                    auto_selected_count = 0
                    
                    for item in latest_result['data']:
                        category = item.get('bulk_category', 'Unknown')
                        if category not in categories:
                            categories[category] = 0
                        categories[category] += 1
                        
                        if item.get('selected_for_report'):
                            auto_selected_count += 1
                    
                    print(f"   📊 UI Data Analysis:")
                    print(f"     Categories: {list(categories.keys())}")
                    for category, count in categories.items():
                        print(f"       {category}: {count} changes")
                    print(f"     Auto-selected: {auto_selected_count}")
                    print(f"     Pending review: {latest_count - auto_selected_count}")
                    
                    # Show sample data structure
                    if latest_count > 0:
                        sample = latest_result['data'][0]
                        print(f"   📋 Sample data for UI:")
                        print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                        print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                        print(f"     Previous: {sample.get('previous_value')}")
                        print(f"     Current: {sample.get('current_value')}")
                        print(f"     Type: {sample.get('change_type')}")
                        print(f"     Category: {sample.get('bulk_category')}")
                        print(f"     Priority: {sample.get('priority')}")
                        print(f"     Selected: {sample.get('selected_for_report')}")
                    
                    print(f"\n🎉 PRE-REPORTING INTERACTIVE UI FULLY READY!")
                    print(f"✅ Session: {current_session}")
                    print(f"✅ Comparison results: {comparison_count}")
                    print(f"✅ Pre-reporting results: {pre_reporting_count}")
                    print(f"✅ UI data available: {latest_count} changes")
                    print(f"✅ Categories: {len(categories)} bulk categories")
                    print(f"✅ Auto-selection: {auto_selected_count} pre-selected")
                    
                    print(f"\n🎯 INTERACTIVE UI FEATURES:")
                    print(f"📋 ✅ Change Review & Selection")
                    print(f"📋 ✅ Bulk Categorization (Individual/Small/Medium/Large)")
                    print(f"📋 ✅ Auto-Selection Rules Applied")
                    print(f"📋 ✅ Manual Selection/Deselection")
                    print(f"📋 ✅ Summary Statistics")
                    print(f"📋 ✅ Generate Final Report Button")
                    
                    print(f"\n🚀 UI SHOULD NOW LOAD WITH {latest_count} CHANGES FOR USER INTERACTION!")
                    
                else:
                    print(f"   ❌ get_latest_pre_reporting_data failed: {latest_result}")
            else:
                print(f"   ❌ get_pre_reporting_data failed: {result}")
        
        except Exception as e:
            print(f"   ❌ UI data method test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    complete_pre_reporting_setup()
