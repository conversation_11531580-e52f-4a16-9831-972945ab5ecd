#!/usr/bin/env python3
"""
Debug the current session that's failing in the full workflow
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_current_session():
    """Debug the current session from the logs"""
    print("🔍 DEBUGGING CURRENT SESSION FROM LOGS")
    print("=" * 60)
    
    # The session from the logs
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Investigating session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check if this session exists
        print("\n1. 📋 SESSION EXISTENCE CHECK:")
        cursor.execute("SELECT session_id, status, created_at FROM audit_sessions WHERE session_id = ?", (current_session,))
        session_info = cursor.fetchone()
        
        if session_info:
            print(f"   ✅ Session exists: {session_info[0]}")
            print(f"   Status: {session_info[1]}")
            print(f"   Created: {session_info[2]}")
        else:
            print(f"   ❌ Session {current_session} does not exist")
            
            # Check latest sessions
            cursor.execute("SELECT session_id, status, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 5")
            latest_sessions = cursor.fetchall()
            print("\n   Latest sessions:")
            for session in latest_sessions:
                print(f"     {session[0]} - {session[1]} - {session[2]}")
        
        # 2. Check extracted data for this session
        print("\n2. 📊 EXTRACTED DATA CHECK:")
        cursor.execute("""
            SELECT period_type, COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ?
            GROUP BY period_type
        """, (current_session,))
        extracted_data = cursor.fetchall()
        
        if extracted_data:
            for row in extracted_data:
                print(f"   {row[0]}: {row[1]} records")
        else:
            print(f"   ❌ No extracted data for session {current_session}")
        
        # 3. Check comparison results for this session
        print("\n3. 📊 COMPARISON RESULTS CHECK:")
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        # 4. Check pre-reporting results for this session
        print("\n4. 📊 PRE-REPORTING RESULTS CHECK:")
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   Pre-reporting results: {pre_reporting_count}")
        
        # 5. Test the get_pre_reporting_data method with this session
        print("\n5. 🔄 TESTING get_pre_reporting_data METHOD:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            
            # Test with the specific session
            result = manager.get_pre_reporting_data(current_session)
            print(f"   Result success: {result.get('success', False)}")
            print(f"   Data count: {len(result.get('data', []))}")
            print(f"   Total changes: {result.get('total_changes', 0)}")
            
            if not result.get('success', False):
                print(f"   Error: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            print(f"   ❌ Method test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Check if the session has completed the comparison phase
        print("\n6. 📊 PHASE COMPLETION CHECK:")
        
        # Check if there are any phase results for this session
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='phase_results'")
        phase_table_exists = cursor.fetchone()
        
        if phase_table_exists:
            cursor.execute("""
                SELECT phase_name, success, completed_at
                FROM phase_results 
                WHERE session_id = ?
                ORDER BY phase_order
            """, (current_session,))
            phase_results = cursor.fetchall()
            
            if phase_results:
                print("   Phase completion status:")
                for phase in phase_results:
                    status = "✅" if phase[1] else "❌"
                    print(f"     {status} {phase[0]} - {phase[2]}")
            else:
                print(f"   ❌ No phase results for session {current_session}")
        else:
            print("   ⚠️ phase_results table does not exist")
        
        # 7. Try to run the PRE-REPORTING phase for this session
        print("\n7. 🔄 ATTEMPTING PRE-REPORTING PHASE:")
        
        if extracted_data and len(extracted_data) >= 2:  # Has both current and previous data
            try:
                manager = PhasedProcessManager()
                manager.session_id = current_session
                
                # Check if comparison results exist, if not, run comparison first
                if comparison_count == 0:
                    print("   Running comparison phase first...")
                    
                    # Load extracted data
                    current_data = manager._load_extracted_data('current')
                    previous_data = manager._load_extracted_data('previous')
                    
                    if current_data and previous_data:
                        print(f"   Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                        
                        # Run comparison
                        comparison_results = manager._compare_payroll_data(current_data, previous_data)
                        print(f"   Found {len(comparison_results)} changes")
                        
                        # Store comparison results
                        manager._store_comparison_results(comparison_results)
                        print("   Comparison results stored")
                        
                        # Verify storage
                        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                        new_comparison_count = cursor.fetchone()[0]
                        print(f"   Verified: {new_comparison_count} comparison results stored")
                
                # Now run PRE-REPORTING phase
                print("   Running PRE-REPORTING phase...")
                options = {
                    'report_name': 'Current Session Report',
                    'report_designation': 'System Generated'
                }
                
                result = manager._phase_pre_reporting(options)
                
                if result:
                    print("   ✅ PRE-REPORTING phase completed successfully!")
                    
                    # Check stored results
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                    final_pre_reporting_count = cursor.fetchone()[0]
                    print(f"   ✅ Pre-reporting results stored: {final_pre_reporting_count}")
                    
                    # Test the get_pre_reporting_data method again
                    result = manager.get_pre_reporting_data(current_session)
                    print(f"   ✅ get_pre_reporting_data now returns: {len(result.get('data', []))} items")
                    
                else:
                    print("   ❌ PRE-REPORTING phase failed")
                
            except Exception as e:
                print(f"   ❌ PRE-REPORTING phase execution failed: {e}")
                import traceback
                traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_current_session()
