/**
 * ENHANCED IPC HANDLERS
 * IPC handlers for the enhanced phased payroll audit process
 * Integrates with main.js to provide enhanced functionality
 */

const { ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

/**
 * Enhanced IPC handlers to be added to main.js
 */

// Enhanced payroll audit with phased execution
ipcMain.handle('enhanced-payroll-audit', async (event, currentPdf, previousPdf, options = {}) => {
    try {
        console.log('[ENHANCED-AUDIT] Starting enhanced phased payroll audit');
        console.log(`[ENHANCED-AUDIT] Current PDF: ${currentPdf}`);
        console.log(`[ENHANCED-AUDIT] Previous PDF: ${previousPdf}`);
        
        const pythonPath = path.join(__dirname, 'enhanced_process_integration.py');
        const args = [
            'full_audit',
            currentPdf,
            previousPdf,
            JSON.stringify(options)
        ];
        
        console.log(`[ENHANCED-PYTHON] Running: python ${pythonPath} ${args.join(' ')}`);
        const result = await runEnhancedPythonScript(pythonPath, args, event);
        console.log(`[ENHANCED-PYTHON] Process exited with code: 0`);

        // runEnhancedPythonScript already returns parsed JSON
        console.log('[ENHANCED-AUDIT] Successfully received result from enhanced script');
        return result;
        
    } catch (error) {
        console.error('[ENHANCED-AUDIT] Error:', error);
        return {
            success: false,
            error: error.message,
            process_type: 'ENHANCED_PHASED_AUDIT'
        };
    }
});

// Tracker feeding only (for testing/standalone use)
ipcMain.handle('feed-tracker-tables', async (event, currentData, previousData, month, year) => {
    try {
        console.log('[TRACKER-FEED] Starting tracker table feeding');
        console.log(`[TRACKER-FEED] Period: ${month}/${year}`);
        
        const pythonPath = path.join(__dirname, 'enhanced_process_integration.py');
        const args = [
            'tracker_only',
            JSON.stringify(currentData),
            JSON.stringify(previousData),
            month,
            year
        ];
        
        const result = await runEnhancedPythonScript(pythonPath, args, event);
        return result;
        
    } catch (error) {
        console.error('[TRACKER-FEED] Error:', error);
        return {
            success: false,
            error: error.message,
            process_type: 'TRACKER_FEEDING'
        };
    }
});

// Auto learning only (for testing/standalone use)
ipcMain.handle('process-auto-learning', async (event, currentData, month, year) => {
    try {
        console.log('[AUTO-LEARNING] Starting auto learning process');
        console.log(`[AUTO-LEARNING] Period: ${month}/${year}`);
        
        const pythonPath = path.join(__dirname, 'enhanced_process_integration.py');
        const args = [
            'learning_only',
            JSON.stringify(currentData),
            month,
            year
        ];
        
        const result = await runEnhancedPythonScript(pythonPath, args, event);
        return result;
        
    } catch (error) {
        console.error('[AUTO-LEARNING] Error:', error);
        return {
            success: false,
            error: error.message,
            process_type: 'AUTO_LEARNING'
        };
    }
});

// Get enhanced process status
ipcMain.handle('get-enhanced-process-status', async (event) => {
    try {
        // This would typically query the current process status
        // For now, return a basic status
        return {
            success: true,
            status: 'READY',
            current_phase: null,
            available_phases: [
                'EXTRACTION',
                'COMPARISON',
                'TRACKER_LEARNING',
                'PRE_REPORTING',
                'REPORT_GENERATION'
            ]
        };

    } catch (error) {
        console.error('[ENHANCED-STATUS] Error:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// Get latest pre-reporting data from database
ipcMain.handle('get-latest-pre-reporting-data', async (event) => {
    try {
        const sqlite3 = require('sqlite3').verbose();
        const path = require('path');

        const dbPath = path.join(__dirname, 'data', 'templar_payroll_auditor.db');

        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(dbPath, (err) => {
                if (err) {
                    console.error('[PRE-REPORTING-DB] Database connection error:', err);
                    resolve({
                        success: false,
                        error: 'Database connection failed'
                    });
                    return;
                }

                // Get the latest pre-reporting session
                db.get(`
                    SELECT session_id, timestamp, analyzed_changes, total_changes, status
                    FROM pre_reporting_sessions
                    ORDER BY timestamp DESC
                    LIMIT 1
                `, (err, row) => {
                    db.close();

                    if (err) {
                        console.error('[PRE-REPORTING-DB] Query error:', err);
                        resolve({
                            success: false,
                            error: 'Database query failed'
                        });
                        return;
                    }

                    if (row) {
                        try {
                            const analyzedChanges = JSON.parse(row.analyzed_changes);
                            console.log(`[PRE-REPORTING-DB] Loaded session ${row.session_id} with ${row.total_changes} changes`);

                            resolve({
                                success: true,
                                data: {
                                    session_id: row.session_id,
                                    analyzed_changes: analyzedChanges,
                                    total_changes: row.total_changes,
                                    timestamp: row.timestamp,
                                    status: row.status
                                }
                            });
                        } catch (parseError) {
                            console.error('[PRE-REPORTING-DB] JSON parse error:', parseError);
                            resolve({
                                success: false,
                                error: 'Data parsing failed'
                            });
                        }
                    } else {
                        console.log('[PRE-REPORTING-DB] No pre-reporting data found');
                        resolve({
                            success: false,
                            error: 'No pre-reporting data available'
                        });
                    }
                });
            });
        });

    } catch (error) {
        console.error('[PRE-REPORTING-DB] Error:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// Initialize content switching UI
ipcMain.handle('initialize-content-switching', async (event) => {
    try {
        console.log('[CONTENT-SWITCHING] Initializing content switching UI');
        
        // Send initialization signal to renderer
        event.sender.send('content-switching-ready', {
            success: true,
            phases: [
                'extraction',
                'comparison',
                'tracker-learning', 
                'pre-reporting',
                'report-generation'
            ]
        });
        
        return {
            success: true,
            message: 'Content switching initialized'
        };
        
    } catch (error) {
        console.error('[CONTENT-SWITCHING] Error:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

/**
 * Run hybrid script function (copied from main.js)
 */
function runHybridScript(scriptPath, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`[HYBRID] Starting 100% accurate hybrid process: python ${scriptPath} ${args.join(' ')}`);

    const python = spawn('python', [scriptPath, ...args], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
        PYTHONUNBUFFERED: '1',  // Force unbuffered output
        PYTHONUTF8: '1'  // Force UTF-8 mode
      },
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    python.stdout.on('data', (data) => {
      const chunk = data.toString();
      output += chunk;
      console.log(`[HYBRID-BACKEND] ${chunk.trim()}`);
    });

    python.stderr.on('data', (data) => {
      const chunk = data.toString();
      errorOutput += chunk;
      console.error(`[HYBRID-ERROR] ${chunk.trim()}`);
    });

    python.on('close', (code) => {
      console.log(`[HYBRID] Process completed with code: ${code}`);
      if (code === 0) {
        resolve(output.trim());
      } else {
        reject(new Error(`Python script failed with code ${code}: ${errorOutput}`));
      }
    });

    python.on('error', (error) => {
      console.error(`[HYBRID] Process error: ${error.message}`);
      reject(error);
    });
  });
}

/**
 * Enhanced Python script runner with real-time progress updates
 */
async function runEnhancedPythonScript(scriptPath, args, event, options = {}) {
    return new Promise((resolve, reject) => {
        const timeout = options.timeout || 30 * 60 * 1000; // 30 minutes default
        
        console.log(`[ENHANCED-PYTHON] Running: python ${scriptPath} ${args.join(' ')}`);
        
        const pythonProcess = spawn('python', [scriptPath, ...args], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: __dirname,
            env: {
                ...process.env,
                PYTHONIOENCODING: 'utf-8',
                PYTHONUNBUFFERED: '1',
                PYTHONUTF8: '1',
                PYTHONLEGACYWINDOWSSTDIO: '0'
            }
        });
        
        let outputData = '';
        let errorData = '';
        let processTimeout;
        
        // Set timeout
        if (timeout > 0) {
            processTimeout = setTimeout(() => {
                pythonProcess.kill('SIGTERM');
                reject(new Error(`Process timeout after ${timeout}ms`));
            }, timeout);
        }
        
        // Handle stdout (progress updates and final result)
        pythonProcess.stdout.on('data', (data) => {
            const output = data.toString();
            outputData += output;
            
            // Send real-time progress updates to renderer
            const lines = output.split('\n');
            lines.forEach(line => {
                if (line.trim()) {
                    // Parse REALTIME_UPDATE JSON messages from Python
                    if (line.includes('REALTIME_UPDATE:')) {
                        try {
                            const updateJson = line.split('REALTIME_UPDATE:')[1].trim();

                            // Try to parse the complete JSON first
                            let updateData;
                            try {
                                updateData = JSON.parse(updateJson);
                            } catch (parseError) {
                                // Fallback: Find the end of JSON by looking for the closing brace
                                let jsonEnd = updateJson.lastIndexOf('}');
                                if (jsonEnd !== -1) {
                                    const cleanJson = updateJson.substring(0, jsonEnd + 1);
                                    updateData = JSON.parse(cleanJson);
                                } else {
                                    throw parseError;
                                }
                            }

                            // CRITICAL: Handle pre-reporting data ready events
                            if (updateData.type === 'pre_reporting_data_ready') {
                                console.log(`[PRE-REPORTING] Data ready with ${updateData.total_changes} changes`);

                                // Send pre-reporting data to UI
                                event.sender.send('pre-reporting-data-ready', updateData);

                                // Also send via enhanced log for debugging
                                event.sender.send('enhanced-log-update', {
                                    message: `Pre-reporting data ready: ${updateData.total_changes} changes categorized`,
                                    timestamp: new Date().toISOString(),
                                    type: 'info',
                                    data: updateData
                                });
                            }

                            // Send ALL real-time updates to enhanced-progress-update channel
                            event.sender.send('enhanced-progress-update', updateData);
                            console.log(`[ENHANCED-PROGRESS] ${updateData.type}: ${updateData.message || 'Update received'}`);

                            // Also send to legacy channel for backward compatibility
                            if (updateData.type === 'phase_progress') {
                                console.log(`[PHASE-PROGRESS] ${updateData.phase}: ${updateData.percentage}% - ${updateData.message}`);
                            }
                        } catch (parseError) {
                            console.error('[ENHANCED-IPC] Error parsing REALTIME_UPDATE:', parseError);
                        }
                        return;
                    }

                    // Check for progress indicators (legacy format)
                    if (line.includes('📊') && line.includes('%')) {
                        // Parse progress line
                        const progressMatch = line.match(/📊\s*\[([^\]]+)\]\s*(\d+)%\s*-\s*(.+)/);
                        if (progressMatch) {
                            const [, phase, percentage, message] = progressMatch;
                            event.sender.send('enhanced-progress-update', {
                                phase: phase,
                                percentage: parseInt(percentage),
                                message: message.trim(),
                                timestamp: new Date().toISOString()
                            });
                        }
                    }

                    // Send general log updates
                    event.sender.send('enhanced-log-update', {
                        message: line.trim(),
                        timestamp: new Date().toISOString(),
                        type: 'info'
                    });
                }
            });
        });
        
        // Handle stderr
        pythonProcess.stderr.on('data', (data) => {
            const error = data.toString();
            errorData += error;

            // Filter out dictionary creation messages (these are informational, not errors)
            const isDictionaryMessage = error.includes('Dictionary file not found. Creating new one.') ||
                                      error.includes('Creating missing section:') ||
                                      error.includes('Dictionary saved successfully');

            if (!isDictionaryMessage) {
                // Send error updates to renderer (only real errors)
                event.sender.send('enhanced-log-update', {
                    message: error.trim(),
                    timestamp: new Date().toISOString(),
                    type: 'error'
                });
            } else {
                // Send dictionary messages as info, not errors
                event.sender.send('enhanced-log-update', {
                    message: error.trim(),
                    timestamp: new Date().toISOString(),
                    type: 'info'
                });
            }
        });
        
        // Handle process completion
        pythonProcess.on('close', (code) => {
            if (processTimeout) {
                clearTimeout(processTimeout);
            }
            
            console.log(`[ENHANCED-PYTHON] Process exited with code: ${code}`);
            
            if (code === 0) {
                try {
                    // Try to parse the last JSON output
                    const lines = outputData.trim().split('\n');
                    let result = null;
                    
                    // Look for JSON result (usually the last substantial line)
                    for (let i = lines.length - 1; i >= 0; i--) {
                        const line = lines[i].trim();
                        if (line.startsWith('{') && line.endsWith('}')) {
                            try {
                                result = JSON.parse(line);
                                break;
                            } catch (e) {
                                // Continue looking
                            }
                        }
                    }
                    
                    if (result) {
                        resolve(result);
                    } else {
                        // Fallback: return raw output
                        resolve({
                            success: true,
                            output: outputData,
                            process_type: 'ENHANCED_PROCESS'
                        });
                    }
                    
                } catch (error) {
                    reject(new Error(`Failed to parse result: ${error.message}`));
                }
            } else {
                reject(new Error(`Process failed with code ${code}: ${errorData}`));
            }
        });
        
        // Handle process errors
        pythonProcess.on('error', (error) => {
            if (processTimeout) {
                clearTimeout(processTimeout);
            }
            
            console.error('[ENHANCED-PYTHON] Process error:', error);
            reject(error);
        });
    });
}

/**
 * Enhanced progress tracking utilities
 */
function sendPhaseUpdate(event, phase, status, progress = 0, message = '') {
    event.sender.send('enhanced-phase-update', {
        phase: phase,
        status: status, // 'pending', 'active', 'completed', 'error'
        progress: progress,
        message: message,
        timestamp: new Date().toISOString()
    });
}

function sendTrackerUpdate(event, component, status, details = {}) {
    event.sender.send('enhanced-tracker-update', {
        component: component, // 'loans', 'vehicles', 'database'
        status: status, // 'pending', 'processing', 'completed', 'error'
        details: details,
        timestamp: new Date().toISOString()
    });
}

function sendLearningUpdate(event, component, status, details = {}) {
    event.sender.send('enhanced-learning-update', {
        component: component, // 'dictionary', 'new_items', 'analysis'
        status: status, // 'pending', 'processing', 'completed', 'error'
        details: details,
        timestamp: new Date().toISOString()
    });
}

/**
 * Export handlers for integration with main.js
 */
module.exports = {
    // Main handlers
    'enhanced-payroll-audit': ipcMain.handle,
    'feed-tracker-tables': ipcMain.handle,
    'process-auto-learning': ipcMain.handle,
    'get-enhanced-process-status': ipcMain.handle,
    'initialize-content-switching': ipcMain.handle,
    
    // Utility functions
    runEnhancedPythonScript,
    sendPhaseUpdate,
    sendTrackerUpdate,
    sendLearningUpdate
};

console.log('✅ Enhanced IPC handlers loaded');
