import sqlite3
import os

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

class SessionManager:
    """Centralized session management for payroll audit system"""
    
    def __init__(self):
        self.db_path = get_database_path()
        if not self.db_path:
            raise Exception("Database not found")
    
    def get_current_session_id(self):
        """Get the current active session ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
            result = cursor.fetchone()
            
            if result:
                return result[0]
            else:
                raise Exception("No current session set")
        finally:
            conn.close()
    
    def set_current_session(self, session_id, session_name=None):
        """Set a session as the current active session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO current_session 
                (id, session_id, session_name, status, updated_at)
                VALUES (1, ?, ?, 'ACTIVE', datetime('now'))
            """, (session_id, session_name or f"Session_{session_id[-8:]}"))
            
            conn.commit()
        finally:
            conn.close()
    
    def get_session_status(self, session_id=None):
        """Get status of current or specified session"""
        if not session_id:
            session_id = self.get_current_session_id()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                SELECT phase_name, status, data_count, completed_at
                FROM session_phases 
                WHERE session_id = ?
                ORDER BY 
                    CASE phase_name
                        WHEN 'EXTRACTION' THEN 1
                        WHEN 'COMPARISON' THEN 2
                        WHEN 'PRE_REPORTING' THEN 3
                        WHEN 'TRACKER_FEEDING' THEN 4
                        WHEN 'AUTO_LEARNING' THEN 5
                        WHEN 'REPORT_GENERATION' THEN 6
                        ELSE 7
                    END
            """, (session_id,))
            
            phases = cursor.fetchall()
            
            return {
                'session_id': session_id,
                'phases': [
                    {
                        'name': row[0],
                        'status': row[1],
                        'data_count': row[2],
                        'completed_at': row[3]
                    }
                    for row in phases
                ]
            }
        finally:
            conn.close()
    
    def update_phase_status(self, phase_name, status, data_count=0, error_message=None):
        """Update the status of a phase for current session"""
        session_id = self.get_current_session_id()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            if status == 'COMPLETED':
                cursor.execute("""
                    UPDATE session_phases 
                    SET status = ?, data_count = ?, completed_at = datetime('now'), error_message = ?
                    WHERE session_id = ? AND phase_name = ?
                """, (status, data_count, error_message, session_id, phase_name))
            elif status == 'IN_PROGRESS':
                cursor.execute("""
                    UPDATE session_phases 
                    SET status = ?, started_at = datetime('now'), error_message = ?
                    WHERE session_id = ? AND phase_name = ?
                """, (status, error_message, session_id, phase_name))
            else:
                cursor.execute("""
                    UPDATE session_phases 
                    SET status = ?, error_message = ?
                    WHERE session_id = ? AND phase_name = ?
                """, (status, error_message, session_id, phase_name))
            
            conn.commit()
        finally:
            conn.close()

# Global session manager instance
_session_manager = None

def get_session_manager():
    """Get global session manager instance"""
    global _session_manager
    if _session_manager is None:
        _session_manager = SessionManager()
    return _session_manager

def get_current_session_id():
    """Get current session ID - use this in all phases"""
    return get_session_manager().get_current_session_id()
