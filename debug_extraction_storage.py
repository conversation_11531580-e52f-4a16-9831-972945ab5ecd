#!/usr/bin/env python3
"""
Debug script to check what's actually being stored during extraction
"""

import sqlite3
import os
import sys

def debug_extraction_storage():
    """Debug what's being stored in the database during extraction"""
    
    print("🔍 DEBUGGING EXTRACTION STORAGE")
    print("=" * 60)
    
    # Database path - check both possible locations
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",  # PythonDatabaseManager default
        r"C:\THE PAYROLL AUDITOR\core\payroll_audit.db",           # Alternative location
        r"C:\THE PAYROLL AUDITOR\payroll_audit.db"                 # Root location
    ]

    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            print(f"📁 Found database at: {path}")
            break

    if not db_path:
        print("❌ Database file not found in any of these locations:")
        for path in db_paths:
            print(f"   - {path}")
        print("\n🔍 Let's check what files exist in the data directory:")
        data_dir = r"C:\THE PAYROLL AUDITOR\data"
        if os.path.exists(data_dir):
            files = os.listdir(data_dir)
            print(f"   Files in {data_dir}: {files}")
        else:
            print(f"   Data directory doesn't exist: {data_dir}")
        return

    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get the latest session
    cursor.execute("""
        SELECT session_id, current_pdf_path, previous_pdf_path, created_at 
        FROM audit_sessions 
        ORDER BY created_at DESC 
        LIMIT 1
    """)
    
    session_data = cursor.fetchone()
    if not session_data:
        print("❌ No sessions found in database")
        conn.close()
        return
    
    session_id, current_pdf, previous_pdf, created_at = session_data
    print(f"📋 Latest Session: {session_id}")
    print(f"📄 Current PDF: {current_pdf}")
    print(f"📄 Previous PDF: {previous_pdf}")
    print(f"⏰ Created: {created_at}")
    print()
    
    # Check extracted data
    print("🔍 CHECKING EXTRACTED DATA:")
    print("-" * 40)
    
    cursor.execute("""
        SELECT period_type, COUNT(*) as employee_count
        FROM extracted_data 
        WHERE session_id = ?
        GROUP BY period_type
    """, (session_id,))
    
    period_counts = cursor.fetchall()
    
    if not period_counts:
        print("❌ NO EXTRACTED DATA FOUND!")
        print("   This means the extraction phase is not storing data properly")
        conn.close()
        return
    
    for period_type, count in period_counts:
        print(f"   {period_type.upper()}: {count} employee records")
    
    print()
    
    # Check sample data structure
    print("🔍 SAMPLE DATA STRUCTURE:")
    print("-" * 40)
    
    cursor.execute("""
        SELECT employee_id, employee_name, section_name, item_label, item_value
        FROM extracted_data 
        WHERE session_id = ? 
        LIMIT 10
    """, (session_id,))
    
    sample_data = cursor.fetchall()
    
    if sample_data:
        for row in sample_data:
            employee_id, employee_name, section_name, item_label, item_value = row
            print(f"   Employee: {employee_id} ({employee_name})")
            print(f"   Section: {section_name}")
            print(f"   Item: {item_label} = {item_value}")
            print()
    else:
        print("   ❌ No sample data found")
    
    # Check unique employees per period
    print("🔍 UNIQUE EMPLOYEES PER PERIOD:")
    print("-" * 40)
    
    cursor.execute("""
        SELECT period_type, COUNT(DISTINCT employee_id) as unique_employees
        FROM extracted_data 
        WHERE session_id = ?
        GROUP BY period_type
    """, (session_id,))
    
    unique_counts = cursor.fetchall()
    
    for period_type, count in unique_counts:
        print(f"   {period_type.upper()}: {count} unique employees")
    
    print()
    
    # Check comparison results
    print("🔍 CHECKING COMPARISON RESULTS:")
    print("-" * 40)
    
    cursor.execute("""
        SELECT COUNT(*) as comparison_count
        FROM comparison_results 
        WHERE session_id = ?
    """, (session_id,))
    
    comparison_count = cursor.fetchone()[0]
    print(f"   Comparison Results: {comparison_count} records")
    
    if comparison_count == 0:
        print("   ❌ NO COMPARISON RESULTS!")
        print("   This means the comparison phase is not working properly")
        
        # Let's check what the _load_extracted_data method would return
        print("\n🔍 TESTING _load_extracted_data METHOD:")
        print("-" * 40)
        
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
            FROM extracted_data
            WHERE session_id = ? AND period_type = 'current'
            ORDER BY employee_id, section_name, item_label
            LIMIT 5
        """, (session_id,))
        
        current_sample = cursor.fetchall()
        print(f"   Current data sample: {len(current_sample)} records")
        
        if current_sample:
            print("   Sample current data:")
            for row in current_sample:
                print(f"     {row}")
        
        cursor.execute("""
            SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
            FROM extracted_data
            WHERE session_id = ? AND period_type = 'previous'
            ORDER BY employee_id, section_name, item_label
            LIMIT 5
        """, (session_id,))
        
        previous_sample = cursor.fetchall()
        print(f"   Previous data sample: {len(previous_sample)} records")
        
        if previous_sample:
            print("   Sample previous data:")
            for row in previous_sample:
                print(f"     {row}")
    
    conn.close()
    
    print("\n" + "=" * 60)
    print("🎯 DIAGNOSIS COMPLETE")

if __name__ == "__main__":
    debug_extraction_storage()
