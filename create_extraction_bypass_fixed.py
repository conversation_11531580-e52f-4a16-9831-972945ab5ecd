#!/usr/bin/env python3
"""
Create extraction bypass with correct schema
"""

import sqlite3

def create_extraction_bypass_fixed():
    """Create extraction bypass with correct table schema"""
    print("🚀 CREATING EXTRACTION BYPASS (FIXED SCHEMA)")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"Current session: {current_session}")
        
        # Clear existing extracted data
        cursor.execute('DELETE FROM extracted_data WHERE session_id = ?', (current_session,))
        print("Cleared existing extracted data")
        
        # Create comprehensive test data with correct schema
        test_data = []
        
        # Create 10 employees with varied data
        for i in range(1, 11):
            emp_id = f"E{i:03d}"
            emp_name = f"Employee {i:02d}"
            department = f"DEPT_{i:02d}"
            
            # Current month data - multiple items per employee
            current_basic = 5000 + (i * 100)
            current_overtime = i * 50
            current_tax = current_basic * 0.15
            current_pension = current_basic * 0.05
            
            # Previous month data - with variations
            previous_basic = current_basic - (i * 10)  # Slight increase from previous
            previous_overtime = current_overtime - 25
            previous_tax = previous_basic * 0.15
            previous_pension = previous_basic * 0.05
            
            # Current month items
            current_items = [
                (emp_id, emp_name, department, 'EARNINGS', 'BASIC SALARY', f'{current_basic:.2f}', current_basic, 'current'),
                (emp_id, emp_name, department, 'EARNINGS', 'OVERTIME', f'{current_overtime:.2f}', current_overtime, 'current'),
                (emp_id, emp_name, department, 'DEDUCTIONS', 'INCOME TAX', f'{current_tax:.2f}', current_tax, 'current'),
                (emp_id, emp_name, department, 'DEDUCTIONS', 'PENSION', f'{current_pension:.2f}', current_pension, 'current'),
                (emp_id, emp_name, department, 'PERSONAL DETAILS', 'DEPARTMENT', department, 0, 'current'),
                (emp_id, emp_name, department, 'PERSONAL DETAILS', 'POSITION', f'POSITION_{i:02d}', 0, 'current'),
            ]
            
            # Previous month items
            previous_items = [
                (emp_id, emp_name, department, 'EARNINGS', 'BASIC SALARY', f'{previous_basic:.2f}', previous_basic, 'previous'),
                (emp_id, emp_name, department, 'EARNINGS', 'OVERTIME', f'{previous_overtime:.2f}', previous_overtime, 'previous'),
                (emp_id, emp_name, department, 'DEDUCTIONS', 'INCOME TAX', f'{previous_tax:.2f}', previous_tax, 'previous'),
                (emp_id, emp_name, department, 'DEDUCTIONS', 'PENSION', f'{previous_pension:.2f}', previous_pension, 'previous'),
                (emp_id, emp_name, department, 'PERSONAL DETAILS', 'DEPARTMENT', department, 0, 'previous'),
                (emp_id, emp_name, department, 'PERSONAL DETAILS', 'POSITION', f'POSITION_{i:02d}', 0, 'previous'),
            ]
            
            test_data.extend(current_items)
            test_data.extend(previous_items)
        
        # Insert test data
        for item in test_data:
            employee_id, employee_name, dept, section_name, item_label, item_value, numeric_value, period_type = item
            
            cursor.execute('''
                INSERT INTO extracted_data 
                (session_id, employee_id, employee_name, department, section_name, item_label, item_value, numeric_value, period_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (current_session, employee_id, employee_name, dept, section_name, item_label, item_value, numeric_value, period_type))
        
        conn.commit()
        
        # Update session status
        cursor.execute('UPDATE audit_sessions SET status = ? WHERE session_id = ?', ('extraction_complete', current_session))
        
        # Update phase status
        cursor.execute('''
            INSERT OR REPLACE INTO session_phases 
            (session_id, phase_name, status, data_count)
            VALUES (?, ?, ?, ?)
        ''', (current_session, 'EXTRACTION', 'COMPLETED', len(test_data)))
        
        conn.commit()
        
        # Verify the data
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
        total_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(DISTINCT employee_id) FROM extracted_data WHERE session_id = ?', (current_session,))
        employee_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = ?', (current_session, 'current'))
        current_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = ?', (current_session, 'previous'))
        previous_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"✅ EXTRACTION BYPASS CREATED SUCCESSFULLY!")
        print(f"   Total records: {total_count}")
        print(f"   Unique employees: {employee_count}")
        print(f"   Current month records: {current_count}")
        print(f"   Previous month records: {previous_count}")
        print(f"   Session {current_session} marked as extraction complete")
        
        print(f"\n🎯 DATA STRUCTURE:")
        print(f"   • 10 employees with realistic payroll data")
        print(f"   • Each employee has 6 items (BASIC SALARY, OVERTIME, INCOME TAX, PENSION, DEPARTMENT, POSITION)")
        print(f"   • Both current and previous month data for comparison")
        print(f"   • Salary variations to generate meaningful comparison results")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. Run the audit process again")
        print(f"   2. It should skip extraction and go directly to comparison")
        print(f"   3. The comparison phase should generate meaningful results")
        print(f"   4. Test the full workflow without extraction delays")
        
        return True
        
    except Exception as e:
        print(f"❌ Bypass creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_extraction_bypass_fixed()
    
    if success:
        print("\n🎉 EXTRACTION BYPASS COMPLETE!")
        print("   The system is now ready for testing without extraction freeze issues.")
    else:
        print("\n⚠️ EXTRACTION BYPASS FAILED")
        print("   Please check the error messages above.")
