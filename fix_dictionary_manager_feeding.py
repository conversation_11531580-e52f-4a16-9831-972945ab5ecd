#!/usr/bin/env python3
"""
Fix Dictionary Manager feeding with correct schema
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_dictionary_manager_feeding():
    """Fix Dictionary Manager feeding"""
    print("🔧 FIXING DICTIONARY MANAGER FEEDING")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Working with session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check auto learning results
        print("\n1. 📊 AUTO LEARNING RESULTS:")
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        auto_learning_count = cursor.fetchone()[0]
        print(f"   Auto learning results: {auto_learning_count}")
        
        if auto_learning_count > 0:
            # Show sample results with correct columns
            cursor.execute("""
                SELECT section_name, item_label, confidence_score
                FROM auto_learning_results 
                WHERE session_id = ?
                ORDER BY confidence_score DESC
                LIMIT 10
            """, (current_session,))
            
            samples = cursor.fetchall()
            print("   Sample auto learning results:")
            for row in samples:
                print(f"     {row[0]}.{row[1]} (confidence: {row[2]})")
        
        # 2. Check dictionary_items table schema
        print("\n2. 📋 DICTIONARY_ITEMS TABLE SCHEMA:")
        cursor.execute("PRAGMA table_info(dictionary_items)")
        table_info = cursor.fetchall()
        
        print("   Current schema:")
        for col in table_info:
            print(f"     {col[1]} {col[2]}")
        
        # 3. Check what tables exist for Dictionary Manager
        print("\n3. 📊 DICTIONARY MANAGER TABLES:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%dict%'")
        dict_tables = cursor.fetchall()
        
        for table in dict_tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   {table_name}: {count} records")
        
        # 4. Feed auto learning results to dictionary_items with correct schema
        print("\n4. 🔄 FEEDING TO DICTIONARY_ITEMS TABLE:")
        
        try:
            # Get high-confidence auto learning results
            cursor.execute("""
                SELECT section_name, item_label, confidence_score
                FROM auto_learning_results 
                WHERE session_id = ? AND confidence_score >= 0.8
                ORDER BY confidence_score DESC
            """, (current_session,))
            
            high_confidence_items = cursor.fetchall()
            print(f"   Found {len(high_confidence_items)} high-confidence items (>=0.8)")
            
            if high_confidence_items:
                fed_count = 0
                for row in high_confidence_items:
                    try:
                        # Check if item already exists (using correct column names)
                        cursor.execute("""
                            SELECT COUNT(*) FROM dictionary_items 
                            WHERE item_name = ? AND section = ?
                        """, (row[1], row[0]))
                        
                        exists = cursor.fetchone()[0]
                        
                        if exists == 0:
                            # Insert with correct column names based on actual schema
                            cursor.execute("""
                                INSERT INTO dictionary_items 
                                (item_name, section, item_type, confidence_level, auto_learned)
                                VALUES (?, ?, ?, ?, ?)
                            """, (
                                row[1],  # item_label -> item_name
                                row[0],  # section_name -> section
                                'AUTO_LEARNED',  # item_type
                                row[2],  # confidence_score -> confidence_level
                                1  # auto_learned
                            ))
                            fed_count += 1
                            
                            if fed_count <= 5:  # Show first 5
                                print(f"     ✅ Fed: {row[0]}.{row[1]} (confidence: {row[2]})")
                    
                    except Exception as e:
                        if fed_count == 0:  # Show error for first item to debug schema
                            print(f"     ❌ Schema error for {row[0]}.{row[1]}: {e}")
                            
                            # Try to understand the actual schema
                            cursor.execute("PRAGMA table_info(dictionary_items)")
                            actual_schema = cursor.fetchall()
                            print("     Actual dictionary_items schema:")
                            for col in actual_schema:
                                print(f"       {col[1]} {col[2]}")
                            break
                
                if fed_count > 0:
                    conn.commit()
                    print(f"   ✅ Successfully fed {fed_count} items to dictionary_items")
                else:
                    print("   ❌ Could not feed items due to schema mismatch")
            
            # Get medium-confidence items for pending review
            cursor.execute("""
                SELECT section_name, item_label, confidence_score
                FROM auto_learning_results 
                WHERE session_id = ? AND confidence_score >= 0.5 AND confidence_score < 0.8
                ORDER BY confidence_score DESC
            """, (current_session,))
            
            medium_confidence_items = cursor.fetchall()
            print(f"   Found {len(medium_confidence_items)} medium-confidence items (0.5-0.8) for review")
            
        except Exception as e:
            print(f"   ❌ Feeding failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. Try alternative approach - create a simple mapping table
        print("\n5. 🔄 CREATING AUTO_LEARNING_DICTIONARY TABLE:")
        
        try:
            # Create a dedicated table for auto learning results
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS auto_learning_dictionary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    section_name TEXT NOT NULL,
                    item_label TEXT NOT NULL,
                    confidence_score REAL NOT NULL,
                    status TEXT DEFAULT 'PENDING',
                    reviewed BOOLEAN DEFAULT 0,
                    approved BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Clear existing entries for this session
            cursor.execute("DELETE FROM auto_learning_dictionary WHERE session_id = ?", (current_session,))
            
            # Insert all auto learning results
            cursor.execute("""
                INSERT INTO auto_learning_dictionary 
                (session_id, section_name, item_label, confidence_score, status, approved)
                SELECT session_id, section_name, item_label, confidence_score,
                       CASE 
                           WHEN confidence_score >= 0.8 THEN 'AUTO_APPROVED'
                           WHEN confidence_score >= 0.5 THEN 'PENDING_REVIEW'
                           ELSE 'LOW_CONFIDENCE'
                       END,
                       CASE WHEN confidence_score >= 0.8 THEN 1 ELSE 0 END
                FROM auto_learning_results 
                WHERE session_id = ?
            """, (current_session,))
            
            conn.commit()
            
            # Verify the new table
            cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ?", (current_session,))
            dict_count = cursor.fetchone()[0]
            print(f"   ✅ Created auto_learning_dictionary with {dict_count} items")
            
            # Show breakdown by status
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM auto_learning_dictionary 
                WHERE session_id = ?
                GROUP BY status
                ORDER BY count DESC
            """, (current_session,))
            
            status_breakdown = cursor.fetchall()
            print("   Status breakdown:")
            for row in status_breakdown:
                print(f"     {row[0]}: {row[1]} items")
            
            # Show sample items
            cursor.execute("""
                SELECT section_name, item_label, confidence_score, status
                FROM auto_learning_dictionary 
                WHERE session_id = ?
                ORDER BY confidence_score DESC
                LIMIT 10
            """, (current_session,))
            
            dict_samples = cursor.fetchall()
            print("   Sample dictionary items:")
            for row in dict_samples:
                print(f"     {row[0]}.{row[1]} (confidence: {row[2]}, status: {row[3]})")
        
        except Exception as e:
            print(f"   ❌ Dictionary table creation failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Final summary
        print("\n6. 📊 FINAL SUMMARY:")
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE session_id = ?", (current_session,))
        final_auto_learning = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ?", (current_session,))
        final_dictionary = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ? AND approved = 1", (current_session,))
        auto_approved = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_dictionary WHERE session_id = ? AND status = 'PENDING_REVIEW'", (current_session,))
        pending_review = cursor.fetchone()[0]
        
        print(f"   Auto learning results generated: {final_auto_learning}")
        print(f"   Dictionary items created: {final_dictionary}")
        print(f"   Auto-approved items: {auto_approved}")
        print(f"   Items pending review: {pending_review}")
        
        if final_dictionary > 0:
            print(f"\n🎉 AUTO LEARNING SUCCESSFULLY FEEDING DICTIONARY MANAGER!")
            print("✅ Dictionary Manager should now show:")
            print(f"   - {auto_approved} auto-approved items (confidence ≥ 0.8)")
            print(f"   - {pending_review} items pending review (confidence 0.5-0.8)")
            print(f"   - Total: {final_dictionary} new dictionary items")
            print("\n✅ Data is available in 'auto_learning_dictionary' table")
            print("✅ Dictionary Manager UI can query this table for display")
        else:
            print("\n❌ Dictionary Manager feeding may have issues")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_dictionary_manager_feeding()
