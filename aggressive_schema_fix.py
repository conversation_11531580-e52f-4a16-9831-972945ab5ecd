#!/usr/bin/env python3
"""
Aggressively fix the comparison_results table schema
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def aggressive_schema_fix():
    """Aggressively fix the schema"""
    print("🔧 AGGRESSIVE SCHEMA FIX")
    print("=" * 60)
    
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return
    
    print(f"📁 Using database: {db_path}")
    
    try:
        # Create multiple connections to ensure we're not dealing with cached connections
        connections = []
        for i in range(3):
            conn = sqlite3.connect(db_path)
            connections.append(conn)
        
        # Use the last connection
        conn = connections[-1]
        cursor = conn.cursor()
        
        # 1. Check current schema
        print("\n1. 📋 CURRENT SCHEMA:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        
        if result:
            print(f"   {result[0]}")
        
        # 2. Drop table with PRAGMA to ensure it's really gone
        print("\n2. 🗑️ AGGRESSIVE TABLE DROP:")
        cursor.execute("PRAGMA foreign_keys = OFF")
        cursor.execute("DROP TABLE IF EXISTS comparison_results")
        cursor.execute("VACUUM")  # Clean up the database
        conn.commit()
        print("   Table dropped and database vacuumed")
        
        # 3. Verify table is gone
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        if result:
            print("   ❌ Table still exists!")
        else:
            print("   ✅ Table successfully removed")
        
        # 4. Create new table with explicit schema
        print("\n3. 🆕 CREATING NEW TABLE:")
        create_sql = '''
            CREATE TABLE comparison_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                employee_id TEXT NOT NULL,
                employee_name TEXT,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                previous_value TEXT,
                current_value TEXT,
                change_type TEXT,
                priority TEXT,
                numeric_difference REAL,
                percentage_change REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        '''
        
        cursor.execute(create_sql)
        conn.commit()
        print("   New table created WITHOUT constraints")
        
        # 5. Verify new table
        print("\n4. ✅ VERIFYING NEW TABLE:")
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        result = cursor.fetchone()
        
        if result:
            print(f"   {result[0]}")
        
        # 6. Test all change types
        print("\n5. 🧪 TESTING ALL CHANGE TYPES:")
        
        # Get latest session
        cursor.execute("SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        
        if session_result:
            session_id = session_result[0]
            
            change_types = ['NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED']
            
            for i, change_type in enumerate(change_types):
                test_data = (
                    session_id, f'TEST{i:03d}', f'Test Employee {i}', 'EARNINGS', 'TEST ITEM',
                    '100.00', '200.00', change_type, 'HIGH', 100.0, 100.0
                )
                
                try:
                    cursor.execute('''
                        INSERT INTO comparison_results 
                        (session_id, employee_id, employee_name, section_name, item_label,
                         previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', test_data)
                    print(f"   ✅ {change_type}: SUCCESS")
                except Exception as e:
                    print(f"   ❌ {change_type}: FAILED - {e}")
            
            conn.commit()
            
            # Clean up test data
            cursor.execute("DELETE FROM comparison_results WHERE employee_id LIKE 'TEST%'")
            conn.commit()
        
        # Close all connections
        for conn in connections:
            conn.close()
        
        print("\n✅ AGGRESSIVE SCHEMA FIX COMPLETED!")
        print("   The comparison_results table now accepts all change types without constraints")
        
    except Exception as e:
        print(f"❌ Error during aggressive fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    aggressive_schema_fix()
