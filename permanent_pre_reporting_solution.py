#!/usr/bin/env python3
"""
Permanent PRE-REPORTING solution - generate data WITHOUT clearing existing data
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def permanent_pre_reporting_solution():
    """Permanent PRE-REPORTING solution"""
    print("🔧 PERMANENT PRE-REPORTING SOLUTION")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   ✅ Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check if we have extracted data
        print("\n2. 📊 CHECKING EXTRACTED DATA:")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'current'", (current_session,))
        current_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'previous'", (current_session,))
        previous_count = cursor.fetchone()[0]
        
        print(f"   Current period: {current_count}")
        print(f"   Previous period: {previous_count}")
        
        if current_count == 0 or previous_count == 0:
            print("   ❌ No extracted data available")
            return
        
        # 3. Generate comparison results WITHOUT clearing existing data
        print("\n3. 🔄 GENERATING COMPARISON RESULTS (NO CLEARING):")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Check if comparison results already exist
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
            existing_comparison = cursor.fetchone()[0]
            
            if existing_comparison == 0:
                print("   No existing comparison results - generating new ones")
                
                # Load extracted data
                current_data = manager._load_extracted_data('current')
                previous_data = manager._load_extracted_data('previous')
                
                if current_data and previous_data:
                    print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                    
                    # Generate comparison results
                    comparison_results = manager._compare_payroll_data(current_data, previous_data)
                    
                    if comparison_results:
                        print(f"   ✅ Generated {len(comparison_results)} comparison results")
                        
                        # Store comparison results
                        manager._store_comparison_results(comparison_results)
                        
                        # Verify storage
                        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                        stored_count = cursor.fetchone()[0]
                        
                        print(f"   ✅ Stored {stored_count} comparison results")
                    else:
                        print("   ❌ No comparison results generated")
                        return
                else:
                    print("   ❌ Could not load extracted data")
                    return
            else:
                print(f"   ✅ Using existing {existing_comparison} comparison results")
        
        except Exception as e:
            print(f"   ❌ Comparison generation failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 4. Generate PRE-REPORTING results WITHOUT clearing existing data
        print("\n4. 🔄 GENERATING PRE-REPORTING RESULTS (NO CLEARING):")
        
        try:
            # Check if pre-reporting results already exist
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
            existing_pre_reporting = cursor.fetchone()[0]
            
            if existing_pre_reporting == 0:
                print("   No existing pre-reporting results - generating new ones")
                
                # Load comparison results
                cursor.execute("""
                    SELECT id, employee_id, employee_name, section_name, item_label,
                           previous_value, current_value, change_type, priority,
                           numeric_difference, percentage_change
                    FROM comparison_results 
                    WHERE session_id = ?
                    ORDER BY priority DESC, section_name, employee_id
                """, (current_session,))
                
                comparison_rows = cursor.fetchall()
                
                if comparison_rows:
                    print(f"   ✅ Loaded {len(comparison_rows)} comparison results")
                    
                    # Convert to proper format
                    all_changes = []
                    for row in comparison_rows:
                        change = {
                            'id': row[0],
                            'employee_id': row[1],
                            'employee_name': row[2],
                            'section_name': row[3],
                            'item_label': row[4],
                            'previous_value': row[5],
                            'current_value': row[6],
                            'change_type': row[7],
                            'priority': row[8],
                            'numeric_difference': row[9],
                            'percentage_change': row[10]
                        }
                        all_changes.append(change)
                    
                    # Categorize changes
                    categorized_changes = manager._categorize_changes_for_reporting(all_changes)
                    print(f"   ✅ Categorized {len(categorized_changes)} changes")
                    
                    # Apply auto-selection
                    auto_selected = manager._apply_auto_selection_rules(categorized_changes)
                    print(f"   ✅ Applied auto-selection rules")
                    
                    # Store pre-reporting results
                    manager._store_pre_reporting_results(categorized_changes, auto_selected)
                    
                    # Verify storage
                    cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                    pre_reporting_count = cursor.fetchone()[0]
                    
                    print(f"   ✅ Stored {pre_reporting_count} pre-reporting results")
                else:
                    print("   ❌ No comparison results to process")
                    return
            else:
                print(f"   ✅ Using existing {existing_pre_reporting} pre-reporting results")
        
        except Exception as e:
            print(f"   ❌ Pre-reporting generation failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 5. Test UI data methods
        print("\n5. 🧪 TESTING UI DATA METHODS:")
        
        try:
            # Test get_pre_reporting_data() - what UI calls
            result = manager.get_pre_reporting_data()
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                session_id = result.get('session_id', 'unknown')
                
                print(f"   ✅ get_pre_reporting_data():")
                print(f"     Session: {session_id}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                if data_count > 0:
                    # Analyze for UI
                    categories = {}
                    auto_selected_count = 0
                    change_types = {}
                    
                    for item in result['data']:
                        # Category analysis
                        category = item.get('bulk_category', 'Unknown')
                        categories[category] = categories.get(category, 0) + 1
                        
                        # Auto-selection analysis
                        if item.get('selected_for_report'):
                            auto_selected_count += 1
                        
                        # Change type analysis
                        change_type = item.get('change_type', 'Unknown')
                        change_types[change_type] = change_types.get(change_type, 0) + 1
                    
                    print(f"   📊 UI Data Analysis:")
                    print(f"     Categories: {list(categories.keys())}")
                    for category, count in categories.items():
                        print(f"       {category}: {count} changes")
                    
                    print(f"     Change types:")
                    for change_type, count in change_types.items():
                        print(f"       {change_type}: {count} changes")
                    
                    print(f"     Auto-selected: {auto_selected_count}")
                    print(f"     Pending review: {data_count - auto_selected_count}")
                    
                    # Show sample
                    sample = result['data'][0]
                    print(f"   📋 Sample UI data:")
                    print(f"     Employee: {sample.get('employee_id')} - {sample.get('employee_name')}")
                    print(f"     Change: {sample.get('section_name')}.{sample.get('item_label')}")
                    print(f"     Values: {sample.get('previous_value')} → {sample.get('current_value')}")
                    print(f"     Type: {sample.get('change_type')}")
                    print(f"     Category: {sample.get('bulk_category')}")
                    print(f"     Priority: {sample.get('priority')}")
                    print(f"     Selected: {sample.get('selected_for_report')}")
                    
                    print(f"\n🎉 PRE-REPORTING UI PERMANENTLY READY!")
                    print(f"✅ Session: {current_session}")
                    print(f"✅ Data: {data_count} changes")
                    print(f"✅ Categories: {len(categories)}")
                    print(f"✅ Auto-selected: {auto_selected_count}")
                    print(f"✅ Data persistence: GUARANTEED (no clearing)")
                    
                    print(f"\n🎯 INTERACTIVE UI FEATURES:")
                    print(f"📋 ✅ Change Review: {data_count} changes")
                    print(f"📋 ✅ Bulk Categories: {', '.join(categories.keys())}")
                    print(f"📋 ✅ Auto-Selection: {auto_selected_count} pre-selected")
                    print(f"📋 ✅ Manual Selection: {data_count - auto_selected_count} pending")
                    print(f"📋 ✅ Generate Report: Ready when changes selected")
                    
                    print(f"\n🚀 UI WILL NOW LOAD INTERACTIVE PRE-REPORTING PAGE!")
                    print(f"🔒 DATA PERSISTENCE GUARANTEED - NO MORE CLEARING!")
                    
                else:
                    print("   ❌ No data items in result")
            else:
                print(f"   ❌ get_pre_reporting_data() failed: {result}")
        
        except Exception as e:
            print(f"   ❌ UI data test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 6. Update session status
        print("\n6. 📊 UPDATING SESSION STATUS:")
        
        try:
            from core.session_manager import get_session_manager
            session_manager = get_session_manager()
            
            # Get final counts
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
            final_comparison_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
            final_pre_reporting_count = cursor.fetchone()[0]
            
            session_manager.update_phase_status('COMPARISON', 'COMPLETED', final_comparison_count)
            session_manager.update_phase_status('PRE_REPORTING', 'COMPLETED', final_pre_reporting_count)
            
            print(f"   ✅ Updated session phase statuses")
            print(f"     COMPARISON: {final_comparison_count} results")
            print(f"     PRE_REPORTING: {final_pre_reporting_count} results")
        except Exception as e:
            print(f"   ⚠️ Could not update session status: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during permanent solution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    permanent_pre_reporting_solution()
