#!/usr/bin/env python3
"""
Check what INCREASED and DECREASED items exist in comparison results
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_increased_decreased_items():
    """Check INCREASED and DECREASED items in comparison results"""
    print("🔍 CHECKING INCREASED AND DECREASED ITEMS")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Checking session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check all change types in comparison results
        print("\n1. 📊 ALL CHANGE TYPES IN COMPARISON RESULTS:")
        
        cursor.execute("""
            SELECT change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
            GROUP BY change_type
            ORDER BY count DESC
        """, (current_session,))
        
        change_types = cursor.fetchall()
        
        if change_types:
            print("   Change type breakdown:")
            for row in change_types:
                print(f"     {row[0]}: {row[1]} items")
        else:
            print("   ❌ No comparison results found")
            return
        
        # 2. Check INCREASED items specifically for loans and motor vehicles
        print("\n2. 📈 INCREASED ITEMS (LOANS AND MOTOR VEHICLES):")
        
        cursor.execute("""
            SELECT employee_id, section_name, item_label, previous_value, current_value
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'INCREASED'
            AND (
                (section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F%') OR
                (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
            )
            ORDER BY employee_id, section_name, item_label
            LIMIT 10
        """, (current_session,))
        
        increased_items = cursor.fetchall()
        
        if increased_items:
            print(f"   Found {len(increased_items)} INCREASED items:")
            for row in increased_items:
                emp_id, section, label, prev_val, curr_val = row
                print(f"     {emp_id} - {section}.{label}")
                print(f"       {prev_val} → {curr_val} (INCREASED)")
        else:
            print("   ❌ No INCREASED loan/motor vehicle items found")
        
        # 3. Check DECREASED items specifically for loans and motor vehicles
        print("\n3. 📉 DECREASED ITEMS (LOANS AND MOTOR VEHICLES):")
        
        cursor.execute("""
            SELECT employee_id, section_name, item_label, previous_value, current_value
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'DECREASED'
            AND (
                (section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F%') OR
                (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
            )
            ORDER BY employee_id, section_name, item_label
            LIMIT 10
        """, (current_session,))
        
        decreased_items = cursor.fetchall()
        
        if decreased_items:
            print(f"   Found {len(decreased_items)} DECREASED items:")
            for row in decreased_items:
                emp_id, section, label, prev_val, curr_val = row
                print(f"     {emp_id} - {section}.{label}")
                print(f"       {prev_val} → {curr_val} (DECREASED)")
        else:
            print("   ❌ No DECREASED loan/motor vehicle items found")
        
        # 4. Check total counts for tracking potential
        print("\n4. 📊 TRACKING POTENTIAL SUMMARY:")
        
        # Count all trackable items by change type
        cursor.execute("""
            SELECT change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
            AND (
                (section_name = 'LOANS' AND item_label LIKE '%BALANCE B/F%') OR
                (item_label LIKE '%MOTOR VEH%' OR item_label LIKE '%VEHICLE MAINT%')
            )
            GROUP BY change_type
            ORDER BY count DESC
        """, (current_session,))
        
        trackable_counts = cursor.fetchall()
        
        if trackable_counts:
            print("   Trackable items by change type:")
            total_trackable = 0
            for row in trackable_counts:
                print(f"     {row[0]}: {row[1]} items")
                total_trackable += row[1]
            
            print(f"   Total trackable items: {total_trackable}")
            
            # Current implementation only tracks NEW
            new_count = next((row[1] for row in trackable_counts if row[0] == 'NEW'), 0)
            missed_count = total_trackable - new_count
            
            if missed_count > 0:
                print(f"   ⚠️ Currently missing {missed_count} INCREASED/DECREASED items!")
                print(f"   📊 Current tracking: {new_count} NEW items only")
                print(f"   📊 Potential tracking: {total_trackable} total items")
        
        # 5. Show sample INCREASED/DECREASED items for context
        print("\n5. 📋 SAMPLE INCREASED/DECREASED ITEMS:")
        
        # Sample INCREASED
        cursor.execute("""
            SELECT employee_id, section_name, item_label, previous_value, current_value
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'INCREASED'
            LIMIT 5
        """, (current_session,))
        
        sample_increased = cursor.fetchall()
        
        if sample_increased:
            print("   Sample INCREASED items (any section):")
            for row in sample_increased:
                emp_id, section, label, prev_val, curr_val = row
                print(f"     {emp_id} - {section}.{label}: {prev_val} → {curr_val}")
        
        # Sample DECREASED
        cursor.execute("""
            SELECT employee_id, section_name, item_label, previous_value, current_value
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'DECREASED'
            LIMIT 5
        """, (current_session,))
        
        sample_decreased = cursor.fetchall()
        
        if sample_decreased:
            print("   Sample DECREASED items (any section):")
            for row in sample_decreased:
                emp_id, section, label, prev_val, curr_val = row
                print(f"     {emp_id} - {section}.{label}: {prev_val} → {curr_val}")
        
        # 6. Recommendation
        print("\n6. 💡 RECOMMENDATION:")
        
        total_new = next((row[1] for row in trackable_counts if row[0] == 'NEW'), 0)
        total_increased = next((row[1] for row in trackable_counts if row[0] == 'INCREASED'), 0)
        total_decreased = next((row[1] for row in trackable_counts if row[0] == 'DECREASED'), 0)
        
        print(f"   Current Bank Adviser tracking: {total_new} NEW items only")
        
        if total_increased > 0 or total_decreased > 0:
            print(f"   Potential expansion:")
            print(f"     + {total_increased} INCREASED items")
            print(f"     + {total_decreased} DECREASED items")
            print(f"     = {total_new + total_increased + total_decreased} total trackable items")
            print("\n   💡 Consider expanding Bank Adviser to track INCREASED/DECREASED items")
            print("      for comprehensive loan and allowance monitoring!")
        else:
            print("   ✅ No INCREASED/DECREASED items found - NEW tracking is complete")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during check: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_increased_decreased_items()
