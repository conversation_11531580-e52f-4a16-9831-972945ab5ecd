#!/usr/bin/env python3
"""
Fix the Python database manager caching issue
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_python_db_cache():
    """Fix the Python database manager caching issue"""
    print("🔧 FIXING PYTHON DATABASE MANAGER CACHING ISSUE")
    print("=" * 60)
    
    try:
        # Import the manager
        sys.path.append(os.path.dirname(__file__))
        from core.python_database_manager import DatabaseManager
        
        # Get database path
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        print(f"📁 Using database: {db_path}")
        
        # 1. Create a fresh database manager instance
        print("\n1. 🔄 CREATING FRESH DATABASE MANAGER:")
        db_manager = DatabaseManager()
        
        # Force close any existing connections
        if hasattr(db_manager, 'connection') and db_manager.connection:
            db_manager.connection.close()
            print("   Closed existing connection")
        
        # Force reconnect
        db_manager.connect()
        print("   Created fresh connection")
        
        # 2. Test the connection with the new schema
        print("\n2. 🧪 TESTING NEW CONNECTION:")
        
        # Get latest session
        sessions = db_manager.execute_query(
            "SELECT session_id FROM audit_sessions ORDER BY created_at DESC LIMIT 1"
        )
        
        if not sessions:
            print("   ❌ No audit sessions found")
            return
        
        session_id = sessions[0][0] if isinstance(sessions[0], tuple) else sessions[0]['session_id']
        print(f"   Using session: {session_id}")
        
        # Test inserting all change types
        change_types = ['NEW', 'REMOVED', 'INCREASED', 'DECREASED', 'CHANGED']
        
        for i, change_type in enumerate(change_types):
            try:
                db_manager.execute_update(
                    '''INSERT INTO comparison_results 
                       (session_id, employee_id, employee_name, section_name, item_label,
                        previous_value, current_value, change_type, priority, numeric_difference, percentage_change)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                    (session_id, f'TEST{i:03d}', f'Test Employee {i}', 'EARNINGS', 'TEST ITEM',
                     '100.00', '200.00', change_type, 'HIGH', 100.0, 100.0)
                )
                print(f"   ✅ {change_type}: SUCCESS")
            except Exception as e:
                print(f"   ❌ {change_type}: FAILED - {e}")
        
        # Clean up test data
        db_manager.execute_update("DELETE FROM comparison_results WHERE employee_id LIKE 'TEST%'")
        
        # 3. Test the actual comparison storage
        print("\n3. 🔄 TESTING ACTUAL COMPARISON STORAGE:")
        
        # Create a test comparison result
        test_result = {
            'employee_id': 'REAL_TEST',
            'employee_name': 'Real Test Employee',
            'section_name': 'EARNINGS',
            'item_label': 'BASIC SALARY',
            'previous_value': '1000.00',
            'current_value': '1100.00',
            'change_type': 'CHANGED',
            'priority': 'HIGH',
            'numeric_difference': 100.0,
            'percentage_change': 10.0
        }
        
        try:
            db_manager.execute_update(
                '''INSERT INTO comparison_results
                   (session_id, employee_id, employee_name, section_name, item_label,
                    previous_value, current_value, change_type, priority,
                    numeric_difference, percentage_change)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                (session_id, test_result['employee_id'], test_result['employee_name'],
                 test_result['section_name'], test_result['item_label'], test_result['previous_value'],
                 test_result['current_value'], test_result['change_type'], test_result['priority'],
                 test_result['numeric_difference'], test_result['percentage_change'])
            )
            print("   ✅ Real comparison result stored successfully!")
            
            # Clean up
            db_manager.execute_update("DELETE FROM comparison_results WHERE employee_id = 'REAL_TEST'")
            
        except Exception as e:
            print(f"   ❌ Failed to store real comparison result: {e}")
        
        # 4. Test with the actual phased process manager
        print("\n4. 🔄 TESTING WITH PHASED PROCESS MANAGER:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            # Create manager and set session
            manager = PhasedProcessManager()
            manager.session_id = session_id
            
            # Force the manager to use a fresh database connection
            if hasattr(manager, 'db_manager'):
                if hasattr(manager.db_manager, 'connection') and manager.db_manager.connection:
                    manager.db_manager.connection.close()
                manager.db_manager.connect()
            
            # Test storing a single comparison result
            test_results = [{
                'employee_id': 'MANAGER_TEST',
                'employee_name': 'Manager Test Employee',
                'section_name': 'EARNINGS',
                'item_label': 'BASIC SALARY',
                'previous_value': '1000.00',
                'current_value': '1100.00',
                'change_type': 'CHANGED',
                'priority': 'HIGH',
                'numeric_difference': 100.0,
                'percentage_change': 10.0
            }]
            
            manager._store_comparison_results(test_results)
            print("   ✅ Phased process manager storage working!")
            
            # Clean up
            manager.db_manager.execute_update("DELETE FROM comparison_results WHERE employee_id = 'MANAGER_TEST'")
            
        except Exception as e:
            print(f"   ❌ Phased process manager test failed: {e}")
            import traceback
            traceback.print_exc()
        
        db_manager.close()
        
        print("\n✅ PYTHON DATABASE MANAGER CACHE FIX COMPLETED!")
        print("   The comparison storage should now work correctly")
        
    except Exception as e:
        print(f"❌ Error fixing Python DB cache: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_python_db_cache()
