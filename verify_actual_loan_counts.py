#!/usr/bin/env python3
"""
Verify actual loan counts in Bank Adviser tables and comparison results
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_actual_loan_counts():
    """Verify actual loan counts"""
    print("🔍 VERIFYING ACTUAL LOAN COUNTS")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Checking session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check actual Bank Adviser table counts
        print("\n1. 📊 ACTUAL BANK ADVISER TABLE COUNTS:")
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        in_house_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        external_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        motor_count = cursor.fetchone()[0]
        
        total_tracked = in_house_count + external_count + motor_count
        total_loans = in_house_count + external_count
        
        print(f"   In-house loans: {in_house_count}")
        print(f"   External loans: {external_count}")
        print(f"   Total loans tracked: {total_loans}")
        print(f"   Motor vehicle allowances: {motor_count}")
        print(f"   Total all items tracked: {total_tracked}")
        
        # 2. Check NEW loan items in comparison results
        print("\n2. 📊 NEW LOAN ITEMS IN COMPARISON RESULTS:")
        
        cursor.execute("""
            SELECT COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
        """, (current_session,))
        
        total_new_loans = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) 
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
            AND item_label LIKE '%BALANCE B/F%'
        """, (current_session,))
        
        new_balance_bf = cursor.fetchone()[0]
        
        print(f"   Total NEW loan items (all types): {total_new_loans}")
        print(f"   NEW Balance B/F items: {new_balance_bf}")
        
        # 3. Show breakdown of NEW loan items by type
        print("\n3. 📋 BREAKDOWN OF NEW LOAN ITEMS:")
        
        cursor.execute("""
            SELECT item_label, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
            GROUP BY item_label
            ORDER BY count DESC
        """, (current_session,))
        
        loan_breakdown = cursor.fetchall()
        
        if loan_breakdown:
            print("   NEW loan items by type:")
            for row in loan_breakdown:
                print(f"     {row[0]}: {row[1]} items")
        
        # 4. Check if we're missing loan items in tracking
        print("\n4. 🔍 CHECKING FOR MISSING LOAN ITEMS:")
        
        if new_balance_bf > total_loans:
            missing_loans = new_balance_bf - total_loans
            print(f"   ⚠️ MISSING {missing_loans} loan items!")
            print(f"   Comparison results show {new_balance_bf} NEW Balance B/F items")
            print(f"   But only {total_loans} loans tracked in Bank Adviser tables")
            
            # Show some missing items
            cursor.execute("""
                SELECT employee_id, item_label, current_value
                FROM comparison_results 
                WHERE session_id = ? AND change_type = 'NEW' AND section_name = 'LOANS'
                AND item_label LIKE '%BALANCE B/F%'
                LIMIT 10
            """, (current_session,))
            
            missing_samples = cursor.fetchall()
            print("   Sample NEW Balance B/F items from comparison results:")
            for row in missing_samples:
                print(f"     {row[0]} - {row[1]} = {row[2]}")
        
        elif total_loans > new_balance_bf:
            print(f"   ⚠️ More loans tracked ({total_loans}) than NEW Balance B/F items ({new_balance_bf})")
            print("   This suggests some non-Balance B/F items were tracked")
        
        else:
            print(f"   ✅ Loan tracking matches NEW Balance B/F items: {total_loans}")
        
        # 5. Check all loan-related change types
        print("\n5. 📊 ALL LOAN-RELATED CHANGE TYPES:")
        
        cursor.execute("""
            SELECT change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND section_name = 'LOANS'
            GROUP BY change_type
            ORDER BY count DESC
        """, (current_session,))
        
        all_loan_changes = cursor.fetchall()
        
        if all_loan_changes:
            print("   All loan changes by type:")
            total_loan_changes = 0
            for row in all_loan_changes:
                print(f"     {row[0]}: {row[1]} items")
                total_loan_changes += row[1]
            
            print(f"   Total loan changes: {total_loan_changes}")
            
            # Calculate what we're missing
            new_loans = next((row[1] for row in all_loan_changes if row[0] == 'NEW'), 0)
            increased_loans = next((row[1] for row in all_loan_changes if row[0] == 'INCREASED'), 0)
            decreased_loans = next((row[1] for row in all_loan_changes if row[0] == 'DECREASED'), 0)
            removed_loans = next((row[1] for row in all_loan_changes if row[0] == 'REMOVED'), 0)
            
            print(f"\n   Detailed breakdown:")
            print(f"     NEW loans: {new_loans} (currently tracking Balance B/F only)")
            print(f"     INCREASED loans: {increased_loans} (not tracked)")
            print(f"     DECREASED loans: {decreased_loans} (not tracked)")
            print(f"     REMOVED loans: {removed_loans} (not tracked)")
            
            potential_total = new_loans + increased_loans + decreased_loans + removed_loans
            print(f"     Potential total trackable: {potential_total}")
        
        # 6. Show actual loan data in Bank Adviser tables
        print("\n6. 📋 ACTUAL LOAN DATA IN BANK ADVISER TABLES:")
        
        if in_house_count > 0:
            cursor.execute("""
                SELECT employee_no, employee_name, loan_type, loan_amount
                FROM in_house_loans 
                WHERE source_session = ?
                ORDER BY loan_amount DESC
                LIMIT 5
            """, (current_session,))
            
            in_house_samples = cursor.fetchall()
            print(f"   In-house loans ({in_house_count} total) - top 5 by amount:")
            for row in in_house_samples:
                print(f"     {row[0]} - {row[1]}: {row[2]} = {row[3]}")
        
        if external_count > 0:
            cursor.execute("""
                SELECT employee_no, employee_name, loan_type, loan_amount
                FROM external_loans 
                WHERE source_session = ?
                ORDER BY loan_amount DESC
                LIMIT 5
            """, (current_session,))
            
            external_samples = cursor.fetchall()
            print(f"   External loans ({external_count} total) - top 5 by amount:")
            for row in external_samples:
                print(f"     {row[0]} - {row[1]}: {row[2]} = {row[3]}")
        
        # 7. Final summary
        print("\n7. 🎯 FINAL ACCURATE SUMMARY:")
        
        print(f"   📊 Bank Adviser Current Tracking:")
        print(f"     In-house loans: {in_house_count}")
        print(f"     External loans: {external_count}")
        print(f"     Total loans: {total_loans}")
        print(f"     Motor vehicles: {motor_count}")
        print(f"     Grand total: {total_tracked}")
        
        print(f"\n   📊 Comparison Results Available:")
        print(f"     NEW loan items (all): {total_new_loans}")
        print(f"     NEW Balance B/F: {new_balance_bf}")
        
        if len(all_loan_changes) > 0:
            total_available = sum(row[1] for row in all_loan_changes)
            print(f"     Total loan changes: {total_available}")
            
            if total_available > total_loans:
                missed_percentage = ((total_available - total_loans) / total_available) * 100
                print(f"     ⚠️ Missing {total_available - total_loans} items ({missed_percentage:.1f}% of available)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_actual_loan_counts()
