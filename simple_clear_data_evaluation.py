#!/usr/bin/env python3
"""
Simple evaluation of Clear Extracted Data impact
"""

import sqlite3
import os

def simple_clear_data_evaluation():
    """Simple evaluation of Clear Extracted Data impact"""
    print("🔍 CLEAR EXTRACTED DATA IMPACT EVALUATION")
    print("=" * 50)
    
    db_path = r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db"
    
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check what tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print("\n📊 CURRENT DATABASE TABLES:")
        for table in sorted(tables):
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} records")
            except:
                print(f"   {table}: Error reading")
        
        # What Clear Extracted Data targets
        target_tables = ['extracted_data', 'audit_sessions', 'comparison_results', 'pre_reporting_data']
        
        print(f"\n🗑️ WHAT 'CLEAR EXTRACTED DATA' WILL DELETE:")
        total_to_delete = 0
        
        for table in target_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_to_delete += count
                print(f"   ❌ {table}: {count} records")
            else:
                print(f"   ⚠️ {table}: Table doesn't exist")
        
        # What will be preserved
        preserved_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance', 
                          'auto_learning_dictionary', 'current_session', 'session_phases']
        
        print(f"\n✅ WHAT WILL BE PRESERVED:")
        total_preserved = 0
        
        for table in preserved_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_preserved += count
                print(f"   ✅ {table}: {count} records")
            else:
                print(f"   ⚠️ {table}: Table doesn't exist")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Records to DELETE: {total_to_delete}")
        print(f"   Records to PRESERVE: {total_preserved}")
        
        print(f"\n💡 RECOMMENDATION:")
        if total_to_delete > 0:
            print("   ⚠️ WARNING: You will lose audit data!")
            print("   • All extracted payroll data")
            print("   • All comparison results") 
            print("   • All pre-reporting data")
            print("   • Current audit session progress")
            print("   ✅ Bank Adviser tracker data WILL be preserved")
            print("   ✅ Auto-learning dictionary WILL be preserved")
            print(f"\n   🎯 USE ONLY IF:")
            print("   • You want to start fresh")
            print("   • You've exported all needed reports")
            print("   • You're troubleshooting issues")
        else:
            print("   ✅ Safe to use - no critical data to lose")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    simple_clear_data_evaluation()
