#!/usr/bin/env python3
"""
Test that the table drop fix works
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_table_drop_fix():
    """Test that the table drop fix works"""
    print("🧪 TESTING TABLE DROP FIX")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check current state
        print("\n1. 📊 CHECKING CURRENT STATE:")
        
        critical_tables = ['comparison_results', 'pre_reporting_results', 'extracted_data']
        
        before_counts = {}
        for table in critical_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                before_counts[table] = count
                print(f"   {table}: {count} records")
            except sqlite3.OperationalError as e:
                before_counts[table] = "TABLE_NOT_EXISTS"
                print(f"   {table}: Table doesn't exist - {e}")
        
        # 2. Test PhasedProcessManager initialization
        print("\n2. 🧪 TESTING PHASEDPROCESSMANAGER INITIALIZATION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            print("   Creating PhasedProcessManager instance...")
            manager = PhasedProcessManager()
            print("   ✅ PhasedProcessManager created successfully")
        
        except Exception as e:
            print(f"   ❌ PhasedProcessManager creation failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 3. Check state after initialization
        print("\n3. 📊 CHECKING STATE AFTER INITIALIZATION:")
        
        after_counts = {}
        tables_dropped = []
        tables_preserved = []
        
        for table in critical_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                after_counts[table] = count
                
                before_count = before_counts[table]
                if before_count == "TABLE_NOT_EXISTS":
                    print(f"   {table}: {count} records (table created)")
                elif count == before_count:
                    print(f"   {table}: {count} records (✅ PRESERVED)")
                    tables_preserved.append(table)
                else:
                    print(f"   {table}: {before_count} → {count} records (❌ CHANGED)")
                    if count == 0:
                        tables_dropped.append(table)
            
            except sqlite3.OperationalError as e:
                after_counts[table] = "TABLE_NOT_EXISTS"
                if before_counts[table] != "TABLE_NOT_EXISTS":
                    print(f"   {table}: ❌ TABLE DROPPED - {e}")
                    tables_dropped.append(table)
                else:
                    print(f"   {table}: Table still doesn't exist")
        
        # 4. Test data access
        print("\n4. 🧪 TESTING DATA ACCESS:")
        
        try:
            # Test get_pre_reporting_data
            result = manager.get_pre_reporting_data()
            
            if result.get('success'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                session_id = result.get('session_id', 'unknown')
                
                print(f"   ✅ get_pre_reporting_data working:")
                print(f"     Session: {session_id}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
            else:
                print(f"   ❌ get_pre_reporting_data failed: {result}")
        
        except Exception as e:
            print(f"   ❌ Data access test failed: {e}")
        
        # 5. Summary
        print("\n5. ✅ SUMMARY:")
        
        if tables_dropped:
            print(f"   ❌ TABLES DROPPED: {tables_dropped}")
            print("   🔧 Fix did not work - tables are still being dropped")
        else:
            print("   ✅ NO TABLES DROPPED")
        
        if tables_preserved:
            print(f"   ✅ TABLES PRESERVED: {tables_preserved}")
        
        if not tables_dropped and len(tables_preserved) > 0:
            print("   🎉 TABLE DROP FIX SUCCESSFUL!")
            print("   ✅ Data persistence is now guaranteed")
        elif not tables_dropped:
            print("   ✅ No tables were dropped (good)")
            print("   ⚠️ But no existing tables to preserve (need to generate data)")
        else:
            print("   ❌ Fix failed - tables are still being dropped")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_table_drop_fix()
