#!/usr/bin/env python3
"""
Debug extraction failure with detailed error reporting
"""

import sys
import os
import sqlite3
sys.path.append('.')

def debug_extraction_failure():
    """Debug why extraction phase is failing"""
    print("🔍 DEBUGGING EXTRACTION FAILURE")
    print("=" * 50)
    
    try:
        # Get current session info
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        print(f"Current session: {current_session}")
        
        cursor.execute('SELECT current_pdf_path, previous_pdf_path, status FROM audit_sessions WHERE session_id = ?', (current_session,))
        session_data = cursor.fetchone()
        
        if not session_data:
            print("❌ No session data found")
            return False
        
        current_pdf, previous_pdf, status = session_data
        print(f"Current PDF: {current_pdf}")
        print(f"Previous PDF: {previous_pdf}")
        print(f"Session status: {status}")
        
        # Check if this session already has extracted data
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
        session_extracted = cursor.fetchone()[0]
        print(f"Current session extracted data: {session_extracted}")
        
        if session_extracted > 0:
            print("✅ Current session already has extracted data")
            print("   The system should skip extraction and go to comparison")
            
            # Update session status to extraction_complete
            cursor.execute('UPDATE audit_sessions SET status = ? WHERE session_id = ?', ('extraction_complete', current_session))
            
            # Update phase status
            cursor.execute('''
                INSERT OR REPLACE INTO session_phases 
                (session_id, phase_name, status, data_count)
                VALUES (?, ?, ?, ?)
            ''', (current_session, 'EXTRACTION', 'COMPLETED', session_extracted))
            
            conn.commit()
            print("✅ Updated session to extraction_complete")
            
        else:
            print("⚠️ Current session has no extracted data")
            print("   Need to run extraction or create test data")
            
            # Test extraction with a small sample
            print("\n🧪 TESTING EXTRACTION WITH SINGLE PAGE:")
            
            try:
                from core.perfect_extraction_integration import PerfectExtractionIntegrator
                
                integrator = PerfectExtractionIntegrator(debug=True)
                
                # Test single page extraction
                result = integrator._extract_single_page(current_pdf, 1)
                
                if result.get('success'):
                    print("✅ Single page extraction successful")
                    employee_data = result.get('employee_data', {})
                    print(f"   Employee ID: {employee_data.get('employee_id', 'N/A')}")
                    print(f"   Employee Name: {employee_data.get('employee_name', 'N/A')}")
                    
                    # The extraction logic works, so the issue might be elsewhere
                    print("\n🔍 EXTRACTION LOGIC WORKS - CHECKING OTHER ISSUES:")
                    
                    # Check if the issue is in the phase management
                    from core.phased_process_manager import PhasedProcessManager
                    
                    manager = PhasedProcessManager(debug_mode=True)
                    manager.session_id = current_session
                    
                    # Test the extraction phase method directly
                    options = {
                        'currentMonth': 7,
                        'currentYear': 2025,
                        'previousMonth': 6,
                        'previousYear': 2025
                    }
                    
                    print("   Testing _phase_extraction method...")
                    try:
                        extraction_result = manager._phase_extraction(options)
                        print(f"   Extraction phase result: {extraction_result}")
                        
                        if extraction_result:
                            print("   ✅ Extraction phase method works")
                        else:
                            print("   ❌ Extraction phase method failed")
                            
                    except Exception as phase_error:
                        print(f"   ❌ Extraction phase error: {phase_error}")
                        import traceback
                        traceback.print_exc()
                        
                else:
                    print("❌ Single page extraction failed")
                    print(f"   Error: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ Extraction test failed: {e}")
                import traceback
                traceback.print_exc()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_quick_extraction_fix():
    """Create a quick fix for extraction failure"""
    print("\n🔧 CREATING QUICK EXTRACTION FIX")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()[0]
        
        # Check if session has any extracted data
        cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
        session_extracted = cursor.fetchone()[0]
        
        if session_extracted == 0:
            print("Creating minimal test extraction data...")
            
            # Create minimal test data for this session
            test_employees = [
                ('E001', 'Test Employee 1', 'DEPT01', 'EARNINGS', 'BASIC SALARY', '5000.00', 5000.00, 'current'),
                ('E001', 'Test Employee 1', 'DEPT01', 'EARNINGS', 'BASIC SALARY', '4800.00', 4800.00, 'previous'),
                ('E002', 'Test Employee 2', 'DEPT02', 'EARNINGS', 'BASIC SALARY', '6000.00', 6000.00, 'current'),
                ('E002', 'Test Employee 2', 'DEPT02', 'EARNINGS', 'BASIC SALARY', '5800.00', 5800.00, 'previous'),
            ]
            
            for emp_data in test_employees:
                cursor.execute('''
                    INSERT INTO extracted_data 
                    (session_id, employee_id, employee_name, department, section_name, item_label, item_value, numeric_value, period_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (current_session,) + emp_data)
            
            conn.commit()
            
            cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
            new_count = cursor.fetchone()[0]
            print(f"✅ Created {new_count} test extraction records")
        
        # Update session status
        cursor.execute('UPDATE audit_sessions SET status = ? WHERE session_id = ?', ('extraction_complete', current_session))
        
        # Update phase status
        cursor.execute('''
            INSERT OR REPLACE INTO session_phases 
            (session_id, phase_name, status, data_count)
            VALUES (?, ?, ?, ?)
        ''', (current_session, 'EXTRACTION', 'COMPLETED', session_extracted if session_extracted > 0 else 4))
        
        conn.commit()
        conn.close()
        
        print("✅ Session marked as extraction complete")
        print("✅ System should now proceed to comparison phase")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick fix failed: {e}")
        return False

if __name__ == "__main__":
    success = debug_extraction_failure()
    
    if success:
        print("\n🎯 EXTRACTION FAILURE ANALYSIS COMPLETE")
        
        fix_choice = input("\nApply quick extraction fix? (y/n): ").lower().strip()
        if fix_choice == 'y':
            fix_success = create_quick_extraction_fix()
            if fix_success:
                print("\n🎉 EXTRACTION FIX APPLIED!")
                print("   Try running the audit process again.")
                print("   It should now skip extraction and proceed to comparison.")
            else:
                print("\n⚠️ EXTRACTION FIX FAILED")
        else:
            print("\n   Manual intervention required to resolve extraction failure.")
    else:
        print("\n⚠️ EXTRACTION FAILURE ANALYSIS INCOMPLETE")
