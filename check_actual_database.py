#!/usr/bin/env python3
"""
Check the actual database structure and data
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_actual_database():
    """Check what's actually in the database"""
    print("🔍 CHECKING ACTUAL DATABASE STRUCTURE AND DATA")
    print("=" * 60)
    
    # Get database path
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return
    
    print(f"📁 Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check what tables exist
        print("\n1. 📋 AVAILABLE TABLES:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   {table_name}: {count} records")
        
        # 2. Check audit_sessions table
        print("\n2. 📋 AUDIT SESSIONS:")
        cursor.execute("""
            SELECT session_id, current_pdf_path, previous_pdf_path, status, created_at
            FROM audit_sessions 
            ORDER BY created_at DESC 
            LIMIT 3
        """)
        sessions = cursor.fetchall()
        
        if not sessions:
            print("   ❌ No audit sessions found")
            return
        
        for session in sessions:
            print(f"   Session: {session[0]}")
            print(f"   Status: {session[3]}")
            print(f"   Created: {session[4]}")
            print()
        
        # Use the most recent session
        latest_session = sessions[0][0]
        print(f"🎯 Using latest session: {latest_session}")
        
        # 3. Check extracted_data table specifically
        print("\n3. 📊 EXTRACTED_DATA TABLE:")
        
        # Check current period data
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'current'
        """, (latest_session,))
        current_count = cursor.fetchone()[0]
        
        # Check previous period data
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM extracted_data 
            WHERE session_id = ? AND period_type = 'previous'
        """, (latest_session,))
        previous_count = cursor.fetchone()[0]
        
        print(f"   Current period records: {current_count}")
        print(f"   Previous period records: {previous_count}")
        
        # 4. Check if data exists for ANY session
        print("\n4. 📊 ALL EXTRACTED DATA:")
        cursor.execute("""
            SELECT session_id, period_type, COUNT(*) as count
            FROM extracted_data 
            GROUP BY session_id, period_type
            ORDER BY session_id DESC
        """)
        all_data = cursor.fetchall()
        
        if all_data:
            print("   Data by session and period:")
            for row in all_data:
                print(f"     Session {row[0]} ({row[1]}): {row[2]} records")
        else:
            print("   ❌ NO EXTRACTED DATA FOUND IN DATABASE!")
        
        # 5. Check unique employees in current session
        if current_count > 0:
            print("\n5. 👥 UNIQUE EMPLOYEES (CURRENT):")
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as unique_employees
                FROM extracted_data 
                WHERE session_id = ? AND period_type = 'current'
            """, (latest_session,))
            current_unique = cursor.fetchone()[0]
            print(f"   Unique employees in current period: {current_unique}")
            
            # Sample some employee IDs
            cursor.execute("""
                SELECT DISTINCT employee_id, employee_name
                FROM extracted_data 
                WHERE session_id = ? AND period_type = 'current'
                LIMIT 5
            """, (latest_session,))
            sample_employees = cursor.fetchall()
            
            print("   Sample employees:")
            for emp in sample_employees:
                print(f"     {emp[0]}: {emp[1]}")
        
        if previous_count > 0:
            print("\n6. 👥 UNIQUE EMPLOYEES (PREVIOUS):")
            cursor.execute("""
                SELECT COUNT(DISTINCT employee_id) as unique_employees
                FROM extracted_data 
                WHERE session_id = ? AND period_type = 'previous'
            """, (latest_session,))
            previous_unique = cursor.fetchone()[0]
            print(f"   Unique employees in previous period: {previous_unique}")
            
            # Sample some employee IDs
            cursor.execute("""
                SELECT DISTINCT employee_id, employee_name
                FROM extracted_data 
                WHERE session_id = ? AND period_type = 'previous'
                LIMIT 5
            """, (latest_session,))
            sample_employees = cursor.fetchall()
            
            print("   Sample employees:")
            for emp in sample_employees:
                print(f"     {emp[0]}: {emp[1]}")
        
        # 6. Check data structure
        if current_count > 0:
            print("\n7. 📋 DATA STRUCTURE SAMPLE:")
            cursor.execute("""
                SELECT employee_id, employee_name, section_name, item_label, item_value
                FROM extracted_data 
                WHERE session_id = ? AND period_type = 'current'
                LIMIT 10
            """, (latest_session,))
            
            current_sample = cursor.fetchall()
            print("   Current period sample:")
            for row in current_sample:
                print(f"     Employee: {row[0]} ({row[1]})")
                print(f"     Section: {row[2]}, Item: {row[3]}, Value: {row[4]}")
                print()
        
        # 7. Check comparison_results table
        print("\n8. 📈 COMPARISON RESULTS:")
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
        """, (latest_session,))
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results for latest session: {comparison_count}")
        
        # Check all comparison results
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        total_comparison_count = cursor.fetchone()[0]
        print(f"   Total comparison results in database: {total_comparison_count}")
        
        # 8. Diagnosis
        print("\n9. 🎯 DIAGNOSIS:")
        if current_count == 0 and previous_count == 0:
            print("   ❌ NO EXTRACTED DATA AT ALL")
            print("   Problem: Extraction is not storing data in the database")
        elif current_count > 0 and previous_count == 0:
            print("   ❌ MISSING PREVIOUS PERIOD DATA")
            print("   Problem: Previous PDF extraction failed or data not stored")
        elif current_count == 0 and previous_count > 0:
            print("   ❌ MISSING CURRENT PERIOD DATA")
            print("   Problem: Current PDF extraction failed or data not stored")
        else:
            print("   ✅ BOTH PERIODS HAVE DATA")
            print("   Problem: Comparison logic issue or data loading problem")
        
    except Exception as e:
        print(f"❌ Error during database check: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_actual_database()
