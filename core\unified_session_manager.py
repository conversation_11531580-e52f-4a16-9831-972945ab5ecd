#!/usr/bin/env python3
"""
UNIFIED SESSION MANAGER - Single Source of Truth
Redesigned to eliminate session confusion and provide reliable session management
"""

import sqlite3
import os
import time
import uuid
import json
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

class UnifiedSessionManager:
    """
    Single, authoritative session manager for the entire payroll audit system
    
    DESIGN PRINCIPLES:
    1. Single source of truth - all components use this manager
    2. Automatic session lifecycle management
    3. Thread-safe operations
    4. Clear session state transitions
    5. Robust error handling and recovery
    """
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'templar_payroll_auditor.db')
        
        self.db_path = db_path
        self._ensure_database_schema()
        
    def _ensure_database_schema(self):
        """Ensure all required tables exist with correct schema"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Single current session table (only one row allowed)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS current_session (
                    id INTEGER PRIMARY KEY CHECK (id = 1),
                    session_id TEXT NOT NULL,
                    session_name TEXT,
                    status TEXT DEFAULT 'ACTIVE',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Session phases with clear status tracking
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS session_phases (
                    session_id TEXT NOT NULL,
                    phase_name TEXT NOT NULL,
                    status TEXT DEFAULT 'NOT_STARTED',
                    started_at DATETIME,
                    completed_at DATETIME,
                    data_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    PRIMARY KEY (session_id, phase_name)
                )
            """)
            
            conn.commit()
    
    @contextmanager
    def _get_connection(self):
        """Thread-safe database connection context manager"""
        conn = sqlite3.connect(self.db_path, timeout=30.0)
        try:
            yield conn
        finally:
            conn.close()
    
    def create_new_session(self, current_pdf: str, previous_pdf: str, options: Dict[str, Any] = None) -> str:
        """
        Create a new session and automatically set it as current
        This is the ONLY way sessions should be created
        """
        # Generate unique session ID
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        session_id = f"audit_session_{timestamp}_{unique_id}"
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                # 1. Create the audit session
                cursor.execute("""
                    INSERT INTO audit_sessions 
                    (session_id, current_pdf_path, previous_pdf_path, status, created_at)
                    VALUES (?, ?, ?, 'in_progress', datetime('now'))
                """, (session_id, current_pdf, previous_pdf))
                
                # 2. Set as current session (CRITICAL - this was missing!)
                cursor.execute("""
                    INSERT OR REPLACE INTO current_session 
                    (id, session_id, session_name, status, updated_at)
                    VALUES (1, ?, ?, 'ACTIVE', datetime('now'))
                """, (session_id, f"Audit_{session_id[-8:]}"))
                
                # 3. Initialize all phase statuses
                phases = [
                    'EXTRACTION',
                    'COMPARISON', 
                    'AUTO_LEARNING',
                    'TRACKER_FEEDING',
                    'PRE_REPORTING',
                    'REPORT_GENERATION'
                ]
                
                for phase in phases:
                    cursor.execute("""
                        INSERT OR REPLACE INTO session_phases 
                        (session_id, phase_name, status, data_count)
                        VALUES (?, ?, 'NOT_STARTED', 0)
                    """, (session_id, phase))
                
                conn.commit()
                
                print(f"✅ UNIFIED SESSION: Created and activated session {session_id}")
                return session_id
                
            except Exception as e:
                conn.rollback()
                raise Exception(f"Failed to create session: {e}")
    
    def get_current_session_id(self) -> str:
        """Get the current active session ID - SINGLE SOURCE OF TRUTH"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
            result = cursor.fetchone()
            
            if result:
                return result[0]
            else:
                # No current session - find the most recent session with data
                cursor.execute("""
                    SELECT session_id FROM audit_sessions 
                    WHERE session_id IN (
                        SELECT DISTINCT session_id FROM pre_reporting_results
                        UNION
                        SELECT DISTINCT session_id FROM comparison_results
                        UNION  
                        SELECT DISTINCT session_id FROM extracted_data
                    )
                    ORDER BY created_at DESC 
                    LIMIT 1
                """)
                
                recent_session = cursor.fetchone()
                if recent_session:
                    # Auto-set as current session
                    self.set_current_session(recent_session[0])
                    return recent_session[0]
                else:
                    raise Exception("No sessions found with data")
    
    def set_current_session(self, session_id: str, session_name: str = None):
        """Set a specific session as current"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Verify session exists
            cursor.execute("SELECT 1 FROM audit_sessions WHERE session_id = ?", (session_id,))
            if not cursor.fetchone():
                raise Exception(f"Session {session_id} does not exist")
            
            # Set as current
            cursor.execute("""
                INSERT OR REPLACE INTO current_session 
                (id, session_id, session_name, status, updated_at)
                VALUES (1, ?, ?, 'ACTIVE', datetime('now'))
            """, (session_id, session_name or f"Session_{session_id[-8:]}"))
            
            conn.commit()
            print(f"✅ UNIFIED SESSION: Set current session to {session_id}")
    
    def update_phase_status(self, phase_name: str, status: str, data_count: int = 0, error_message: str = None):
        """Update phase status for current session"""
        session_id = self.get_current_session_id()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            if status == 'COMPLETED':
                cursor.execute("""
                    INSERT OR REPLACE INTO session_phases 
                    (session_id, phase_name, status, completed_at, data_count, error_message)
                    VALUES (?, ?, ?, datetime('now'), ?, ?)
                """, (session_id, phase_name, status, data_count, error_message))
            elif status == 'IN_PROGRESS':
                cursor.execute("""
                    INSERT OR REPLACE INTO session_phases 
                    (session_id, phase_name, status, started_at, data_count, error_message)
                    VALUES (?, ?, ?, datetime('now'), ?, ?)
                """, (session_id, phase_name, status, data_count, error_message))
            else:
                cursor.execute("""
                    INSERT OR REPLACE INTO session_phases 
                    (session_id, phase_name, status, data_count, error_message)
                    VALUES (?, ?, ?, ?, ?)
                """, (session_id, phase_name, status, data_count, error_message))
            
            conn.commit()
            print(f"✅ UNIFIED SESSION: Updated {phase_name} to {status} for session {session_id}")
    
    def get_session_status(self, session_id: str = None) -> Dict[str, Any]:
        """Get complete session status"""
        if not session_id:
            session_id = self.get_current_session_id()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Get session info
            cursor.execute("""
                SELECT status, created_at, current_pdf_path, previous_pdf_path
                FROM audit_sessions WHERE session_id = ?
            """, (session_id,))
            
            session_info = cursor.fetchone()
            if not session_info:
                raise Exception(f"Session {session_id} not found")
            
            # Get phase statuses
            cursor.execute("""
                SELECT phase_name, status, data_count, started_at, completed_at, error_message
                FROM session_phases 
                WHERE session_id = ?
                ORDER BY 
                    CASE phase_name
                        WHEN 'EXTRACTION' THEN 1
                        WHEN 'COMPARISON' THEN 2
                        WHEN 'AUTO_LEARNING' THEN 3
                        WHEN 'TRACKER_FEEDING' THEN 4
                        WHEN 'PRE_REPORTING' THEN 5
                        WHEN 'REPORT_GENERATION' THEN 6
                        ELSE 7
                    END
            """, (session_id,))
            
            phases = cursor.fetchall()
            
            return {
                'session_id': session_id,
                'session_status': session_info[0],
                'created_at': session_info[1],
                'current_pdf': session_info[2],
                'previous_pdf': session_info[3],
                'phases': [
                    {
                        'name': row[0],
                        'status': row[1],
                        'data_count': row[2],
                        'started_at': row[3],
                        'completed_at': row[4],
                        'error_message': row[5]
                    }
                    for row in phases
                ]
            }
    
    def get_pre_reporting_data(self, session_id: str = None) -> Dict[str, Any]:
        """Get pre-reporting data for session"""
        if not session_id:
            session_id = self.get_current_session_id()
        
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?
            """, (session_id,))
            
            count = cursor.fetchone()[0]
            
            if count == 0:
                return {
                    'success': False,
                    'error': 'No pre-reporting data found',
                    'session_id': session_id,
                    'total_changes': 0
                }
            
            cursor.execute("""
                SELECT * FROM pre_reporting_results WHERE session_id = ? ORDER BY bulk_category, change_id
            """, (session_id,))
            
            data = cursor.fetchall()
            
            return {
                'success': True,
                'session_id': session_id,
                'total_changes': count,
                'data': [dict(zip([col[0] for col in cursor.description], row)) for row in data]
            }

# Global instance - SINGLE SOURCE OF TRUTH
_unified_session_manager = None

def get_unified_session_manager() -> UnifiedSessionManager:
    """Get the global unified session manager instance"""
    global _unified_session_manager
    if _unified_session_manager is None:
        _unified_session_manager = UnifiedSessionManager()
    return _unified_session_manager

def get_current_session_id() -> str:
    """Convenience function to get current session ID"""
    return get_unified_session_manager().get_current_session_id()

def create_new_session(current_pdf: str, previous_pdf: str, options: Dict[str, Any] = None) -> str:
    """Convenience function to create new session"""
    return get_unified_session_manager().create_new_session(current_pdf, previous_pdf, options)

def update_phase_status(phase_name: str, status: str, data_count: int = 0, error_message: str = None):
    """Convenience function to update phase status"""
    return get_unified_session_manager().update_phase_status(phase_name, status, data_count, error_message)
