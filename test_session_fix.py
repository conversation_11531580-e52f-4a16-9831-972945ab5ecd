#!/usr/bin/env python3
"""
Test script to verify the session ID generation fix and duplicate cleanup
"""

import sys
import os
import sqlite3
import time
import uuid
import random

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]

    for path in db_paths:
        if os.path.exists(path):
            return path

    return None

def test_session_id_generation():
    """Test the new session ID generation logic"""
    print("🧪 Testing Session ID Generation Logic")
    print("=" * 50)
    
    # Test the new session ID generation pattern
    session_ids = []
    for i in range(10):
        timestamp = int(time.time())
        unique_suffix = str(uuid.uuid4())[:8]
        random_num = random.randint(1000, 9999)
        session_id = f"audit_session_{timestamp}_{unique_suffix}_{random_num}"
        session_ids.append(session_id)
        print(f"Generated Session ID {i+1}: {session_id}")
        time.sleep(0.1)  # Small delay to ensure different timestamps
    
    # Check for uniqueness
    unique_ids = set(session_ids)
    print(f"\n✅ Generated {len(session_ids)} session IDs")
    print(f"✅ Unique session IDs: {len(unique_ids)}")
    print(f"✅ All unique: {len(session_ids) == len(unique_ids)}")
    
    return len(session_ids) == len(unique_ids)

def test_database_cleanup():
    """Test the database cleanup functionality"""
    print("\n🧹 Testing Database Cleanup Functionality")
    print("=" * 50)

    # Get database path
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        # Create some test audit sessions
        test_sessions = [
            ("test_session_1", "current1.pdf", "previous1.pdf"),
            ("test_session_2", "current2.pdf", "previous2.pdf"),
            ("test_session_1", "current1_dup.pdf", "previous1_dup.pdf"),  # Duplicate
        ]

        print("📝 Creating test audit sessions...")
        for session_id, current_pdf, previous_pdf in test_sessions:
            try:
                cursor.execute(
                    '''INSERT INTO audit_sessions
                       (session_id, current_pdf_path, previous_pdf_path, status, created_at)
                       VALUES (?, ?, ?, ?, datetime('now'))''',
                    (session_id, current_pdf, previous_pdf, 'test')
                )
                print(f"  ✅ Created session: {session_id}")
            except Exception as e:
                print(f"  ⚠️ Failed to create session {session_id}: {e}")

        conn.commit()

        # Check sessions before cleanup
        print("\n📊 Sessions before cleanup:")
        cursor.execute(
            "SELECT session_id, current_pdf_path, created_at FROM audit_sessions WHERE status = 'test'"
        )
        sessions = cursor.fetchall()
        for session in sessions:
            print(f"  - {session[0]}: {session[1]} ({session[2]})")

        # Test duplicate cleanup
        print("\n🧹 Running duplicate cleanup...")

        # Find and remove duplicate sessions (keep the most recent one for each session_id pattern)
        duplicate_cleanup_query = """
            DELETE FROM audit_sessions
            WHERE id NOT IN (
                SELECT MAX(id)
                FROM audit_sessions
                WHERE status = 'test'
                GROUP BY session_id
            ) AND status = 'test'
        """

        cursor.execute(duplicate_cleanup_query)
        rows_affected = cursor.rowcount
        conn.commit()
        print(f"  ✅ Cleanup completed: {rows_affected} rows affected")

        # Check sessions after cleanup
        print("\n📊 Sessions after cleanup:")
        cursor.execute(
            "SELECT session_id, current_pdf_path, created_at FROM audit_sessions WHERE status = 'test'"
        )
        sessions = cursor.fetchall()
        for session in sessions:
            print(f"  - {session[0]}: {session[1]} ({session[2]})")

        # Clean up test data
        print("\n🗑️ Cleaning up test data...")
        cursor.execute("DELETE FROM audit_sessions WHERE status = 'test'")
        conn.commit()
        print("  ✅ Test data cleaned up")

        return True

    except Exception as e:
        print(f"❌ Database cleanup test failed: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_session_creation_with_retry():
    """Test session creation with retry logic"""
    print("\n🔄 Testing Session Creation with Retry Logic")
    print("=" * 50)

    # Get database path
    db_path = get_database_path()
    if not db_path:
        print("❌ Database file not found")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        # Simulate the new session creation logic
        max_retries = 5
        retry_count = 0
        session_created = False
        
        while retry_count < max_retries and not session_created:
            try:
                # Generate session ID
                timestamp = int(time.time())
                unique_suffix = str(uuid.uuid4())[:8]
                random_num = random.randint(1000, 9999)
                session_id = f"audit_session_{timestamp}_{unique_suffix}_{random_num}"
                
                print(f"Attempt {retry_count + 1}: Trying session ID: {session_id}")
                
                # Try to insert the session
                cursor.execute(
                    '''INSERT INTO audit_sessions
                       (session_id, current_pdf_path, previous_pdf_path, status, created_at)
                       VALUES (?, ?, ?, ?, datetime('now'))''',
                    (session_id, "test_current.pdf", "test_previous.pdf", "test_retry")
                )
                conn.commit()

                print(f"  ✅ Session created successfully: {session_id}")
                session_created = True

                # Clean up
                cursor.execute("DELETE FROM audit_sessions WHERE session_id = ?", (session_id,))
                conn.commit()
                
            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    retry_count += 1
                    print(f"  ⚠️ UNIQUE constraint collision, retrying... (attempt {retry_count})")
                    time.sleep(0.1)  # Small delay before retry
                else:
                    print(f"  ❌ Different error: {e}")
                    break
        
        if session_created:
            print(f"✅ Session creation successful after {retry_count + 1} attempts")
            return True
        else:
            print(f"❌ Session creation failed after {max_retries} attempts")
            return False
            
    except Exception as e:
        print(f"❌ Session creation test failed: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Run all tests"""
    print("🚀 Session ID Generation and Database Cleanup Tests")
    print("=" * 60)
    
    # Test 1: Session ID Generation
    test1_result = test_session_id_generation()
    
    # Test 2: Database Cleanup
    test2_result = test_database_cleanup()
    
    # Test 3: Session Creation with Retry
    test3_result = test_session_creation_with_retry()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 30)
    print(f"Session ID Generation: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Database Cleanup: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"Session Creation Retry: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    all_passed = test1_result and test2_result and test3_result
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 The session ID generation fix should resolve the UNIQUE constraint error!")
    else:
        print("\n⚠️ Some issues detected. Please review the test results above.")

if __name__ == "__main__":
    main()
