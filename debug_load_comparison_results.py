#!/usr/bin/env python3
"""
Debug the _load_all_comparison_results method
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_load_comparison_results():
    """Debug the comparison results loading"""
    print("🔍 DEBUGGING _load_all_comparison_results METHOD")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Direct database check
        print("\n1. 📊 DIRECT DATABASE CHECK:")
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
        direct_count = cursor.fetchone()[0]
        print(f"   Direct count: {direct_count}")
        
        if direct_count > 0:
            # Sample some results
            cursor.execute("""
                SELECT id, employee_id, employee_name, section_name, item_label, change_type, priority
                FROM comparison_results 
                WHERE session_id = ?
                LIMIT 5
            """, (current_session,))
            
            sample_results = cursor.fetchall()
            print("   Sample results:")
            for row in sample_results:
                print(f"     {row[0]}: {row[1]} - {row[3]}.{row[4]} ({row[5]}, {row[6]})")
        
        # 2. Test the exact query from _load_all_comparison_results
        print("\n2. 🔄 TESTING EXACT QUERY FROM METHOD:")
        
        query = '''SELECT id, employee_id, employee_name, section_name, item_label,
                          previous_value, current_value, change_type, priority,
                          numeric_difference, percentage_change
                   FROM comparison_results
                   WHERE session_id = ?
                   ORDER BY priority DESC, employee_id, section_name, item_label'''
        
        cursor.execute(query, (current_session,))
        query_results = cursor.fetchall()
        print(f"   Query returned: {len(query_results)} rows")
        
        if query_results:
            print("   Sample query result:")
            row = query_results[0]
            print(f"     ID: {row[0]}")
            print(f"     Employee: {row[1]} - {row[2]}")
            print(f"     Change: {row[3]}.{row[4]}")
            print(f"     Type: {row[7]}")
            print(f"     Priority: {row[8]}")
        
        # 3. Test the Python database manager
        print("\n3. 🔄 TESTING PYTHON DATABASE MANAGER:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.python_database_manager import PythonDatabaseManager
            
            db_manager = PythonDatabaseManager()
            
            # Test the exact query
            rows = db_manager.execute_query(query, (current_session,))
            print(f"   Python DB Manager returned: {len(rows)} rows")
            
            if rows:
                print("   Sample Python DB Manager result:")
                row = rows[0]
                print(f"     Type: {type(row)}")
                if isinstance(row, dict):
                    print(f"     Keys: {list(row.keys())}")
                    print(f"     ID: {row.get('id')}")
                    print(f"     Employee: {row.get('employee_id')} - {row.get('employee_name')}")
                    print(f"     Change: {row.get('section_name')}.{row.get('item_label')}")
                else:
                    print(f"     Row: {row}")
            
            db_manager.close()
            
        except Exception as e:
            print(f"   ❌ Python DB Manager test failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. Test the phased process manager method directly
        print("\n4. 🔄 TESTING PHASED PROCESS MANAGER METHOD:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Test the method directly
            all_changes = manager._load_all_comparison_results()
            print(f"   Method returned: {len(all_changes)} changes")
            
            if all_changes:
                print("   Sample method result:")
                change = all_changes[0]
                print(f"     Type: {type(change)}")
                print(f"     Keys: {list(change.keys())}")
                print(f"     ID: {change.get('id')}")
                print(f"     Employee: {change.get('employee_id')} - {change.get('employee_name')}")
                print(f"     Change: {change.get('section_name')}.{change.get('item_label')}")
            else:
                print("   ❌ Method returned no changes - investigating...")
                
                # Check if the session_id is being set correctly
                print(f"   Manager session_id: {manager.session_id}")
                
                # Check if the database manager is working
                test_query = "SELECT COUNT(*) FROM comparison_results"
                test_result = manager.db_manager.execute_query(test_query)
                print(f"   Total comparison results in database: {test_result[0] if test_result else 'None'}")
                
                # Check if the specific session query works
                session_query = "SELECT COUNT(*) FROM comparison_results WHERE session_id = ?"
                session_result = manager.db_manager.execute_query(session_query, (current_session,))
                print(f"   Comparison results for session: {session_result[0] if session_result else 'None'}")
            
        except Exception as e:
            print(f"   ❌ Phased Process Manager test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_load_comparison_results()
