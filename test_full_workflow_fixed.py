#!/usr/bin/env python3
"""
Test the full workflow with the fixed PRE-REPORTING phase
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def test_full_workflow_fixed():
    """Test the full workflow with fixes"""
    print("🔍 TESTING FULL WORKFLOW WITH FIXED PRE-REPORTING")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Clear any existing comparison and pre-reporting results for clean test
        print("\n1. 🧹 CLEANING EXISTING RESULTS:")
        cursor.execute("DELETE FROM comparison_results WHERE session_id = ?", (current_session,))
        cursor.execute("DELETE FROM pre_reporting_results WHERE session_id = ?", (current_session,))
        conn.commit()
        print("   ✅ Cleaned existing results")
        
        # Test the full workflow
        print("\n2. 🔄 RUNNING FULL WORKFLOW:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Step 1: Load extracted data
            print("   Step 1: Loading extracted data...")
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                
                # Step 2: Run comparison
                print("   Step 2: Running comparison...")
                comparison_results = manager._compare_payroll_data(current_data, previous_data)
                print(f"   ✅ Generated {len(comparison_results)} comparison results")
                
                # Step 3: Store comparison results (with fixed method)
                print("   Step 3: Storing comparison results...")
                manager._store_comparison_results(comparison_results)
                
                # Verify storage
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                stored_count = cursor.fetchone()[0]
                print(f"   ✅ Verified: {stored_count} comparison results stored")
                
                if stored_count > 0:
                    # Step 4: Test PRE-REPORTING phase
                    print("   Step 4: Running PRE-REPORTING phase...")
                    
                    options = {
                        'report_name': 'Full Workflow Test Report',
                        'report_designation': 'System Integration Test'
                    }
                    
                    result = manager._phase_pre_reporting(options)
                    
                    if result:
                        print("   ✅ PRE-REPORTING phase completed successfully!")
                        
                        # Check pre-reporting results
                        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                        pre_reporting_count = cursor.fetchone()[0]
                        print(f"   ✅ Pre-reporting results stored: {pre_reporting_count}")
                        
                        # Step 5: Test get_pre_reporting_data method
                        print("   Step 5: Testing get_pre_reporting_data method...")
                        
                        data_result = manager.get_pre_reporting_data(current_session)
                        print(f"   Success: {data_result.get('success', False)}")
                        print(f"   Data count: {len(data_result.get('data', []))}")
                        print(f"   Total changes: {data_result.get('total_changes', 0)}")
                        
                        if data_result.get('success') and data_result.get('data'):
                            print("   ✅ get_pre_reporting_data working correctly!")
                            
                            # Show summary
                            data = data_result.get('data', [])
                            categories = {}
                            selected_count = 0
                            
                            for item in data:
                                category = item.get('bulk_category', 'Unknown')
                                categories[category] = categories.get(category, 0) + 1
                                if item.get('selected_for_report'):
                                    selected_count += 1
                            
                            print(f"   📊 Summary:")
                            print(f"     Total changes: {len(data)}")
                            print(f"     Auto-selected: {selected_count}")
                            print(f"     Categories: {categories}")
                            
                            # Sample some data
                            print("   📋 Sample data:")
                            for i, item in enumerate(data[:3]):
                                print(f"     {i+1}. {item.get('employee_id')} - {item.get('section_name')}.{item.get('item_label')}")
                                print(f"        Category: {item.get('bulk_category')}, Selected: {item.get('selected_for_report')}")
                            
                            print("\n🎉 FULL WORKFLOW TEST SUCCESSFUL!")
                            print("✅ PRE-REPORTING phase is now working correctly!")
                            print("✅ The system can now handle the current session data!")
                            
                        else:
                            print("   ❌ get_pre_reporting_data not working correctly")
                    else:
                        print("   ❌ PRE-REPORTING phase failed")
                else:
                    print("   ❌ No comparison results were stored")
            else:
                print("   ❌ Could not load extracted data")
            
        except Exception as e:
            print(f"   ❌ Workflow test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_workflow_fixed()
