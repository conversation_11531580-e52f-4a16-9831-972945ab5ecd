#!/usr/bin/env python3
import json
import os
from typing import Dict, List, Union, Any

"""
PYTHON DATABASE MANAGER
Provides Python interface to the SQLite database for TEMPLAR PAYROLL AUDITOR
Ensures database-only operations without JSON fallbacks
"""

import sqlite3
import json
import os
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

class PythonDatabaseManager:
    """
    Python interface to the unified SQLite database
    Provides database-only operations for Python modules
    """

    def __init__(self, db_path: str = None):
        self.db_path = db_path or os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'templar_payroll_auditor.db')
        self.connection = None
        self.is_connected = False

        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # Initialize database connection
        self.connect()

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Enable column access by name

            # Enable high-performance settings
            self.connection.execute('PRAGMA journal_mode = WAL')
            self.connection.execute('PRAGMA synchronous = NORMAL')
            self.connection.execute('PRAGMA cache_size = 20000')
            self.connection.execute('PRAGMA temp_store = MEMORY')

            self.is_connected = True
            self.ensure_tables_exist()

        except Exception as e:
            raise Exception(f"Database connection failed: {e}")

    def ensure_tables_exist(self):
        """Ensure all required tables exist"""
        try:
            # Force drop and recreate comparison_results table to fix schema issues
            self.connection.execute("DROP TABLE IF EXISTS comparison_results")
            self.connection.commit()
            # Dictionary tables - unified schema
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS dictionary_sections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    section_name TEXT UNIQUE NOT NULL,
                    section_order INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS dictionary_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    section_id INTEGER,
                    item_name TEXT NOT NULL,
                    standard_key TEXT,
                    format_type TEXT,
                    value_format TEXT,
                    include_in_report BOOLEAN DEFAULT 1,
                    is_fixed BOOLEAN DEFAULT 0,
                    validation_rules TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (section_id) REFERENCES dictionary_sections(id),
                    UNIQUE(section_id, item_name)
                )
            ''')

            # Auto-learning tables
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS auto_learning_sessions (
                    session_id TEXT PRIMARY KEY,
                    session_name TEXT,
                    is_active INTEGER DEFAULT 1,
                    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ended_at DATETIME
                )
            ''')

            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS pending_items (
                    item_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    discovery_id TEXT,
                    section_name TEXT,
                    item_label TEXT,
                    item_value TEXT,
                    confidence REAL,
                    source TEXT,
                    discovery_data TEXT,
                    status TEXT DEFAULT 'pending',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES auto_learning_sessions(session_id)
                )
            ''')

            # Settings table
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS system_settings (
                    setting_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT NOT NULL,
                    setting_value TEXT NOT NULL,
                    module_name TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(setting_key, module_name)
                )
            ''')

            # Reports table (unified schema)
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    report_id TEXT UNIQUE NOT NULL,
                    report_type TEXT NOT NULL,
                    report_category TEXT,
                    title TEXT NOT NULL,
                    description TEXT,
                    file_paths TEXT,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    file_size INTEGER,
                    is_archived BOOLEAN DEFAULT 0
                )
            ''')

            # Phased process tables
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS phased_process_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    current_pdf_path TEXT NOT NULL,
                    previous_pdf_path TEXT,
                    process_options TEXT,
                    total_phases INTEGER DEFAULT 6,
                    completed_phases INTEGER DEFAULT 0,
                    current_phase TEXT,
                    processing_status TEXT DEFAULT 'pending',
                    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS employees (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    employee_id TEXT NOT NULL,
                    employee_name TEXT,
                    department TEXT,
                    section TEXT,
                    job_title TEXT,
                    ssf_number TEXT,
                    ghana_card_id TEXT,
                    page_number INTEGER,
                    period_type TEXT DEFAULT 'current',
                    extraction_confidence REAL DEFAULT 1.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS extracted_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    employee_id TEXT,
                    section_name TEXT,
                    item_label TEXT,
                    item_value TEXT,
                    numeric_value REAL,
                    value_format TEXT,
                    period_type TEXT DEFAULT 'current',
                    extraction_confidence REAL DEFAULT 1.0,
                    extraction_source TEXT DEFAULT 'Perfect Section-Aware Extractor',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # comparison_results table is handled by the unified database schema
            # No constraints on change_type to allow all comparison result types
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS comparison_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    employee_id TEXT NOT NULL,
                    employee_name TEXT,
                    section_name TEXT NOT NULL,
                    item_label TEXT NOT NULL,
                    previous_value TEXT,
                    current_value TEXT,
                    change_type TEXT,
                    priority TEXT,
                    numeric_difference REAL,
                    percentage_change REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Create pre_reporting_results table for pre-reporting phase
            self.connection.execute('''
                CREATE TABLE IF NOT EXISTS pre_reporting_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    change_id INTEGER NOT NULL,
                    selected_for_report BOOLEAN DEFAULT 1,
                    bulk_category TEXT CHECK(bulk_category IN ('Individual', 'Small_Bulk', 'Medium_Bulk', 'Large_Bulk')),
                    bulk_size INTEGER DEFAULT 1,
                    user_notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES phased_process_sessions(session_id)
                )
            ''')

            # Run migrations for existing tables
            self.run_migrations()

            self.connection.commit()

        except Exception as e:
            raise Exception(f"Failed to create tables: {e}")

    def run_migrations(self):
        """Run database migrations for existing tables"""
        try:
            # Check if period_type column exists in employees table
            cursor = self.connection.execute("PRAGMA table_info(employees)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'period_type' not in columns:
                print('🔄 Adding period_type column to employees table...')
                self.connection.execute('ALTER TABLE employees ADD COLUMN period_type TEXT DEFAULT "current"')
                print('✅ Added period_type column to employees table')

            # Check if period_type column exists in extracted_items table
            cursor = self.connection.execute("PRAGMA table_info(extracted_items)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'period_type' not in columns:
                # Silent column addition - no print to avoid encoding issues
                self.connection.execute('ALTER TABLE extracted_items ADD COLUMN period_type TEXT DEFAULT "current"')

            # Silent migration completion - no print to avoid encoding issues
        except Exception as e:
            # Silent error handling - no print to avoid encoding issues
            # Don't throw error to prevent app from crashing
            pass

    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute a SELECT query and return results as list of dictionaries"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # Get column names
            columns = [description[0] for description in cursor.description] if cursor.description else []

            # Fetch all rows and convert to dictionaries
            rows = cursor.fetchall()
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))

            return result
        except Exception as e:
            raise Exception(f"Query execution failed: {e}")

    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an INSERT, UPDATE, or DELETE query and return affected rows"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            self.connection.commit()
            return cursor.rowcount
        except Exception as e:
            raise Exception(f"Update execution failed: {e}")

    def _get_loan_types_from_items(self, loan_items: Dict) -> Dict:
        """Extract loan_types structure from loan items"""
        loan_types = {}

        # Define your original IN-HOUSE loan list for classification
        in_house_loan_types = {
            "SALARY ADVANCE-MINS",
            "BUILDING-MINISTERS",
            "SALARY ADVANCE-STAFF",
            "RENT ADVANCE",
            "SALARY ADVANCE MISSI",
            "SALARY ADVANCE MISSIONS",
            "RENT ADVANCE MISSIONS",
            "STAFF CREDIT UNION LO",
            "PENSIONS SALARY ADVA",
            "PENSIONS RENT ADVANCE"
        }

        # Process loan items to build loan_types structure
        for item_name, item_data in loan_items.items():
            # Skip column headers
            if item_data.get('is_column_header', False):
                continue

            # Extract loan type from composite items (e.g., "RENT ADVANCE - BALANCE B/F")
            if ' - ' in item_name:
                loan_type = item_name.split(' - ')[0].strip()

                # Initialize loan type if not exists
                if loan_type not in loan_types:
                    # Determine classification based on your original list
                    classification = "IN-HOUSE LOAN" if loan_type in in_house_loan_types else "EXTERNAL LOAN"

                    loan_types[loan_type] = {
                        'classification': classification,
                        'items': [],
                        'created_date': item_data.get('created_date', ''),
                        'is_active': True,
                        'is_fixed': loan_type in in_house_loan_types,
                        'source': 'USER_ORIGINAL_LIST' if loan_type in in_house_loan_types else 'EXTRACTED'
                    }

                # Add item to loan type
                if item_name not in loan_types[loan_type]['items']:
                    loan_types[loan_type]['items'].append(item_name)

        return loan_types

    # Auto-learning operations
    def save_auto_learning_session(self, session_id: str, session_name: str, is_active: bool = True):
        """Save auto-learning session"""
        self.execute_update(
            '''INSERT OR REPLACE INTO auto_learning_sessions
               (session_id, session_name, is_active) VALUES (?, ?, ?)''',
            (session_id, session_name, 1 if is_active else 0)
        )

    def save_pending_item(self, session_id: str, discovery_data: Dict):
        """Save a pending item for approval"""
        self.execute_update(
            '''INSERT OR REPLACE INTO pending_items
               (session_id, discovery_id, item_label, item_value, suggested_section,
                suggested_standard_name, confidence_score, status, format_detected,
                value_format_detected, is_fixed_item, auto_approved, loan_classification,
                loan_type, column_type, first_seen_in)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
            (
                session_id,
                discovery_data.get('discovery_id'),
                discovery_data.get('label'),
                discovery_data.get('value'),
                discovery_data.get('section'),
                discovery_data.get('suggested_standardized_name', discovery_data.get('label')),
                discovery_data.get('confidence', 1.0),
                discovery_data.get('status', 'pending'),
                discovery_data.get('format_detected'),
                discovery_data.get('value_format_detected'),
                1 if discovery_data.get('is_fixed_item', False) else 0,
                1 if discovery_data.get('auto_approved', False) else 0,
                discovery_data.get('loan_classification'),
                discovery_data.get('loan_type'),
                discovery_data.get('column_type'),
                discovery_data.get('source', 'auto_learning')
            )
        )

    def get_pending_items(self, session_id: str = None) -> List[Dict]:
        """Get pending items for approval"""
        if session_id:
            rows = self.execute_query(
                '''SELECT discovery_id, item_label, item_value, suggested_section,
                          suggested_standard_name, confidence_score, status,
                          format_detected, value_format_detected, is_fixed_item,
                          auto_approved, loan_classification, loan_type, column_type
                   FROM pending_items WHERE session_id = ? AND status = ?''',
                (session_id, 'pending_approval')
            )
        else:
            rows = self.execute_query(
                '''SELECT discovery_id, item_label, item_value, suggested_section,
                          suggested_standard_name, confidence_score, status,
                          format_detected, value_format_detected, is_fixed_item,
                          auto_approved, loan_classification, loan_type, column_type
                   FROM pending_items WHERE status = ?''',
                ('pending_approval',)
            )

        # Convert to the expected format
        result = []
        for row in rows:
            item = {
                'discovery_id': row['discovery_id'],
                'label': row['item_label'],
                'value': row['item_value'],
                'section': row['suggested_section'],
                'suggested_standardized_name': row['suggested_standard_name'],
                'confidence': row['confidence_score'],
                'status': row['status'],
                'format_detected': row['format_detected'],
                'value_format_detected': row['value_format_detected'],
                'is_fixed_item': bool(row['is_fixed_item']),
                'auto_approved': bool(row['auto_approved'])
            }

            # Add loan-specific fields if present
            if row['loan_classification']:
                item['loan_classification'] = row['loan_classification']
            if row['loan_type']:
                item['loan_type'] = row['loan_type']
            if row['column_type']:
                item['column_type'] = row['column_type']

            result.append(item)

        return result

    # Settings operations
    def save_setting(self, key: str, value: str, module_name: str = 'system'):
        """Save a system setting"""
        self.execute_update(
            '''INSERT OR REPLACE INTO system_settings
               (setting_key, setting_value, module_name, updated_at)
               VALUES (?, ?, ?, CURRENT_TIMESTAMP)''',
            (key, value, module_name)
        )

    def get_setting(self, key: str, module_name: str = 'system') -> Optional[str]:
        """Get a system setting"""
        rows = self.execute_query(
            'SELECT setting_value FROM system_settings WHERE setting_key = ? AND module_name = ?',
            (key, module_name)
        )
        return rows[0]['setting_value'] if rows else None

    def get_all_settings(self, module_name: str = 'system') -> Dict:
        """Get all settings for a module"""
        rows = self.execute_query(
            'SELECT setting_key, setting_value FROM system_settings WHERE module_name = ?',
            (module_name,)
        )
        return {row['setting_key']: row['setting_value'] for row in rows}

    # Report operations
    def save_report(self, report_id: str, report_name: str, report_type: str,
                   module_name: str, file_path: str, metadata: Dict = None):
        """Save a report record using correct schema"""
        # Map to correct schema columns
        report_category = self._determine_report_category(module_name)
        file_paths_json = json.dumps({"main": file_path}) if file_path else None

        self.execute_update(
            '''INSERT OR REPLACE INTO reports
               (report_id, report_type, report_category, title, description, file_paths, metadata)
               VALUES (?, ?, ?, ?, ?, ?, ?)''',
            (report_id, report_type, report_category, report_name,
             f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
             file_paths_json, json.dumps(metadata) if metadata else None)
        )

    def _determine_report_category(self, module_name: str) -> str:
        """Determine report category from module name"""
        if 'payroll' in module_name.lower() or 'audit' in module_name.lower():
            return 'Payroll Audit'
        elif 'pdf' in module_name.lower() or 'sort' in module_name.lower():
            return 'PDF Sorter'
        elif 'data' in module_name.lower() or 'builder' in module_name.lower():
            return 'Data Builder'
        elif 'bank' in module_name.lower() or 'adviser' in module_name.lower():
            return 'Bank Adviser'
        return 'Payroll Audit'  # Default

    def get_reports(self, module_name: str = None) -> List[Dict]:
        """Get reports by category"""
        if module_name:
            report_category = self._determine_report_category(module_name)
            return self.execute_query(
                'SELECT * FROM reports WHERE report_category = ? ORDER BY created_at DESC',
                (report_category,)
            )
        else:
            return self.execute_query(
                'SELECT * FROM reports ORDER BY created_at DESC'
            )

    def get_reports_by_type(self, report_type: str) -> List[Dict]:
        """Get reports by type"""
        return self.execute_query(
            'SELECT * FROM reports WHERE report_type = ? ORDER BY created_at DESC',
            (report_type,)
        )

    def save_comparison_results(self, session_id, comparison_data):
        """Save comparison results to database using structured columns.
        
        Args:
            session_id: Unique session ID
            comparison_data: Dictionary containing comparison results data
            
        Returns:
            session_id if successful, None otherwise
        """
        try:
            if not self.is_connected:
                self.connect()
                
            cursor = self.connection.cursor()
            
            # Note: comparison_results table is created in ensure_tables_exist() method
            
            # Create employee_changes table for individual employee changes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS employee_changes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    employee_id TEXT,
                    employee_name TEXT,
                    section TEXT,
                    item_name TEXT,
                    previous_value TEXT,
                    current_value TEXT,
                    difference TEXT,
                    change_type TEXT,
                    priority TEXT,
                    FOREIGN KEY (session_id) REFERENCES comparison_results(session_id)
                )
            ''')
            
            # Extract summary data - handle both wrapped and unwrapped formats
            if 'reportData' in comparison_data:
                # Wrapped format
                summary_data = comparison_data.get('reportData', {}).get('comparisonData', {}).get('summary', {})
                metadata = comparison_data.get('reportData', {}).get('metadata', {})
                employees_data = comparison_data.get('reportData', {}).get('comparisonData', {}).get('employees', [])
            else:
                # Direct format
                summary_data = comparison_data.get('comparisonData', {}).get('summary', {})
                metadata = comparison_data.get('metadata', {})
                employees_data = comparison_data.get('comparisonData', {}).get('employees', [])
            
            # Convert to text representation for backward compatibility during transition
            import pickle
            serialized_summary = pickle.dumps(summary_data)  # Using pickle instead of JSON
            
            # Save to main results table
            cursor.execute('''
                INSERT OR REPLACE INTO comparison_results
                (session_id, summary_data, total_employees, total_changes, employees_with_changes,
                 current_month, current_year, previous_month, previous_year, report_name, report_designation)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session_id,
                serialized_summary,  # Store structured summary data
                summary_data.get('total_employees_compared', 0),
                summary_data.get('total_changes', 0),
                summary_data.get('employees_with_changes', 0),
                metadata.get('current_month', ''),
                metadata.get('current_year', ''),
                metadata.get('previous_month', ''),
                metadata.get('previous_year', ''),
                metadata.get('report_name', ''),
                metadata.get('report_designation', '')
            ))
            
            # Save individual employee changes to the dedicated table
            for employee in employees_data:
                employee_id = employee.get('employee_id', 'Unknown')
                employee_name = employee.get('employee_name', employee.get('name', 'Unknown'))
                changes = employee.get('changes', [])
                
                for change in changes:
                    section = change.get('section', 'Unknown')
                    item_name = change.get('item_name', 'Unknown Item')
                    change_type = change.get('change_type', 'changed')
                    previous_value = str(change.get('previous_value', 'N/A'))
                    current_value = str(change.get('current_value', 'N/A'))
                    difference = str(change.get('difference', 'N/A'))
                    
                    # Determine priority
                    priority = 'low'
                    if section.upper() in ('PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS'):
                        priority = 'high'
                    elif section.upper() in ('LOANS', 'EMPLOYEE BANK DETAILS'):
                        priority = 'moderate'
                    
                    cursor.execute('''
                        INSERT INTO employee_changes
                        (session_id, employee_id, employee_name, section, item_name,
                         previous_value, current_value, difference, change_type, priority)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        session_id,
                        employee_id,
                        employee_name,
                        section,
                        item_name,
                        previous_value,
                        current_value,
                        difference,
                        'introduced' if change_type == 'added' else 'changed',
                        priority
                    ))
            
            self.connection.commit()
            print(f"[DATABASE] ✅ Comparison data saved to database with session_id: {session_id}")
            return session_id
            
        except Exception as e:
            print(f"[DATABASE] ❌ Error saving comparison results: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def load_comparison_results(self, session_id):
        """Load comparison results from database using structured approach.
        
        Args:
            session_id: Unique session ID
            
        Returns:
            Dictionary with structured comparison data if successful, empty dict otherwise
        """
        try:
            if not self.is_connected:
                self.connect()
                
            cursor = self.connection.cursor()
            
            # Load comparison metadata from main table
            cursor.execute('''
                SELECT session_id, summary_data, total_employees, total_changes, employees_with_changes,
                       current_month, current_year, previous_month, previous_year, report_name, report_designation
                FROM comparison_results
                WHERE session_id = ?
            ''', (session_id,))
            
            result = cursor.fetchone()
            
            if not result:
                print(f"[DATABASE] ⚠️ No comparison data found for session: {session_id}")
                return {'data': {}, 'metadata': {}, 'message': f"No comparison data found for session: {session_id}"}
            
            # Store the session ID for future reference
            self.last_comparison_session_id = session_id
            
            # Extract metadata fields
            metadata = {
                'session_id': result[0],
                'total_employees': result[2],
                'total_changes': result[3],
                'employees_with_changes': result[4],
                'current_month': result[5],
                'current_year': result[6],
                'previous_month': result[7],
                'previous_year': result[8],
                'report_name': result[9],
                'report_designation': result[10]
            }
            
            # Load employee changes from the dedicated table
            cursor.execute('''
                SELECT employee_id, employee_name, section, item_name, previous_value, 
                       current_value, difference, change_type, priority
                FROM employee_changes
                WHERE session_id = ?
                ORDER BY priority DESC, employee_id, section
            ''', (session_id,))
            
            changes_rows = cursor.fetchall()
            
            # Organize changes by employee
            employees_data = {}
            for row in changes_rows:
                employee_id = row[0]
                
                if employee_id not in employees_data:
                    employees_data[employee_id] = {
                        'employee_id': employee_id,
                        'employee_name': row[1],
                        'changes': []
                    }
                
                change = {
                    'section': row[2],
                    'item_name': row[3], 
                    'previous_value': row[4],
                    'current_value': row[5],
                    'difference': row[6],
                    'change_type': 'added' if row[7] == 'introduced' else 'changed',
                    'priority': row[8]
                }
                
                employees_data[employee_id]['changes'].append(change)
            
            # Convert to list of employees
            employees_list = list(employees_data.values())
            
            # Deserialize summary data
            try:
                import pickle
                summary_data = pickle.loads(result[1]) if result[1] else {}
            except Exception as e:
                print(f"[DATABASE] ⚠️ Error deserializing summary data: {e}")
                summary_data = {
                    'total_employees_compared': metadata['total_employees'],
                    'total_changes': metadata['total_changes'],
                    'employees_with_changes': metadata['employees_with_changes']
                }
            
            # Build structured comparison data
            comparison_data = {
                'comparisonData': {
                    'summary': summary_data,
                    'employees': employees_list
                },
                'metadata': metadata
            }
            
            print(f"[DATABASE] ✅ Loaded structured comparison data from session: {session_id}")
            
            return {'data': comparison_data, 'metadata': metadata}
                
        except Exception as e:
            print(f"[DATABASE] ❌ Error loading comparison results: {e}")
            import traceback
            traceback.print_exc()
            return {'data': {}, 'metadata': {}, 'error': str(e)}
    
    def load_latest_comparison_results(self):
        """Load the most recent comparison data from database
    
        Returns:
            Dictionary with comparison data if successful, empty dictionary otherwise
        """
        try:
            # Note: comparison_results table is created in ensure_tables_exist() method
            
            # Try to get the most recent record first, regardless of schema
            try:
                # Try with comparison_data first
                rows = self.execute_query('''
                    SELECT session_id, comparison_data, total_employees, total_changes, employees_with_changes,
                           current_month, current_year, previous_month, previous_year, report_name, report_designation
                    FROM comparison_results 
                    ORDER BY created_at DESC
                    LIMIT 1
                ''')
                has_comparison_data = True
                print(f"[DATABASE] Using standard query with comparison_data column")
            except Exception as column_error:
                # If that fails, try without comparison_data
                print(f"[DATABASE] ⚠️ comparison_data column not found, using alternative query")
                rows = self.execute_query('''
                    SELECT session_id, total_employees, total_changes, employees_with_changes,
                           current_month, current_year, previous_month, previous_year, report_name, report_designation
                    FROM comparison_results 
                    ORDER BY created_at DESC
                    LIMIT 1
                ''')
                has_comparison_data = False
            
            if rows and len(rows) > 0:
                print(f"[DATABASE] ✅ Found latest comparison data in database")
                try:
                    # Build metadata regardless of data structure
                    metadata = {'session_id': rows[0][0]}
                    
                    # If comparison_data exists, parse it
                    if has_comparison_data:
                        comparison_data_str = rows[0][1]
                        parsed_data = json.loads(comparison_data_str) if comparison_data_str else {}
                        
                        # Adjust indexes for other metadata fields
                        offset = 2
                    else:
                        # Create empty data structure
                        parsed_data = {}
                        offset = 1
                    
                    # Add remaining metadata fields with adjusted indexes if they exist
                    try:
                        metadata.update({
                            'total_employees': rows[0][offset] if len(rows[0]) > offset else 0,
                            'total_changes': rows[0][offset+1] if len(rows[0]) > offset+1 else 0,
                            'employees_with_changes': rows[0][offset+2] if len(rows[0]) > offset+2 else 0,
                            'current_month': rows[0][offset+3] if len(rows[0]) > offset+3 else '',
                            'current_year': rows[0][offset+4] if len(rows[0]) > offset+4 else '',
                            'previous_month': rows[0][offset+5] if len(rows[0]) > offset+5 else '',
                            'previous_year': rows[0][offset+6] if len(rows[0]) > offset+6 else '',
                            'report_name': rows[0][offset+7] if len(rows[0]) > offset+7 else '',
                            'report_designation': rows[0][offset+8] if len(rows[0]) > offset+8 else ''
                        })
                    except IndexError:
                        print(f"[DATABASE] ⚠️ Some metadata fields missing from database record")
                    
                    # Return as structured data with metadata
                    return {'data': parsed_data, 'metadata': metadata}
                except Exception as parse_error:
                    print(f"[DATABASE] ⚠️ Error parsing data structure: {parse_error}")
                    # Return structured error object
                    return {
                        'data': {}, 
                        'metadata': {},
                        'error': 'Data structure parse error',
                        'message': str(parse_error)
                    }
            else:
                print(f"[DATABASE] ⚠️ No comparison data found in database")
                # Return empty structured data
                return {'data': {}, 'metadata': {}, 'message': "No comparison data found"}
                
        except Exception as e:
            print(f"[DATABASE] ❌ Error loading latest comparison results: {e}")
            import traceback
            traceback.print_exc()
            # Return Python dict instead of json.dumps string
            return {"comparison_results": [], "error": str(e), "message": "Database error occurred"}
    
    def save_report(self, report_type, report_path, metadata={}):
        """Save report to database with report type differentiation.

        Args:
            report_type: Type of report ('comparison' or 'final')
            report_path: Path to the report file
            metadata: Dictionary of metadata

        Returns:
            report_id if successful, None otherwise
        """
        try:
            if not self.is_connected:
                self.connect()
                
            cursor = self.connection.cursor()
            
            # Generate unique report ID
            import time
            report_id = f"{report_type}_report_{int(time.time())}"
            
            # Ensure report_type is valid
            if report_type not in ['comparison', 'final']:
                report_type = 'final'  # Default to final if invalid
                
            # Extract and format metadata
            report_name = metadata.get('report_name', 'Payroll Audit Report')
            file_type = metadata.get('format', 'unknown')
            report_category = 'Payroll Audit'
            description = f"{report_type.capitalize()} Report - Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # Format file paths as JSON
            file_paths = json.dumps({file_type: report_path})
            
            # Format title professionally
            title = f"{report_name} - {report_type.capitalize()} Report"
            if 'current_month' in metadata and 'current_year' in metadata:
                title += f" ({metadata.get('current_month')}/{metadata.get('current_year')})"
            
            # Save to database using the unified schema
            cursor.execute('''
                INSERT INTO reports
                (report_id, report_type, report_category, title, description, file_paths, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                report_id,
                report_type,
                report_category,
                title,
                description,
                file_paths,
                json.dumps(metadata)
            ))
            
            self.connection.commit()
            print(f"[DATABASE] ✅ {report_type.capitalize()} report saved to database: {report_id} ({file_type})")
            return report_id
            
        except Exception as e:
            print(f"[DATABASE] ❌ Error saving report: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_reports_by_type(self, report_type):
        """Get reports by type.
        
        Args:
            report_type: Type of report ('comparison' or 'final')
            
        Returns:
            List of reports matching the specified type
        """
        try:
            if not self.is_connected:
                self.connect()
                
            cursor = self.connection.cursor()
            
            # Query reports by type using the unified schema
            cursor.execute('''
                SELECT report_id, report_type, report_category, title, description, file_paths, metadata, created_at
                FROM reports
                WHERE report_type = ?
                ORDER BY created_at DESC
            ''', (report_type,))
            
            results = cursor.fetchall()
            reports = []
            
            for row in results:
                # Parse file paths JSON
                file_paths = json.loads(row[5]) if row[5] else {}
                
                # Parse metadata
                try:
                    metadata = json.loads(row[6]) if row[6] else {}
                except:
                    metadata = {}
                    
                # Create report object with professional description
                report = {
                    'report_id': row[0],
                    'report_type': row[1],
                    'category': row[2],
                    'title': row[3],
                    'description': row[4],
                    'file_paths': file_paths,
                    'metadata': metadata,
                    'created_at': row[7],
                }
                
                # Add specific paths if available
                for format_type, path in file_paths.items():
                    report[f'{format_type}_path'] = path
                
                reports.append(report)
                
            print(f"[DATABASE] ✅ Retrieved {len(reports)} {report_type} reports")
            return reports
            
        except Exception as e:
            print(f"[DATABASE] ❌ Error retrieving {report_type} reports: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def close_connection(self):
        """Close the database connection."""
        if hasattr(self, 'conn') and self.conn:
            self.conn.close()
            self.is_connected = False

    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.is_connected = False

# Singleton instance
_db_instance = None

def get_database_instance() -> PythonDatabaseManager:
    """Get singleton database instance"""
    global _db_instance
    if _db_instance is None:
        _db_instance = PythonDatabaseManager()
    return _db_instance
