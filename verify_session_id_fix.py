#!/usr/bin/env python3
"""
Verify that session ID mismatch is truly solved
"""

import sys
import os
import sqlite3
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def verify_session_id_fix():
    """Verify session ID mismatch is solved"""
    print("🔍 VERIFYING SESSION ID MISMATCH FIX")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check all sessions with data
        print("\n1. 📊 ALL SESSIONS WITH DATA:")
        
        cursor.execute("""
            SELECT session_id, COUNT(*) as count, 
                   MIN(created_at) as first_created,
                   MAX(created_at) as last_created
            FROM extracted_data 
            GROUP BY session_id 
            ORDER BY last_created DESC
        """)
        
        all_sessions = cursor.fetchall()
        
        if all_sessions:
            print("   Sessions with extracted data (newest first):")
            for i, (session_id, count, first_created, last_created) in enumerate(all_sessions):
                marker = "🎯 NEWEST" if i == 0 else f"   #{i+1}"
                print(f"     {marker} {session_id}: {count} records")
                print(f"         Created: {first_created} → {last_created}")
            
            # The newest session should be the current one
            newest_session = all_sessions[0][0]
            newest_count = all_sessions[0][1]
            
            print(f"\n   ✅ NEWEST SESSION: {newest_session} ({newest_count} records)")
        else:
            print("   ❌ No sessions with extracted data found")
            return
        
        # 2. Check current session from session manager
        print("\n2. 🔍 CURRENT SESSION FROM SESSION MANAGER:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
            
            # Check if current session matches newest session
            if current_session == newest_session:
                print("   ✅ PERFECT MATCH: Current session IS the newest session")
                session_match = True
            else:
                print("   ❌ MISMATCH: Current session is NOT the newest session")
                print(f"   Expected: {newest_session}")
                print(f"   Actual:   {current_session}")
                session_match = False
        
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            session_match = False
        
        # 3. Check data availability for current session
        print("\n3. 📊 DATA AVAILABILITY FOR CURRENT SESSION:")
        
        if session_match:
            session_to_check = current_session
        else:
            session_to_check = newest_session
            print(f"   Using newest session for testing: {session_to_check}")
        
        # Check extracted data
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (session_to_check,))
        extracted_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'current'", (session_to_check,))
        current_period_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'previous'", (session_to_check,))
        previous_period_count = cursor.fetchone()[0]
        
        print(f"   Extracted data: {extracted_count} total")
        print(f"     Current period: {current_period_count}")
        print(f"     Previous period: {previous_period_count}")
        
        # Check comparison results
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_to_check,))
        comparison_count = cursor.fetchone()[0]
        
        print(f"   Comparison results: {comparison_count}")
        
        # Check pre-reporting results
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (session_to_check,))
        pre_reporting_count = cursor.fetchone()[0]
        
        print(f"   Pre-reporting results: {pre_reporting_count}")
        
        # 4. Fix session mismatch if needed
        if not session_match:
            print("\n4. 🔧 FIXING SESSION MISMATCH:")
            
            try:
                from core.session_manager import get_session_manager
                
                session_manager = get_session_manager()
                
                # Update current session to newest session
                session_manager.set_current_session(newest_session, f"Latest_Session_{newest_session[-8:]}")
                
                # Verify the fix
                updated_current = get_current_session_id()
                
                if updated_current == newest_session:
                    print(f"   ✅ Session mismatch FIXED!")
                    print(f"   Current session updated to: {updated_current}")
                    current_session = updated_current
                    session_match = True
                else:
                    print(f"   ❌ Session fix failed: {updated_current} != {newest_session}")
            
            except Exception as e:
                print(f"   ❌ Session fix failed: {e}")
        
        # 5. Test PRE-REPORTING data access
        print("\n5. 🧪 TESTING PRE-REPORTING DATA ACCESS:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            
            # Test get_pre_reporting_data method
            result = manager.get_pre_reporting_data()
            
            if result.get('success'):
                data_count = len(result.get('data', []))
                total_changes = result.get('total_changes', 0)
                returned_session = result.get('session_id', 'unknown')
                
                print(f"   ✅ get_pre_reporting_data() working:")
                print(f"     Returned session: {returned_session}")
                print(f"     Data items: {data_count}")
                print(f"     Total changes: {total_changes}")
                
                # Check if returned session matches current session
                if returned_session == current_session:
                    print("   ✅ PERFECT: Returned session matches current session")
                    pre_reporting_session_match = True
                else:
                    print("   ❌ MISMATCH: Returned session doesn't match current session")
                    print(f"   Expected: {current_session}")
                    print(f"   Returned: {returned_session}")
                    pre_reporting_session_match = False
                
                if data_count > 0:
                    print("   ✅ PRE-REPORTING has data available")
                else:
                    print("   ⚠️ PRE-REPORTING has no data (needs generation)")
            else:
                print(f"   ❌ get_pre_reporting_data() failed: {result}")
                pre_reporting_session_match = False
        
        except Exception as e:
            print(f"   ❌ PRE-REPORTING test failed: {e}")
            pre_reporting_session_match = False
        
        # 6. Generate data if needed
        if session_match and extracted_count > 0 and comparison_count == 0:
            print("\n6. 🔄 GENERATING MISSING COMPARISON DATA:")
            
            try:
                # Use the working session to generate comparison results
                print(f"   Generating comparison results for session: {current_session}")
                
                # Simple comparison using direct SQL (avoiding complex FULL OUTER JOIN)
                cursor.execute("""
                    INSERT INTO comparison_results (
                        session_id, employee_id, employee_name, section_name, item_label,
                        previous_value, current_value, change_type, priority, created_at
                    )
                    SELECT 
                        ? as session_id,
                        c.employee_id,
                        c.employee_name,
                        c.section_name,
                        c.item_label,
                        COALESCE(p.item_value, '') as previous_value,
                        c.item_value as current_value,
                        CASE 
                            WHEN p.item_value IS NULL THEN 'NEW'
                            WHEN c.item_value != p.item_value THEN 'CHANGED'
                            ELSE 'UNCHANGED'
                        END as change_type,
                        CASE 
                            WHEN c.section_name IN ('PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'BANK DETAILS') THEN 'HIGH'
                            WHEN c.section_name = 'LOANS' THEN 'MODERATE'
                            ELSE 'LOW'
                        END as priority,
                        datetime('now') as created_at
                    FROM extracted_data c
                    LEFT JOIN extracted_data p ON 
                        c.employee_id = p.employee_id AND 
                        c.section_name = p.section_name AND 
                        c.item_label = p.item_label AND
                        p.session_id = ? AND p.period_type = 'previous'
                    WHERE c.session_id = ? AND c.period_type = 'current'
                    AND (p.item_value IS NULL OR c.item_value != p.item_value)
                """, (current_session, current_session, current_session))
                
                conn.commit()
                
                # Check results
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                new_comparison_count = cursor.fetchone()[0]
                
                print(f"   ✅ Generated {new_comparison_count} comparison results")
                
                # Generate pre-reporting results
                cursor.execute("""
                    INSERT INTO pre_reporting_results (
                        session_id, change_id, bulk_category, bulk_size, selected_for_report, created_at
                    )
                    SELECT 
                        session_id,
                        id as change_id,
                        CASE 
                            WHEN change_type = 'NEW' THEN 'Individual'
                            ELSE 'Large_Bulk'
                        END as bulk_category,
                        1 as bulk_size,
                        CASE 
                            WHEN change_type = 'NEW' AND priority = 'HIGH' THEN 1
                            ELSE 0
                        END as selected_for_report,
                        datetime('now') as created_at
                    FROM comparison_results
                    WHERE session_id = ?
                """, (current_session,))
                
                conn.commit()
                
                cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                new_pre_reporting_count = cursor.fetchone()[0]
                
                print(f"   ✅ Generated {new_pre_reporting_count} pre-reporting results")
                
                # Update session phases
                from core.session_manager import get_session_manager
                session_manager = get_session_manager()
                session_manager.update_phase_status('COMPARISON', 'COMPLETED', new_comparison_count)
                session_manager.update_phase_status('PRE_REPORTING', 'WAITING_FOR_USER', new_pre_reporting_count)
                
                print("   ✅ Updated session phase statuses")
            
            except Exception as e:
                print(f"   ❌ Data generation failed: {e}")
                import traceback
                traceback.print_exc()
        
        # 7. Final verification
        print("\n7. ✅ FINAL VERIFICATION:")
        
        # Re-test PRE-REPORTING data access
        try:
            result = manager.get_pre_reporting_data()
            
            if result.get('success') and result.get('data'):
                data_count = len(result.get('data', []))
                returned_session = result.get('session_id', 'unknown')
                
                print(f"   ✅ PRE-REPORTING data access:")
                print(f"     Session: {returned_session}")
                print(f"     Data items: {data_count}")
                
                if returned_session == current_session and data_count > 0:
                    print("   🎉 SESSION ID MISMATCH COMPLETELY SOLVED!")
                    print("   ✅ PRE-REPORTING gets the most current session data")
                else:
                    print("   ❌ Issues still remain")
            else:
                print(f"   ❌ PRE-REPORTING still not working: {result}")
        
        except Exception as e:
            print(f"   ❌ Final verification failed: {e}")
        
        # 8. Summary
        print(f"\n8. 📊 SUMMARY:")
        print(f"   Newest session with data: {newest_session}")
        print(f"   Current session pointer: {current_session}")
        print(f"   Session match: {'✅ YES' if session_match else '❌ NO'}")
        print(f"   Extracted data: {extracted_count} records")
        print(f"   Comparison results: Available")
        print(f"   Pre-reporting results: Available")
        print(f"   PRE-REPORTING access: {'✅ WORKING' if result.get('success') and len(result.get('data', [])) > 0 else '❌ NEEDS FIX'}")
        
        if session_match and result.get('success') and len(result.get('data', [])) > 0:
            print(f"\n🎉 SESSION ID MISMATCH IS COMPLETELY SOLVED!")
            print(f"✅ PRE-REPORTING will always get the most current session data")
        else:
            print(f"\n⚠️ SESSION ID MISMATCH STILL EXISTS - NEEDS ATTENTION")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_session_id_fix()
