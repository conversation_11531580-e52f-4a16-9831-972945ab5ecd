#!/usr/bin/env python3
"""
Comprehensive production readiness check
"""

import sqlite3
import sys
import os
sys.path.append('.')

def production_readiness_check():
    """Check if system is ready for full production end-to-end run"""
    print("🔍 COMPREHENSIVE PRODUCTION READINESS CHECK")
    print("=" * 70)
    
    issues_found = []
    warnings = []
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # CHECK 1: Database Schema Integrity
        print("1. 📋 DATABASE SCHEMA INTEGRITY:")
        
        required_tables = [
            'audit_sessions', 'current_session', 'extracted_data', 
            'comparison_results', 'session_phases', 'session_metadata',
            'session_guidance_log', 'session_consolidation_rules'
        ]
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if cursor.fetchone():
                print(f"   ✅ {table} table exists")
            else:
                issues_found.append(f"Missing table: {table}")
                print(f"   ❌ {table} table missing")
        
        # CHECK 2: Session Management
        print("\n2. 🎯 SESSION MANAGEMENT:")
        
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session_result = cursor.fetchone()
        
        if current_session_result:
            current_session = current_session_result[0]
            print(f"   ✅ Current session: {current_session}")
            
            # Check session status
            cursor.execute('SELECT status FROM audit_sessions WHERE session_id = ?', (current_session,))
            session_status_result = cursor.fetchone()
            
            if session_status_result:
                session_status = session_status_result[0]
                print(f"   ✅ Session status: {session_status}")
            else:
                issues_found.append("Current session not found in audit_sessions table")
                print(f"   ❌ Current session not found in audit_sessions")
        else:
            issues_found.append("No current session found")
            print(f"   ❌ No current session found")
        
        # CHECK 3: Data Persistence
        print("\n3. 💾 DATA PERSISTENCE:")
        
        if current_session_result:
            # Check extracted data
            cursor.execute('SELECT COUNT(*) FROM extracted_data WHERE session_id = ?', (current_session,))
            extracted_count = cursor.fetchone()[0]
            print(f"   Extracted data: {extracted_count} records")
            
            # Check comparison results
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (current_session,))
            comparison_count = cursor.fetchone()[0]
            print(f"   Comparison results: {comparison_count} records")
            
            if extracted_count > 0:
                print(f"   ✅ Extracted data present")
            else:
                warnings.append("No extracted data - will need extraction or bypass")
                print(f"   ⚠️ No extracted data")
            
            if comparison_count > 0:
                print(f"   ✅ Comparison data present")
            else:
                warnings.append("No comparison data - will need comparison phase")
                print(f"   ⚠️ No comparison data")
        
        # CHECK 4: Database Manager Safety
        print("\n4. 🛡️ DATABASE MANAGER SAFETY:")
        
        # Check if PythonDatabaseManager still has dangerous DROP statements
        try:
            with open('core/python_database_manager.py', 'r') as f:
                content = f.read()
                
            if 'DROP TABLE IF EXISTS comparison_results' in content and not content.count('# self.connection.execute("DROP TABLE IF EXISTS comparison_results")'):
                issues_found.append("PythonDatabaseManager still has active DROP TABLE statement")
                print(f"   ❌ Dangerous DROP TABLE statement still active")
            else:
                print(f"   ✅ DROP TABLE statement safely commented out")
        except Exception as e:
            warnings.append(f"Could not check PythonDatabaseManager: {e}")
        
        # CHECK 5: API Endpoints
        print("\n5. 🔌 API ENDPOINTS:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            manager = PhasedProcessManager(debug_mode=False)
            
            # Test get_pre_reporting_data
            result = manager.get_pre_reporting_data(current_session)
            if result.get('success'):
                print(f"   ✅ get_pre_reporting_data API working")
            else:
                issues_found.append("get_pre_reporting_data API failing")
                print(f"   ❌ get_pre_reporting_data API failing: {result.get('error')}")
        except Exception as e:
            issues_found.append(f"API test failed: {e}")
            print(f"   ❌ API test failed: {e}")
        
        # CHECK 6: Phase Management
        print("\n6. 📊 PHASE MANAGEMENT:")
        
        if current_session_result:
            cursor.execute('SELECT phase_name, status, data_count FROM session_phases WHERE session_id = ?', (current_session,))
            phases = cursor.fetchall()
            
            if phases:
                print(f"   Phase statuses:")
                for phase_name, status, data_count in phases:
                    print(f"      {phase_name}: {status} ({data_count} items)")
                print(f"   ✅ Phase tracking active")
            else:
                warnings.append("No phase tracking data")
                print(f"   ⚠️ No phase tracking data")
        
        # CHECK 7: Session Protection
        print("\n7. 🛡️ SESSION PROTECTION:")
        
        cursor.execute('SELECT COUNT(*) FROM session_guidance_log')
        guidance_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM session_consolidation_rules')
        rules_count = cursor.fetchone()[0]
        
        if guidance_count > 0:
            print(f"   ✅ Session guidance active ({guidance_count} log entries)")
        else:
            warnings.append("No session guidance activity")
            print(f"   ⚠️ No session guidance activity")
        
        if rules_count >= 4:
            print(f"   ✅ Consolidation rules active ({rules_count} rules)")
        else:
            warnings.append("Insufficient consolidation rules")
            print(f"   ⚠️ Insufficient consolidation rules")
        
        conn.close()
        
        # FINAL ASSESSMENT
        print("\n" + "=" * 70)
        print("🎯 PRODUCTION READINESS ASSESSMENT")
        print("=" * 70)
        
        if not issues_found:
            if not warnings:
                print("🎉 SYSTEM FULLY PRODUCTION READY!")
                print("   ✅ All critical issues resolved")
                print("   ✅ All systems operational")
                print("   ✅ Ready for full end-to-end production run")
                return "FULLY_READY"
            else:
                print("✅ SYSTEM PRODUCTION READY WITH MINOR WARNINGS")
                print("   ✅ All critical issues resolved")
                print("   ⚠️ Minor warnings present (see below)")
                print("   ✅ Ready for production run with monitoring")
                
                print(f"\n⚠️ WARNINGS ({len(warnings)}):")
                for i, warning in enumerate(warnings, 1):
                    print(f"   {i}. {warning}")
                
                return "READY_WITH_WARNINGS"
        else:
            print("❌ SYSTEM NOT READY FOR PRODUCTION")
            print("   ❌ Critical issues found")
            print("   ❌ Requires fixes before production use")
            
            print(f"\n❌ CRITICAL ISSUES ({len(issues_found)}):")
            for i, issue in enumerate(issues_found, 1):
                print(f"   {i}. {issue}")
            
            if warnings:
                print(f"\n⚠️ ADDITIONAL WARNINGS ({len(warnings)}):")
                for i, warning in enumerate(warnings, 1):
                    print(f"   {i}. {warning}")
            
            return "NOT_READY"
        
    except Exception as e:
        print(f"❌ Production readiness check failed: {e}")
        import traceback
        traceback.print_exc()
        return "CHECK_FAILED"

def provide_production_recommendations(status):
    """Provide recommendations based on production readiness status"""
    print("\n" + "=" * 70)
    print("💡 PRODUCTION RECOMMENDATIONS")
    print("=" * 70)
    
    if status == "FULLY_READY":
        print("🚀 READY FOR FULL PRODUCTION RUN:")
        print("   1. Run complete end-to-end audit process")
        print("   2. Test with real production PDF files")
        print("   3. Verify all phases complete successfully")
        print("   4. Generate and review final reports")
        print("   5. Monitor system performance and memory usage")
        
    elif status == "READY_WITH_WARNINGS":
        print("✅ READY FOR PRODUCTION WITH MONITORING:")
        print("   1. Proceed with production run")
        print("   2. Monitor warnings closely")
        print("   3. Have backup/recovery plan ready")
        print("   4. Test with smaller datasets first")
        print("   5. Address warnings in next maintenance cycle")
        
    elif status == "NOT_READY":
        print("🔧 FIXES REQUIRED BEFORE PRODUCTION:")
        print("   1. Address all critical issues listed above")
        print("   2. Re-run production readiness check")
        print("   3. Test with small datasets first")
        print("   4. Verify fixes with comprehensive testing")
        
    else:
        print("⚠️ UNABLE TO ASSESS READINESS:")
        print("   1. Check system logs for errors")
        print("   2. Verify database connectivity")
        print("   3. Ensure all dependencies are installed")
        print("   4. Re-run readiness check")

if __name__ == "__main__":
    status = production_readiness_check()
    provide_production_recommendations(status)
