#!/usr/bin/env python3
"""
Debug why there are no NEW items in comparison results
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_new_items_issue():
    """Debug why there are no NEW items"""
    print("🔍 DEBUGGING NEW ITEMS ISSUE")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check change types in comparison results
        print("\n1. 📊 CHANGE TYPES IN COMPARISON RESULTS:")
        cursor.execute("""
            SELECT change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
            GROUP BY change_type
            ORDER BY count DESC
        """, (current_session,))
        
        change_types = cursor.fetchall()
        for row in change_types:
            print(f"   {row[0]}: {row[1]} items")
        
        # 2. Check for loan-related items
        print("\n2. 📊 LOAN-RELATED ITEMS:")
        cursor.execute("""
            SELECT section_name, item_label, change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND section_name LIKE '%LOAN%'
            GROUP BY section_name, item_label, change_type
            ORDER BY count DESC
            LIMIT 10
        """, (current_session,))
        
        loan_items = cursor.fetchall()
        if loan_items:
            print("   Loan items found:")
            for row in loan_items:
                print(f"     {row[0]}.{row[1]} ({row[2]}): {row[3]} employees")
        else:
            print("   ❌ No loan items found")
        
        # 3. Check for motor vehicle items
        print("\n3. 📊 MOTOR VEHICLE ITEMS:")
        cursor.execute("""
            SELECT section_name, item_label, change_type, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ? AND (
                item_label LIKE '%MOTOR VEH%' OR 
                item_label LIKE '%MOTOR VEHICLE%'
            )
            GROUP BY section_name, item_label, change_type
            ORDER BY count DESC
            LIMIT 10
        """, (current_session,))
        
        motor_items = cursor.fetchall()
        if motor_items:
            print("   Motor vehicle items found:")
            for row in motor_items:
                print(f"     {row[0]}.{row[1]} ({row[2]}): {row[3]} employees")
        else:
            print("   ❌ No motor vehicle items found")
        
        # 4. Check what sections exist
        print("\n4. 📊 SECTIONS IN COMPARISON RESULTS:")
        cursor.execute("""
            SELECT section_name, COUNT(*) as count
            FROM comparison_results 
            WHERE session_id = ?
            GROUP BY section_name
            ORDER BY count DESC
        """, (current_session,))
        
        sections = cursor.fetchall()
        for row in sections:
            print(f"   {row[0]}: {row[1]} items")
        
        # 5. Look for items that should be trackable
        print("\n5. 📊 POTENTIALLY TRACKABLE ITEMS:")
        
        # Look for any items with "LOAN" in section name
        cursor.execute("""
            SELECT DISTINCT section_name, item_label, change_type
            FROM comparison_results 
            WHERE session_id = ? AND (
                section_name LIKE '%LOAN%' OR
                item_label LIKE '%LOAN%' OR
                item_label LIKE '%ADVANCE%' OR
                item_label LIKE '%MOTOR%'
            )
            ORDER BY section_name, item_label
            LIMIT 20
        """, (current_session,))
        
        trackable_items = cursor.fetchall()
        if trackable_items:
            print("   Potentially trackable items:")
            for row in trackable_items:
                print(f"     {row[0]}.{row[1]} ({row[2]})")
        else:
            print("   ❌ No potentially trackable items found")
        
        # 6. Test the comparison logic to see why no NEW items
        print("\n6. 🔄 TESTING COMPARISON LOGIC:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Load extracted data
            print("   Loading extracted data...")
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                
                # Sample a few employees to check their data
                print("   Sample current employee data:")
                sample_employee = current_data[0]
                print(f"     Employee: {sample_employee.get('employee_id')} - {sample_employee.get('employee_name')}")
                print(f"     Sections: {list(sample_employee.get('sections', {}).keys())}")
                
                # Check if LOANS section exists
                if 'LOANS' in sample_employee.get('sections', {}):
                    loans_section = sample_employee['sections']['LOANS']
                    print(f"     LOANS section items: {list(loans_section.keys())[:5]}")
                else:
                    print("     ❌ No LOANS section found")
                
                # Check previous employee data
                print("   Sample previous employee data:")
                prev_sample = None
                for emp in previous_data:
                    if emp.get('employee_id') == sample_employee.get('employee_id'):
                        prev_sample = emp
                        break
                
                if prev_sample:
                    print(f"     Previous employee found: {prev_sample.get('employee_id')}")
                    print(f"     Previous sections: {list(prev_sample.get('sections', {}).keys())}")
                    
                    # Compare LOANS sections
                    current_loans = sample_employee.get('sections', {}).get('LOANS', {})
                    previous_loans = prev_sample.get('sections', {}).get('LOANS', {})
                    
                    print(f"     Current LOANS items: {len(current_loans)}")
                    print(f"     Previous LOANS items: {len(previous_loans)}")
                    
                    # Look for NEW items
                    new_items = set(current_loans.keys()) - set(previous_loans.keys())
                    if new_items:
                        print(f"     NEW loan items found: {list(new_items)[:3]}")
                    else:
                        print("     ❌ No NEW loan items found for this employee")
                else:
                    print("     ❌ Previous employee not found")
            
        except Exception as e:
            print(f"   ❌ Comparison logic test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_new_items_issue()
