#!/usr/bin/env python3
"""
Fix NEW rule logic to properly compare individual employee payslips
"""

import sys
import os
import sqlite3
import re

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def extract_numeric_value(value_str):
    """Extract numeric value from string"""
    if not value_str:
        return 0.0
    
    # Remove commas and extract number
    clean_str = str(value_str).replace(',', '').strip()
    
    # Find number pattern
    match = re.search(r'[\d,]+\.?\d*', clean_str)
    if match:
        try:
            return float(match.group().replace(',', ''))
        except:
            return 0.0
    return 0.0

def get_employee_department(employee_data):
    """Extract department from employee data"""
    # Try to get department from personal details
    personal_details = employee_data.get('sections', {}).get('PERSONAL DETAILS', {})
    
    # Look for department-related fields
    dept_fields = ['DEPARTMENT', 'DEPT', 'DIVISION', 'UNIT']
    for field in dept_fields:
        if field in personal_details:
            return personal_details[field]
    
    # Default based on employee ID pattern
    emp_id = employee_data.get('employee_id', '')
    if emp_id.startswith('COP'):
        return 'POLICE'
    elif emp_id.startswith('MIN'):
        return 'MINISTRY'
    else:
        return 'UNKNOWN'

def fix_new_rule_logic():
    """Fix NEW rule logic for proper employee-level comparison"""
    print("🔧 FIXING NEW RULE LOGIC FOR EMPLOYEE-LEVEL COMPARISON")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Working with session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Clear existing tracker data to start fresh
        print("\n1. 🧹 CLEARING EXISTING TRACKER DATA:")
        cursor.execute("DELETE FROM in_house_loans WHERE source_session = ?", (current_session,))
        cursor.execute("DELETE FROM external_loans WHERE source_session = ?", (current_session,))
        cursor.execute("DELETE FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (current_session,))
        conn.commit()
        print("   ✅ Cleared existing data")
        
        # 2. Load extracted data for proper comparison
        print("\n2. 📊 LOADING EXTRACTED DATA FOR COMPARISON:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if not current_data or not previous_data:
                print("   ❌ Could not load extracted data")
                return
            
            print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
            
            # Create lookup for previous data
            previous_lookup = {emp['employee_id']: emp for emp in previous_data}
            
            # 3. Find NEW items at employee level
            print("\n3. 🔍 FINDING NEW ITEMS AT EMPLOYEE LEVEL:")
            
            new_loans = []
            new_motor_vehicles = []
            
            in_house_loan_types = manager._load_in_house_loan_types()
            print(f"   In-house loan types: {in_house_loan_types}")
            
            for current_emp in current_data:
                emp_id = current_emp.get('employee_id')
                emp_name = current_emp.get('employee_name')
                department = get_employee_department(current_emp)
                
                # Get previous employee data
                previous_emp = previous_lookup.get(emp_id)
                
                if not previous_emp:
                    continue  # Skip if no previous data
                
                # Check LOANS section for NEW items
                current_loans = current_emp.get('sections', {}).get('LOANS', {})
                previous_loans = previous_emp.get('sections', {}).get('LOANS', {})
                
                for loan_item, loan_value in current_loans.items():
                    # Check if this loan item is NEW (not in previous month)
                    if loan_item not in previous_loans:
                        # Focus on Balance B/F items for actual loan amounts
                        if 'BALANCE B/F' in loan_item:
                            loan_type = loan_item.replace(' - BALANCE B/F', '').strip()
                            loan_amount = extract_numeric_value(loan_value)
                            
                            if loan_amount > 0:  # Only track loans with actual amounts
                                # Classify as in-house or external
                                is_in_house = any(in_house_type.lower() in loan_type.lower() 
                                                for in_house_type in in_house_loan_types)
                                
                                new_loans.append({
                                    'employee_id': emp_id,
                                    'employee_name': emp_name,
                                    'department': department,
                                    'loan_type': loan_type,
                                    'loan_amount': loan_amount,
                                    'is_in_house': is_in_house,
                                    'period_month': '06',
                                    'period_year': '2025',
                                    'period_acquired': '2025-06'
                                })
                
                # Check for NEW motor vehicle allowances
                current_personal = current_emp.get('sections', {}).get('PERSONAL DETAILS', {})
                previous_personal = previous_emp.get('sections', {}).get('PERSONAL DETAILS', {})
                
                for item_label, item_value in current_personal.items():
                    # Check if this item is NEW and is motor vehicle related
                    if (item_label not in previous_personal and 
                        ('MOTOR VEH' in item_label or 'VEHICLE MAINT' in item_label)):
                        
                        allowance_amount = extract_numeric_value(item_value)
                        
                        if allowance_amount > 0:  # Only track allowances with actual amounts
                            new_motor_vehicles.append({
                                'employee_id': emp_id,
                                'employee_name': emp_name,
                                'department': department,
                                'allowance_type': item_label,
                                'allowance_amount': allowance_amount,
                                'period_month': '06',
                                'period_year': '2025',
                                'period_acquired': '2025-06'
                            })
            
            print(f"   ✅ Found {len(new_loans)} NEW loan items")
            print(f"   ✅ Found {len(new_motor_vehicles)} NEW motor vehicle allowances")
            
            # 4. Populate tables with correct data
            print("\n4. 📝 POPULATING TABLES WITH CORRECT DATA:")
            
            # Populate in-house loans
            in_house_count = 0
            external_count = 0
            
            for loan in new_loans:
                try:
                    if loan['is_in_house']:
                        cursor.execute("""
                            INSERT INTO in_house_loans 
                            (employee_no, employee_name, department, loan_type, loan_amount,
                             period_month, period_year, period_acquired, source_session, remarks)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            loan['employee_id'],
                            loan['employee_name'],
                            loan['department'],
                            loan['loan_type'],
                            loan['loan_amount'],
                            loan['period_month'],
                            loan['period_year'],
                            loan['period_acquired'],
                            current_session,
                            f"NEW loan - Balance B/F amount"
                        ))
                        in_house_count += 1
                    else:
                        cursor.execute("""
                            INSERT INTO external_loans 
                            (employee_no, employee_name, department, loan_type, loan_amount,
                             period_month, period_year, period_acquired, source_session, remarks)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            loan['employee_id'],
                            loan['employee_name'],
                            loan['department'],
                            loan['loan_type'],
                            loan['loan_amount'],
                            loan['period_month'],
                            loan['period_year'],
                            loan['period_acquired'],
                            current_session,
                            f"NEW loan - Balance B/F amount"
                        ))
                        external_count += 1
                    
                    if (in_house_count + external_count) <= 5:  # Show first 5
                        loan_category = "In-house" if loan['is_in_house'] else "External"
                        print(f"     ✅ {loan_category}: {loan['employee_id']} - {loan['loan_type']} = {loan['loan_amount']}")
                
                except Exception as e:
                    print(f"     ❌ Failed to insert loan for {loan['employee_id']}: {e}")
            
            # Populate motor vehicle maintenance
            motor_count = 0
            
            for motor in new_motor_vehicles:
                try:
                    cursor.execute("""
                        INSERT INTO motor_vehicle_maintenance 
                        (employee_no, employee_name, department, maintenance_amount,
                         period_month, period_year, period_acquired, source_session, remarks)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        motor['employee_id'],
                        motor['employee_name'],
                        motor['department'],
                        motor['allowance_amount'],
                        motor['period_month'],
                        motor['period_year'],
                        motor['period_acquired'],
                        current_session,
                        f"NEW allowance: {motor['allowance_type']}"
                    ))
                    motor_count += 1
                    
                    if motor_count <= 5:  # Show first 5
                        print(f"     ✅ Motor Vehicle: {motor['employee_id']} - {motor['allowance_type']} = {motor['allowance_amount']}")
                
                except Exception as e:
                    print(f"     ❌ Failed to insert motor vehicle for {motor['employee_id']}: {e}")
            
            conn.commit()
            
            print(f"\n   ✅ POPULATION COMPLETED:")
            print(f"     In-house loans: {in_house_count}")
            print(f"     External loans: {external_count}")
            print(f"     Motor vehicle allowances: {motor_count}")
            print(f"     Total NEW items: {in_house_count + external_count + motor_count}")
        
        except Exception as e:
            print(f"   ❌ Data processing failed: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 5. Verify data quality
        print("\n5. ✅ VERIFYING DATA QUALITY:")
        
        # Check for duplicates
        cursor.execute("""
            SELECT employee_no, loan_type, COUNT(*) as count
            FROM in_house_loans 
            WHERE source_session = ?
            GROUP BY employee_no, loan_type
            HAVING COUNT(*) > 1
        """, (current_session,))
        
        in_house_duplicates = cursor.fetchall()
        
        cursor.execute("""
            SELECT employee_no, loan_type, COUNT(*) as count
            FROM external_loans 
            WHERE source_session = ?
            GROUP BY employee_no, loan_type
            HAVING COUNT(*) > 1
        """, (current_session,))
        
        external_duplicates = cursor.fetchall()
        
        cursor.execute("""
            SELECT employee_no, remarks, COUNT(*) as count
            FROM motor_vehicle_maintenance 
            WHERE source_session = ?
            GROUP BY employee_no, remarks
            HAVING COUNT(*) > 1
        """, (current_session,))
        
        motor_duplicates = cursor.fetchall()
        
        total_duplicates = len(in_house_duplicates) + len(external_duplicates) + len(motor_duplicates)
        
        if total_duplicates == 0:
            print("   ✅ No duplicates found")
        else:
            print(f"   ⚠️ Found {total_duplicates} duplicate groups")
        
        # Check data completeness
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(department) as has_dept,
                COUNT(loan_amount) as has_amount,
                COUNT(remarks) as has_remarks
            FROM in_house_loans 
            WHERE source_session = ?
        """, (current_session,))
        
        in_house_quality = cursor.fetchone()
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(department) as has_dept,
                COUNT(maintenance_amount) as has_amount,
                COUNT(remarks) as has_remarks
            FROM motor_vehicle_maintenance 
            WHERE source_session = ?
        """, (current_session,))
        
        motor_quality = cursor.fetchone()
        
        print(f"   In-house loans data quality:")
        print(f"     Total: {in_house_quality[0]}")
        print(f"     Has department: {in_house_quality[1]}")
        print(f"     Has amount: {in_house_quality[2]}")
        print(f"     Has remarks: {in_house_quality[3]}")
        
        print(f"   Motor vehicle data quality:")
        print(f"     Total: {motor_quality[0]}")
        print(f"     Has department: {motor_quality[1]}")
        print(f"     Has amount: {motor_quality[2]}")
        print(f"     Has remarks: {motor_quality[3]}")
        
        # 6. Final summary
        print("\n6. 📊 FINAL SUMMARY:")
        
        cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (current_session,))
        final_in_house = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (current_session,))
        final_external = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (current_session,))
        final_motor = cursor.fetchone()[0]
        
        total_new_items = final_in_house + final_external + final_motor
        
        print(f"   ✅ CORRECTED NEW RULE RESULTS:")
        print(f"     In-house loans: {final_in_house} (with Balance B/F amounts)")
        print(f"     External loans: {final_external} (with Balance B/F amounts)")
        print(f"     Motor vehicle allowances: {final_motor} (with payable amounts)")
        print(f"     Total TRUE NEW items: {total_new_items}")
        
        if total_new_items > 0:
            print(f"\n🎉 NEW RULE LOGIC SUCCESSFULLY CORRECTED!")
            print("✅ Employee-level comparison working")
            print("✅ Balance B/F amounts used for loans")
            print("✅ Department and remarks populated")
            print("✅ Duplicates eliminated")
            print("✅ Only TRUE NEW items tracked")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_new_rule_logic()
