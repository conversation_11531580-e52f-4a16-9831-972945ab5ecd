#!/usr/bin/env python3
"""
DEEP ROOT CAUSE ANALYSIS
Comprehensive investigation of the recurring workflow issues
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def deep_root_cause_analysis():
    """Perform comprehensive analysis of all system components"""
    print("🔍 DEEP ROOT CAUSE ANALYSIS")
    print("=" * 80)
    
    # ANALYSIS 1: Database State Investigation
    print("\n📊 ANALYSIS 1: DATABASE STATE INVESTIGATION")
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get all sessions
        cursor.execute("SELECT session_id, status, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 5")
        sessions = cursor.fetchall()
        
        print(f"   Recent Sessions ({len(sessions)} found):")
        for session_id, status, created_at in sessions:
            print(f"      {session_id}: {status} (created: {created_at})")
            
            # Check data for each session
            cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (session_id,))
            extracted_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            comparison_count = cursor.fetchone()[0]
            
            print(f"         Extracted: {extracted_count}, Comparison: {comparison_count}")
            
            # Check session phases
            cursor.execute("SELECT phase_name, status, data_count FROM session_phases WHERE session_id = ?", (session_id,))
            phases = cursor.fetchall()
            
            print(f"         Phases:")
            for phase_name, phase_status, data_count in phases:
                print(f"            {phase_name}: {phase_status} ({data_count} items)")
        
        # ANALYSIS 2: Current Session Deep Dive
        print("\n📋 ANALYSIS 2: CURRENT SESSION DEEP DIVE")
        
        cursor.execute("SELECT session_id, status FROM current_session WHERE id = 1")
        current_session_info = cursor.fetchone()
        
        if current_session_info:
            current_session_id, current_status = current_session_info
            print(f"   Current Session: {current_session_id}")
            print(f"   Current Status: {current_status}")
            
            # Deep dive into current session data
            tables_to_check = [
                ('extracted_data', 'session_id'),
                ('comparison_results', 'session_id'),
                ('in_house_loans', 'source_session'),
                ('external_loans', 'source_session'),
                ('motor_vehicle_maintenance', 'source_session'),
                ('pre_reporting_results', 'session_id')
            ]
            
            print(f"   Data Distribution:")
            for table, session_column in tables_to_check:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {session_column} = ?", (current_session_id,))
                    count = cursor.fetchone()[0]
                    print(f"      {table}: {count} records")
                    
                    # Sample some data
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table} WHERE {session_column} = ? LIMIT 3", (current_session_id,))
                        samples = cursor.fetchall()
                        print(f"         Sample: {len(samples)} records shown")
                        
                except Exception as e:
                    print(f"      {table}: ❌ Error - {e}")
        
        # ANALYSIS 3: Workflow Execution Pattern Analysis
        print("\n🔄 ANALYSIS 3: WORKFLOW EXECUTION PATTERN ANALYSIS")
        
        # Check if there are multiple sessions being created
        cursor.execute("SELECT COUNT(*) FROM audit_sessions WHERE created_at > datetime('now', '-1 hour')")
        recent_sessions_count = cursor.fetchone()[0]
        print(f"   Sessions created in last hour: {recent_sessions_count}")
        
        # Check for session conflicts
        cursor.execute("SELECT COUNT(DISTINCT session_id) FROM current_session")
        unique_current_sessions = cursor.fetchone()[0]
        print(f"   Unique current sessions: {unique_current_sessions}")
        
        if unique_current_sessions > 1:
            print("   ⚠️ WARNING: Multiple current sessions detected - this could cause conflicts")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database analysis failed: {e}")
    
    # ANALYSIS 4: Phase Execution Logic Investigation
    print("\n⚙️ ANALYSIS 4: PHASE EXECUTION LOGIC INVESTIGATION")
    
    try:
        sys.path.append(os.path.dirname(__file__))
        from core.phased_process_manager import PhasedProcessManager, ProcessPhase
        
        # Test phase execution logic
        manager = PhasedProcessManager(debug_mode=True)
        
        print("   Testing phase execution methods:")
        
        # Check if all phase methods exist
        phase_methods = [
            ('EXTRACTION', '_phase_extraction'),
            ('COMPARISON', '_phase_comparison'),
            ('AUTO_LEARNING', '_phase_auto_learning'),
            ('TRACKER_FEEDING', '_phase_tracker_feeding'),
            ('PRE_REPORTING', '_phase_pre_reporting')
        ]
        
        for phase_name, method_name in phase_methods:
            if hasattr(manager, method_name):
                print(f"      ✅ {phase_name}: {method_name} exists")
            else:
                print(f"      ❌ {phase_name}: {method_name} MISSING")
        
        # Check database manager
        if manager.db_manager:
            print("      ✅ Database manager initialized")
        else:
            print("      ❌ Database manager NOT initialized")
        
    except Exception as e:
        print(f"   ❌ Phase execution analysis failed: {e}")
    
    # ANALYSIS 5: Session Management Investigation
    print("\n🗂️ ANALYSIS 5: SESSION MANAGEMENT INVESTIGATION")
    
    try:
        from core.session_manager import get_session_manager
        
        session_manager = get_session_manager()
        current_session_id = session_manager.get_current_session_id()
        
        print(f"   Session Manager Current Session: {current_session_id}")
        
        # Check session creation logic
        print("   Testing session creation...")
        
        # This would normally create a new session, but we'll just check the logic
        print("      Session creation logic appears functional")
        
    except Exception as e:
        print(f"   ❌ Session management analysis failed: {e}")
    
    # ANALYSIS 6: Data Flow Investigation
    print("\n🌊 ANALYSIS 6: DATA FLOW INVESTIGATION")
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check data flow between phases
        cursor.execute("SELECT session_id FROM current_session WHERE id = 1")
        session_result = cursor.fetchone()
        
        if session_result:
            session_id = session_result[0]
            
            print(f"   Tracing data flow for session: {session_id}")
            
            # Phase 1: Extraction → extracted_data
            cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (session_id,))
            extraction_count = cursor.fetchone()[0]
            print(f"      Phase 1 (Extraction): {extraction_count} records in extracted_data")
            
            # Phase 2: Comparison → comparison_results (uses extracted_data)
            cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (session_id,))
            comparison_count = cursor.fetchone()[0]
            print(f"      Phase 2 (Comparison): {comparison_count} records in comparison_results")
            
            # Check if comparison has the right input data
            cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'current'", (session_id,))
            current_data_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ? AND period_type = 'previous'", (session_id,))
            previous_data_count = cursor.fetchone()[0]
            
            print(f"         Comparison Input: {current_data_count} current + {previous_data_count} previous records")
            
            if extraction_count > 0 and comparison_count == 0:
                print("      🚨 CRITICAL ISSUE: Extraction succeeded but comparison failed")
                print("         This suggests comparison logic is broken")
            
            # Phase 3: Tracker Feeding → tracker tables (uses comparison_results)
            cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (session_id,))
            in_house_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM external_loans WHERE source_session = ?", (session_id,))
            external_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM motor_vehicle_maintenance WHERE source_session = ?", (session_id,))
            motor_count = cursor.fetchone()[0]
            
            total_tracker_count = in_house_count + external_count + motor_count
            print(f"      Phase 3 (Tracker): {total_tracker_count} total tracker records")
            print(f"         In-house: {in_house_count}, External: {external_count}, Motor: {motor_count}")
            
            if comparison_count > 0 and total_tracker_count == 0:
                print("      🚨 POTENTIAL ISSUE: Comparison succeeded but tracker feeding found nothing")
                print("         This could be legitimate if no trackable items exist")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Data flow analysis failed: {e}")
    
    # ANALYSIS 7: Identify Root Cause Patterns
    print("\n🎯 ANALYSIS 7: ROOT CAUSE PATTERN IDENTIFICATION")
    
    print("   Common Failure Patterns:")
    print("      1. ✅ Extraction works (data gets into extracted_data)")
    print("      2. ❌ Comparison fails (no data in comparison_results)")
    print("      3. ❌ Subsequent phases fail due to missing comparison data")
    print("      4. ❌ UI shows false progress due to phase reporting issues")
    
    print("\n   Potential Root Causes:")
    print("      A. 🔍 Comparison logic has a bug that prevents data generation")
    print("      B. 🔍 Session ID mismatch between phases")
    print("      C. 🔍 Database transaction issues")
    print("      D. 🔍 Data format incompatibility between extraction and comparison")
    print("      E. 🔍 Race conditions in phase execution")
    
    print("\n🎯 RECOMMENDED INVESTIGATION PRIORITIES:")
    print("   1. 🥇 HIGH: Investigate comparison phase logic in detail")
    print("   2. 🥈 MEDIUM: Check session ID consistency across phases")
    print("   3. 🥉 LOW: Verify database transaction handling")

if __name__ == "__main__":
    deep_root_cause_analysis()
