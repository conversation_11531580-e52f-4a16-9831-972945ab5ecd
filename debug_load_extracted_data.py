#!/usr/bin/env python3
"""
Debug the _load_extracted_data method specifically
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.phased_process_manager import PhasedProcessManager

def debug_load_extracted_data():
    """Debug the _load_extracted_data method step by step"""
    
    print("🔍 DEBUGGING _load_extracted_data METHOD")
    print("=" * 60)
    
    # Initialize the manager
    manager = PhasedProcessManager()
    
    # Get latest session
    sessions = manager.db_manager.execute_query("""
        SELECT session_id FROM audit_sessions 
        ORDER BY created_at DESC 
        LIMIT 1
    """)
    
    if not sessions:
        print("❌ No sessions found")
        return
    
    session_id = sessions[0]['session_id']
    manager.session_id = session_id
    print(f"📋 Testing session: {session_id}")
    
    # Test the raw database query first
    print("\n🔍 TESTING RAW DATABASE QUERY:")
    print("-" * 50)
    
    # Test current data query
    current_rows = manager.db_manager.execute_query(
        '''SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
           FROM extracted_data
           WHERE session_id = ? AND period_type = ?
           ORDER BY employee_id, section_name, item_label''',
        (session_id, 'current')
    )
    
    print(f"Raw current rows: {len(current_rows)}")
    
    if current_rows:
        print("Sample raw current rows:")
        for i, row in enumerate(current_rows[:5]):
            print(f"  {i+1}. {row}")
    
    # Test previous data query
    previous_rows = manager.db_manager.execute_query(
        '''SELECT employee_id, employee_name, section_name, item_label, item_value, numeric_value
           FROM extracted_data
           WHERE session_id = ? AND period_type = ?
           ORDER BY employee_id, section_name, item_label''',
        (session_id, 'previous')
    )
    
    print(f"Raw previous rows: {len(previous_rows)}")
    
    if previous_rows:
        print("Sample raw previous rows:")
        for i, row in enumerate(previous_rows[:5]):
            print(f"  {i+1}. {row}")
    
    # Test the grouping logic manually
    print("\n🔍 TESTING GROUPING LOGIC:")
    print("-" * 50)
    
    def debug_grouping(rows, period_name):
        """Debug the grouping logic step by step"""
        print(f"\nGrouping {period_name} data:")
        print(f"Input rows: {len(rows)}")
        
        employees = {}
        processed_count = 0
        
        for row in rows:
            processed_count += 1
            
            # Check if row is dict or tuple
            if isinstance(row, dict):
                emp_id = row['employee_id']
                emp_name = row['employee_name']
                section = row['section_name']
                label = row['item_label']
                value = row['item_value']
                numeric_val = row['numeric_value']
            else:
                # Assume tuple format
                emp_id, emp_name, section, label, value, numeric_val = row
            
            if processed_count <= 5:
                print(f"  Processing row {processed_count}: {emp_id} - {section}.{label}")
            
            if emp_id not in employees:
                employees[emp_id] = {
                    'employee_id': emp_id,
                    'employee_name': emp_name,
                    'sections': {}
                }
                if processed_count <= 5:
                    print(f"    Created new employee: {emp_id}")
            
            if section not in employees[emp_id]['sections']:
                employees[emp_id]['sections'][section] = {}
                if processed_count <= 5:
                    print(f"    Created new section: {section}")
            
            employees[emp_id]['sections'][section][label] = {
                'value': value,
                'numeric_value': numeric_val
            }
            
            if processed_count <= 5:
                print(f"    Added item: {label} = {value}")
        
        print(f"Final grouped employees: {len(employees)}")
        print(f"Employee IDs: {list(employees.keys())[:10]}...")  # Show first 10
        
        return list(employees.values())
    
    # Test grouping for current data
    if current_rows:
        current_grouped = debug_grouping(current_rows, "current")
        print(f"Current grouped result: {len(current_grouped)} employees")
    
    # Test grouping for previous data  
    if previous_rows:
        previous_grouped = debug_grouping(previous_rows, "previous")
        print(f"Previous grouped result: {len(previous_grouped)} employees")
    
    # Test the actual method
    print("\n🔍 TESTING ACTUAL _load_extracted_data METHOD:")
    print("-" * 50)
    
    try:
        actual_current = manager._load_extracted_data('current')
        actual_previous = manager._load_extracted_data('previous')
        
        print(f"Actual method current result: {len(actual_current)} employees")
        print(f"Actual method previous result: {len(actual_previous)} employees")
        
        if actual_current:
            sample = actual_current[0]
            print(f"Sample actual current: {sample}")
        
    except Exception as e:
        print(f"❌ Error in actual method: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    
    print("\n" + "=" * 60)
    print("🎯 DEBUG COMPLETE")

if __name__ == "__main__":
    debug_load_extracted_data()
