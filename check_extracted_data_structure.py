#!/usr/bin/env python3
"""
Check extracted data structure to understand loading issues
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_extracted_data_structure():
    """Check extracted data structure"""
    print("🔍 CHECKING EXTRACTED DATA STRUCTURE")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Get current session
        print("\n1. 📊 CURRENT SESSION:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.session_manager import get_current_session_id
            
            current_session = get_current_session_id()
            print(f"   Current session: {current_session}")
        except Exception as e:
            print(f"   ❌ Could not get current session: {e}")
            return
        
        # 2. Check extracted_data table structure
        print("\n2. 📊 EXTRACTED_DATA TABLE STRUCTURE:")
        
        cursor.execute("PRAGMA table_info(extracted_data)")
        columns = cursor.fetchall()
        
        print("   Columns:")
        for col in columns:
            print(f"     {col[1]} {col[2]}")
        
        # 3. Check data for current session
        print("\n3. 📊 DATA FOR CURRENT SESSION:")
        
        cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE session_id = ?", (current_session,))
        total_count = cursor.fetchone()[0]
        print(f"   Total records: {total_count}")
        
        if total_count > 0:
            # Check period types
            cursor.execute("SELECT period_type, COUNT(*) FROM extracted_data WHERE session_id = ? GROUP BY period_type", (current_session,))
            period_counts = cursor.fetchall()
            
            print("   Period breakdown:")
            for period_type, count in period_counts:
                print(f"     {period_type}: {count} records")
            
            # Check sample data
            cursor.execute("SELECT * FROM extracted_data WHERE session_id = ? LIMIT 3", (current_session,))
            samples = cursor.fetchall()
            
            print("   Sample records:")
            for i, sample in enumerate(samples):
                print(f"     Record {i+1}: {str(sample)[:100]}...")
        else:
            print("   ❌ No data for current session")
        
        # 4. Check what the _load_extracted_data method expects
        print("\n4. 🔍 CHECKING LOAD METHOD REQUIREMENTS:")
        
        try:
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Try to understand what's failing
            print("   Testing _load_extracted_data method...")
            
            # Check if the method exists and what it does
            if hasattr(manager, '_load_extracted_data'):
                print("   ✅ _load_extracted_data method exists")
                
                # Try loading current data
                try:
                    current_data = manager._load_extracted_data('current')
                    if current_data:
                        print(f"   ✅ Current data loaded: {len(current_data)} employees")
                    else:
                        print("   ❌ Current data returned None/empty")
                except Exception as e:
                    print(f"   ❌ Current data loading failed: {e}")
                
                # Try loading previous data
                try:
                    previous_data = manager._load_extracted_data('previous')
                    if previous_data:
                        print(f"   ✅ Previous data loaded: {len(previous_data)} employees")
                    else:
                        print("   ❌ Previous data returned None/empty")
                except Exception as e:
                    print(f"   ❌ Previous data loading failed: {e}")
            else:
                print("   ❌ _load_extracted_data method not found")
        
        except Exception as e:
            print(f"   ❌ Method testing failed: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. Check if we need to run comparison directly
        print("\n5. 🔄 ATTEMPTING DIRECT COMPARISON:")
        
        if total_count > 0:
            try:
                # Get current and previous period data directly
                cursor.execute("""
                    SELECT employee_id, employee_name, section_name, item_label, item_value
                    FROM extracted_data 
                    WHERE session_id = ? AND period_type = 'current'
                    LIMIT 10
                """, (current_session,))
                
                current_samples = cursor.fetchall()
                
                cursor.execute("""
                    SELECT employee_id, employee_name, section_name, item_label, item_value
                    FROM extracted_data 
                    WHERE session_id = ? AND period_type = 'previous'
                    LIMIT 10
                """, (current_session,))
                
                previous_samples = cursor.fetchall()
                
                print(f"   Current period samples: {len(current_samples)}")
                print(f"   Previous period samples: {len(previous_samples)}")
                
                if current_samples and previous_samples:
                    print("   ✅ Both periods have data - comparison should be possible")
                    
                    # Try to run comparison manually
                    print("   🔄 Attempting manual comparison...")
                    
                    # Simple comparison logic
                    cursor.execute("""
                        SELECT 
                            c.employee_id,
                            c.employee_name,
                            c.section_name,
                            c.item_label,
                            p.item_value as previous_value,
                            c.item_value as current_value,
                            CASE 
                                WHEN p.item_value IS NULL THEN 'NEW'
                                WHEN c.item_value != p.item_value THEN 'CHANGED'
                                ELSE 'UNCHANGED'
                            END as change_type
                        FROM extracted_data c
                        LEFT JOIN extracted_data p ON 
                            c.employee_id = p.employee_id AND 
                            c.section_name = p.section_name AND 
                            c.item_label = p.item_label AND
                            p.session_id = ? AND p.period_type = 'previous'
                        WHERE c.session_id = ? AND c.period_type = 'current'
                        AND (p.item_value IS NULL OR c.item_value != p.item_value)
                        LIMIT 10
                    """, (current_session, current_session))
                    
                    changes = cursor.fetchall()
                    
                    print(f"   Found {len(changes)} sample changes:")
                    for change in changes:
                        print(f"     {change[0]} - {change[3]}: {change[4]} → {change[5]} ({change[6]})")
                    
                    if changes:
                        print("   ✅ Manual comparison working - data is good!")
                    else:
                        print("   ⚠️ No changes found in sample")
                else:
                    print("   ❌ Missing data for one or both periods")
            
            except Exception as e:
                print(f"   ❌ Direct comparison failed: {e}")
        
        # 6. Final recommendation
        print("\n6. 💡 RECOMMENDATION:")
        
        if total_count > 0:
            print("   ✅ Data exists for current session")
            print("   🔧 Issue: _load_extracted_data method may have compatibility issues")
            print("   💡 Solution: Use direct database queries for comparison")
            print("   🎯 Next: Implement direct comparison without _load_extracted_data")
        else:
            print("   ❌ No data for current session")
            print("   🔧 Issue: Session still pointing to wrong data")
            print("   💡 Solution: Check session management again")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during check: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_extracted_data_structure()
