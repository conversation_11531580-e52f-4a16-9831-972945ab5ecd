#!/usr/bin/env python3
"""
Debug database persistence issues
"""

import sys
import os
import sqlite3

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def debug_database_persistence():
    """Debug database persistence"""
    print("🔍 DEBUGGING DATABASE PERSISTENCE")
    print("=" * 60)
    
    current_session = "audit_session_1750779866_4cb1949e_5870"
    print(f"🎯 Testing session: {current_session}")
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return
        
        print(f"📁 Database path: {db_path}")
        
        # 1. Check database file size and modification time
        print("\n1. 📊 DATABASE FILE INFO:")
        file_stats = os.stat(db_path)
        print(f"   File size: {file_stats.st_size} bytes")
        print(f"   Last modified: {file_stats.st_mtime}")
        
        # 2. Check all tables in database
        print("\n2. 📋 DATABASE TABLES:")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   {table_name}: {count} records")
        
        # 3. Check if session exists
        print(f"\n3. 📊 SESSION CHECK FOR {current_session}:")
        cursor.execute("SELECT session_id, status FROM audit_sessions WHERE session_id = ?", (current_session,))
        session_info = cursor.fetchone()
        
        if session_info:
            print(f"   ✅ Session exists: {session_info[0]} - {session_info[1]}")
        else:
            print(f"   ❌ Session {current_session} not found")
            
            # List all sessions
            cursor.execute("SELECT session_id, status, created_at FROM audit_sessions ORDER BY created_at DESC")
            all_sessions = cursor.fetchall()
            print("   All sessions:")
            for session in all_sessions:
                print(f"     {session[0]} - {session[1]} - {session[2]}")
        
        # 4. Test storing and retrieving comparison results step by step
        print("\n4. 🔄 TESTING STEP-BY-STEP STORAGE:")
        
        try:
            sys.path.append(os.path.dirname(__file__))
            from core.phased_process_manager import PhasedProcessManager
            
            manager = PhasedProcessManager()
            manager.session_id = current_session
            
            # Check extracted data first
            print("   Checking extracted data...")
            current_data = manager._load_extracted_data('current')
            previous_data = manager._load_extracted_data('previous')
            
            if current_data and previous_data:
                print(f"   ✅ Loaded {len(current_data)} current and {len(previous_data)} previous employees")
                
                # Run comparison
                print("   Running comparison...")
                comparison_results = manager._compare_payroll_data(current_data, previous_data)
                print(f"   ✅ Generated {len(comparison_results)} comparison results")
                
                # Check database before storing
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                before_count = cursor.fetchone()[0]
                print(f"   Before storing: {before_count} results in database")
                
                # Store results with explicit commit
                print("   Storing comparison results...")
                
                # Test direct database insertion
                print("   Testing direct database insertion...")
                
                for i, result in enumerate(comparison_results[:5]):  # Test with first 5 results
                    try:
                        cursor.execute('''
                            INSERT INTO comparison_results 
                            (session_id, employee_id, employee_name, section_name, item_label,
                             previous_value, current_value, change_type, priority,
                             numeric_difference, percentage_change)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            current_session,
                            result['employee_id'],
                            result['employee_name'],
                            result['section_name'],
                            result['item_label'],
                            result['previous_value'],
                            result['current_value'],
                            result['change_type'],
                            result['priority'],
                            result.get('numeric_difference'),
                            result.get('percentage_change')
                        ))
                        
                        # Commit after each insert
                        conn.commit()
                        
                        # Check if it was inserted
                        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                        current_count = cursor.fetchone()[0]
                        print(f"     Inserted result {i+1}: {current_count} total results")
                        
                    except Exception as e:
                        print(f"     ❌ Failed to insert result {i+1}: {e}")
                
                # Final check
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE session_id = ?", (current_session,))
                final_count = cursor.fetchone()[0]
                print(f"   ✅ Final count: {final_count} results stored")
                
                if final_count > 0:
                    # Test loading them back
                    print("   Testing loading results back...")
                    all_changes = manager._load_all_comparison_results()
                    print(f"   ✅ Loaded back: {len(all_changes)} results")
                    
                    if all_changes:
                        print("   ✅ Database persistence working!")
                        
                        # Now test PRE-REPORTING
                        print("   Testing PRE-REPORTING phase...")
                        options = {
                            'report_name': 'Persistence Test Report',
                            'report_designation': 'Debug Test'
                        }
                        
                        result = manager._phase_pre_reporting(options)
                        
                        if result:
                            print("   ✅ PRE-REPORTING phase completed successfully!")
                            
                            # Check pre-reporting results
                            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (current_session,))
                            pre_reporting_count = cursor.fetchone()[0]
                            print(f"   ✅ Pre-reporting results: {pre_reporting_count}")
                        else:
                            print("   ❌ PRE-REPORTING phase failed")
                    else:
                        print("   ❌ Could not load results back")
                else:
                    print("   ❌ No results were persisted")
            else:
                print("   ❌ No extracted data found")
            
        except Exception as e:
            print(f"   ❌ Step-by-step test failed: {e}")
            import traceback
            traceback.print_exc()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_database_persistence()
